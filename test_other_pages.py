import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('other_pages_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OtherPagesTester:
    def __init__(self):
        """
        Testeur d'autres pages du site data-asso.fr
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def count_associations_on_page(self):
        """
        Compte les associations sur la page actuelle
        """
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Différentes méthodes de comptage
            methods = {
                'association_links': len([link for link in soup.find_all('a', href=True) if '/association/' in link['href']]),
                'rna_mentions': len(re.findall(r'RNA[:\s]*([A-Z0-9]+)', soup.get_text(), re.IGNORECASE)),
                'siren_mentions': len(re.findall(r'SIREN[:\s]*([0-9]+)', soup.get_text(), re.IGNORECASE))
            }
            
            return max(methods.values()), methods
            
        except Exception as e:
            logging.error(f"Erreur comptage: {e}")
            return 0, {}
    
    def test_page_with_scroll(self, url, page_name):
        """
        Teste une page avec scroll
        """
        logging.info(f"🌐 Test de {page_name}: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            # État initial
            initial_count, initial_methods = self.count_associations_on_page()
            initial_height = self.driver.execute_script("return document.body.scrollHeight")
            
            logging.info(f"   📊 État initial: {initial_count} associations, {initial_height}px")
            
            # Test de scroll
            max_associations = initial_count
            
            for i in range(10):  # 10 tentatives de scroll
                # Scroll vers le bas
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)
                
                # Vérifier les changements
                new_count, new_methods = self.count_associations_on_page()
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                
                if new_count > max_associations:
                    max_associations = new_count
                    logging.info(f"   🎯 NOUVELLES ASSOCIATIONS! {initial_count} -> {new_count}")
                
                if new_height > initial_height:
                    logging.info(f"   📈 Page agrandie: {initial_height} -> {new_height}px")
                    initial_height = new_height
                
                # Si pas de changement, arrêter
                if new_count == initial_count and new_height == initial_height:
                    break
            
            result = {
                'url': url,
                'page_name': page_name,
                'initial_associations': initial_count,
                'max_associations': max_associations,
                'scroll_infinite_detected': max_associations > initial_count,
                'final_height': new_height,
                'page_title': self.driver.title
            }
            
            logging.info(f"   📊 Résultat: {initial_count} -> {max_associations} associations")
            
            return result
            
        except Exception as e:
            logging.error(f"   ❌ Erreur {page_name}: {e}")
            return {
                'url': url,
                'page_name': page_name,
                'error': str(e)
            }
    
    def test_multiple_pages(self):
        """
        Teste plusieurs pages du site
        """
        logging.info("🔍 TEST DE PLUSIEURS PAGES DU SITE")
        
        pages_to_test = [
            ("https://www.data-asso.fr/annuaire", "Annuaire principal"),
            ("https://www.data-asso.fr/", "Page d'accueil"),
            ("https://www.data-asso.fr/search", "Page de recherche"),
            ("https://www.data-asso.fr/annuaire?q=sport", "Recherche 'sport'"),
            ("https://www.data-asso.fr/annuaire?q=culture", "Recherche 'culture'"),
            ("https://www.data-asso.fr/annuaire?q=", "Recherche vide"),
            ("https://www.data-asso.fr/annuaire?theme=sport", "Filtre thème sport"),
            ("https://www.data-asso.fr/annuaire?departement=75", "Département 75"),
            ("https://www.data-asso.fr/annuaire?ville=paris", "Ville Paris"),
        ]
        
        results = []
        
        for url, page_name in pages_to_test:
            result = self.test_page_with_scroll(url, page_name)
            results.append(result)
            time.sleep(2)  # Pause entre les tests
        
        # Analyser les résultats
        successful_pages = [r for r in results if r.get('scroll_infinite_detected')]
        
        logging.info(f"\n📊 RÉSULTATS GLOBAUX:")
        logging.info(f"   📄 Pages testées: {len(results)}")
        logging.info(f"   ✅ Pages avec scroll infini: {len(successful_pages)}")
        
        if successful_pages:
            logging.info(f"   🎯 PAGES AVEC SCROLL INFINI TROUVÉES:")
            for page in successful_pages:
                logging.info(f"      ✅ {page['page_name']}: {page['initial_associations']} -> {page['max_associations']}")
        else:
            logging.info(f"   ⚠️ Aucune page avec scroll infini détectée")
        
        return results

def main():
    """
    Test principal des autres pages
    """
    print("🔍 TEST D'AUTRES PAGES DU SITE DATA-ASSO")
    print("=" * 60)
    print("Ce test va vérifier si le scroll infini fonctionne")
    print("sur d'autres pages du site data-asso.fr")
    print()
    
    tester = OtherPagesTester()
    
    try:
        tester.start_driver()
        
        # Tester plusieurs pages
        results = tester.test_multiple_pages()
        
        # Sauvegarder les résultats
        with open('other_pages_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # Afficher le résumé
        successful_pages = [r for r in results if r.get('scroll_infinite_detected')]
        
        print(f"\n📊 RÉSULTATS FINAUX:")
        print(f"   📄 Pages testées: {len(results)}")
        print(f"   ✅ Pages avec scroll infini: {len(successful_pages)}")
        
        if successful_pages:
            print(f"\n🎯 PAGES AVEC SCROLL INFINI:")
            for page in successful_pages:
                print(f"   ✅ {page['page_name']}")
                print(f"      URL: {page['url']}")
                print(f"      Associations: {page['initial_associations']} -> {page['max_associations']}")
        else:
            print(f"\n⚠️ Aucune page avec scroll infini détectée")
            print(f"💡 Le site semble vraiment limité à ~20 associations")
        
        print(f"\n💾 Résultats détaillés dans: other_pages_results.json")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
