{"site_accessible": true, "page_title": "Data-Asso", "associations_found": 5, "association_links": ["https://www.data-asso.fr/annuaire/association/W9R2000732?docFields=documentsDac,documentsRna", "https://www.data-asso.fr/annuaire/association/W741004400?docFields=documentsDac,documentsRna", "https://www.data-asso.fr/annuaire/association/W641006789?docFields=documentsDac,documentsRna", "https://www.data-asso.fr/annuaire/association/W262003525?docFields=documentsDac,documentsRna", "https://www.data-asso.fr/annuaire/association/W133013116?docFields=documentsDac,documentsRna"], "sample_html": "<html data-countly-useragent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"><head>\n<title>Data-Asso</title><meta charset=\"utf-8\" data-n-head=\"1\"/><meta content=\"width=device-width, initial-scale=1\" data-n-head=\"1\" name=\"viewport\"/><meta content=\"Carte Data-Asso\" data-hid=\"description\" data-n-head=\"1\" name=\"description\"/><link data-n-head=\"1\" href=\"/images/favicon.ico\" rel=\"icon\" type=\"image/x-icon\"/><script async=\"\" data-n-head=\"1\" src=\"https://cdn.jsdelivr.net/npm/countly-sdk-web@latest/lib/countly.min.js\"></script><link as=\"script\" href=\"/_nuxt/7ddf189.js\" rel=\"preload\"/><link as=\"script\" href=\"/_nuxt/6b69079.js\" rel=\"preload\"/><link as=\"script\" href=\"/_nuxt/8203be4.js\" rel=\"preload\"/><link as=\"script\" href=\"/_nuxt/b37248b.js\" rel=\"preload\"/>\n<style type=\"text/css\">.title[data-v-b166f0f6]{margin-top:15px;font-size:5em}.info[data-v-b166f0f6]{font-weight:300;color:#9aabb1;margin:0 0 30px}hr[data-v-b166f0f6]{background-color:#a0a0a0}.image img[data-v-b166f0f6]{width:200px;height:auto}.card[data-v-b166f0f6]{padding-bottom:15px}</style><style type=\"text/css\">#chargement[data-v-4ce4361d]{display:none}@media only screen and (min-width:320px) and (max-width:640px){#chargement[data-v-4ce4361d]{display:block;position:fixed;top:0;left:0;z-index:1000;background:#79c17b;min-height:100vh;width:100vw}#chargement .image[data-v-4ce4361d]{position:absolute;top:50%;left:25%;transform:translateY(-50%)}#chargement .image img[data-v-4ce4361d]{width:200px;height:auto;filter:invert(100%);-webkit-filter:invert(100%)}}</style><style type=\"text/css\">@charset \"UTF-8\";\n/*! Buefy v0.9.13 | MIT License | github.com/buefy/buefy */@-webkit-keyframes fadeOut{0%{opacity:1}to{opacity:0}}@-webkit-keyframes fadeOutDown{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}@-webkit-keyframes fadeOutUp{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d...", "rna_siren_patterns": {"rna_found": 21, "siren_found": 7, "rna_examples": ["Extraire", "W9R2000732", "W741004400"], "siren_examples": ["448267377", "833121726", "814638185"]}, "scroll_test": false, "association_page_test": {}, "coordonnees_tab_found": false, "extraction_fields": {"Adresse": true, "Téléphone": true, "Courriel": true, "Adresse de gestion": true, "Code postal": true}}