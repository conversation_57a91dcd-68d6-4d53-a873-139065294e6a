{"metadata": {"discovery_date": "2025-07-19 22:44:43", "methods_discovered": 4, "purpose": "Découverte d'autres méthodes pour obtenir TOUTES les associations françaises"}, "methods": [{"method": "official_government_sources", "sources_tested": 5, "accessible_sources": 2, "results": [{"name": "data.gouv.fr - RNA", "url": "https://www.data.gouv.fr/fr/datasets/repertoire-national-des-associations/", "status_code": 200, "accessible": true, "content_length": 185348, "description": "Répertoire National des Associations officiel", "data_indicators": ["télécharger", "download", "csv", "json", "api", "dataset", "donn<PERSON>"]}, {"name": "Journal Officiel", "url": "https://www.journal-officiel.gouv.fr/pages/associations-search/", "status_code": 404, "accessible": false, "content_length": 507795, "description": "Déclarations officielles d'associations"}, {"name": "API Sirene INSEE", "url": "https://api.insee.fr/catalogue/site/themes/wso2/subthemes/insee/pages/item-info.jag?name=<PERSON>ene&version=V3&provider=insee", "status_code": 500, "accessible": false, "content_length": 271, "description": "API officielle des entreprises et associations"}, {"name": "Légifrance", "url": "https://www.legifrance.gouv.fr/", "status_code": 403, "accessible": false, "content_length": 7180, "description": "Textes légaux et déclarations"}, {"name": "Service-Public.fr", "url": "https://www.service-public.fr/associations/", "status_code": 200, "accessible": true, "content_length": 51838, "description": "Informations officielles sur les associations", "data_indicators": ["télécharger", "download", "api", "donn<PERSON>"]}]}, {"method": "alternative_association_sites", "sites_tested": 6, "accessible_sites": 3, "results": [{"name": "HelloAsso", "url": "https://www.helloasso.com/", "status_code": 403, "accessible": false, "description": "Plateforme de financement associatif"}, {"name": "Associations.gouv.fr", "url": "https://www.associations.gouv.fr/", "status_code": 200, "accessible": true, "description": "Portail officiel des associations", "potential_association_links": 69, "search_forms": 1}, {"name": "Le Mouvement Associatif", "url": "https://lemouvementassociatif.org/", "status_code": 200, "accessible": true, "description": "Réseau d'associations", "potential_association_links": 8, "search_forms": 1}, {"name": "France Bénévolat", "url": "https://www.francebenevolat.org/", "status_code": 200, "accessible": true, "description": "Réseau du bénévolat", "potential_association_links": 8, "search_forms": 6}, {"name": "Recherche-Associations.fr", "url": "https://www.recherche-associations.fr/", "error": "HTTPSConnectionPool(host='www.recherche-associations.fr', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000002462BB2A210>: Failed to resolve 'www.recherche-associations.fr' ([Errno 11001] getaddrinfo failed)\"))", "accessible": false}, {"name": "Associations-France.org", "url": "https://www.associations-france.org/", "error": "HTTPSConnectionPool(host='www.associations-france.org', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000002462B840410>: Failed to resolve 'www.associations-france.org' ([Errno 11001] getaddrinfo failed)\"))", "accessible": false}]}, {"method": "web_scraping_aggregation", "targets_tested": 3, "results": [{"name": "Pages J<PERSON>nes - Associations", "url": "https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=association&ou=france", "accessible": true, "potential_associations": 20, "description": "Annuaire commercial"}, {"name": "Yelp - Associations", "url": "https://www.yelp.fr/search?find_desc=association&find_loc=France", "accessible": true, "potential_associations": 10, "description": "Plateforme d'avis"}, {"name": "Google Maps - Associations", "url": "https://www.google.com/maps/search/association+france", "accessible": true, "potential_associations": 11, "description": "Cartographie"}]}, {"method": "api_discovery_advanced", "apis_tested": 7, "results": [{"api_url": "https://entreprise.data.gouv.fr/api/sirene/v1/siret/", "methods_results": {"GET": {"error": "('Connection aborted.', <PERSON>R<PERSON>tError(10054, 'Une connexion existante a dû être fermée par l’hôte distant', None, 10054, None))"}, "POST": {"error": "('Connection aborted.', <PERSON>R<PERSON>tError(10054, 'Une connexion existante a dû être fermée par l’hôte distant', None, 10054, None))"}, "OPTIONS": {"error": "('Connection aborted.', <PERSON>R<PERSON>tError(10054, 'Une connexion existante a dû être fermée par l’hôte distant', None, 10054, None))"}}}, {"api_url": "https://entreprise.data.gouv.fr/api/sirene/v3/unites_legales/", "methods_results": {"GET": {"error": "('Connection aborted.', <PERSON>R<PERSON>tError(10054, 'Une connexion existante a dû être fermée par l’hôte distant', None, 10054, None))"}, "POST": {"error": "HTTPSConnectionPool(host='entreprise.data.gouv.fr', port=443): Max retries exceeded with url: /api/sirene/v3/unites_legales/ (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002462BED9ED0>: Failed to establish a new connection: [WinError 10061] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée'))"}, "OPTIONS": {"error": "HTTPSConnectionPool(host='entreprise.data.gouv.fr', port=443): Max retries exceeded with url: /api/sirene/v3/unites_legales/ (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002462BEDB2D0>: Failed to establish a new connection: [WinError 10061] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée'))"}}}, {"api_url": "https://api.insee.fr/entreprises/sirene/V3/siret/", "methods_results": {"GET": {"status_code": 404, "content_type": "application/xml; charset=UTF-8", "content_length": 232}, "POST": {"status_code": 404, "content_type": "application/xml; charset=UTF-8", "content_length": 232}, "OPTIONS": {"status_code": 404, "content_type": "application/xml; charset=UTF-8", "content_length": 232}}}, {"api_url": "https://api.insee.fr/entreprises/sirene/V3/unites_legales/", "methods_results": {"GET": {"status_code": 404, "content_type": "application/xml; charset=UTF-8", "content_length": 232}, "POST": {"status_code": 404, "content_type": "application/xml; charset=UTF-8", "content_length": 232}, "OPTIONS": {"status_code": 404, "content_type": "application/xml; charset=UTF-8", "content_length": 232}}}, {"api_url": "https://www.data-asso.fr/api/", "methods_results": {"GET": {"status_code": 400, "content_type": "text/html; charset=utf-8", "content_length": 143}, "POST": {"status_code": 400, "content_type": "text/html; charset=utf-8", "content_length": 144}, "OPTIONS": {"status_code": 204, "content_type": "", "content_length": 0}}}, {"api_url": "https://www.data-asso.fr/gw/", "methods_results": {"GET": {"status_code": 404, "content_type": "text/html; charset=utf-8", "content_length": 139}, "POST": {"status_code": 404, "content_type": "text/html; charset=utf-8", "content_length": 140}, "OPTIONS": {"status_code": 204, "content_type": "", "content_length": 0}}}, {"api_url": "https://api.journal-officiel.gouv.fr/", "methods_results": {"GET": {"error": "HTTPSConnectionPool(host='api.journal-officiel.gouv.fr', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000002462B486C50>: Failed to resolve 'api.journal-officiel.gouv.fr' ([Errno 11001] getaddrinfo failed)\"))"}, "POST": {"error": "HTTPSConnectionPool(host='api.journal-officiel.gouv.fr', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000002462BEF4A10>: Failed to resolve 'api.journal-officiel.gouv.fr' ([Errno 11001] getaddrinfo failed)\"))"}, "OPTIONS": {"error": "HTTPSConnectionPool(host='api.journal-officiel.gouv.fr', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000002462BEE3510>: Failed to resolve 'api.journal-officiel.gouv.fr' ([Errno 11001] getaddrinfo failed)\"))"}}}]}]}