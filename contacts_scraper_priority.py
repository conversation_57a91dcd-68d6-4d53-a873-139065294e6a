import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('contacts_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ContactsScraperPriority:
    def __init__(self):
        """
        Scraper spécialisé pour les CONTACTS (email + téléphone) des associations
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.associations_with_contacts = {}
        self.stats = {
            'total_associations': 0,
            'with_email': 0,
            'with_phone': 0,
            'with_both': 0,
            'by_source': {}
        }
        
        self.lock = threading.Lock()
        
        # Patterns pour extraire emails et téléphones
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_patterns = [
            re.compile(r'0[1-9](?:[0-9]{8})'),  # Format français standard
            re.compile(r'\+33[1-9](?:[0-9]{8})'),  # Format international
            re.compile(r'(?:0[1-9]|33[1-9])(?:[-.\s]?[0-9]{2}){4}'),  # Avec séparateurs
        ]
        
    def add_association_with_contacts(self, rna, nom, source, email=None, phone=None, **kwargs):
        """Ajoute une association SEULEMENT si elle a des contacts"""
        if not email and not phone:
            return False
        
        with self.lock:
            if rna not in self.associations_with_contacts:
                self.associations_with_contacts[rna] = {
                    'rna': rna,
                    'nom': nom,
                    'email': email or '',
                    'telephone': phone or '',
                    'source': source,
                    **kwargs
                }
                
                self.stats['total_associations'] += 1
                if email:
                    self.stats['with_email'] += 1
                if phone:
                    self.stats['with_phone'] += 1
                if email and phone:
                    self.stats['with_both'] += 1
                
                self.stats['by_source'][source] = self.stats['by_source'].get(source, 0) + 1
                return True
        return False
    
    def extract_contacts_from_text(self, text):
        """Extrait emails et téléphones d'un texte"""
        emails = self.email_pattern.findall(text)
        phones = []
        
        for pattern in self.phone_patterns:
            phones.extend(pattern.findall(text))
        
        # Nettoyer les téléphones
        cleaned_phones = []
        for phone in phones:
            cleaned = re.sub(r'[-.\s]', '', phone)
            if len(cleaned) >= 10:
                cleaned_phones.append(cleaned)
        
        return emails, cleaned_phones
    
    def source_1_helloasso_detailed(self):
        """SOURCE 1: HelloAsso - Extraction détaillée avec contacts"""
        logging.info("💰 SOURCE 1: HelloAsso - Extraction contacts")
        
        try:
            # URLs HelloAsso avec plus de détails
            urls = [
                "https://www.helloasso.com/associations",
                "https://www.helloasso.com/annuaire",
                "https://www.helloasso.com/directory",
            ]
            
            for url in urls:
                try:
                    response = self.session.get(url, timeout=15)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Chercher des liens vers des pages d'associations
                        association_links = []
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            if '/associations/' in href or '/asso/' in href:
                                if href.startswith('/'):
                                    href = 'https://www.helloasso.com' + href
                                association_links.append(href)
                        
                        logging.info(f"   🔗 {len(association_links)} liens d'associations trouvés")
                        
                        # Visiter chaque page d'association pour extraire les contacts
                        for i, assoc_url in enumerate(association_links[:50]):  # Limiter pour les tests
                            try:
                                self.extract_contacts_from_association_page(assoc_url, 'helloasso')
                                
                                if i % 10 == 0:
                                    logging.info(f"      📊 {i}/{len(association_links[:50])} pages traitées")
                                
                                time.sleep(1)  # Respecter le site
                                
                            except Exception as e:
                                logging.debug(f"      ❌ Erreur page {assoc_url}: {e}")
                
                except Exception as e:
                    logging.debug(f"   ❌ Erreur URL {url}: {e}")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur HelloAsso: {e}")
    
    def source_2_pages_jaunes_contacts(self):
        """SOURCE 2: Pages Jaunes - Focus sur les contacts"""
        logging.info("📞 SOURCE 2: Pages Jaunes - Extraction contacts")
        
        try:
            search_terms = ['association', 'club', 'federation']
            cities = ['paris', 'lyon', 'marseille', 'toulouse', 'nice', 'nantes', 'strasbourg', 'bordeaux']
            
            for term in search_terms:
                for city in cities:
                    try:
                        url = f"https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui={term}&ou={city}"
                        response = self.session.get(url, timeout=15)
                        
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            
                            # Chercher spécifiquement les éléments avec contacts
                            contact_elements = soup.find_all(class_=lambda x: x and any(
                                keyword in str(x).lower() for keyword in ['contact', 'tel', 'phone', 'mail', 'email']
                            ))
                            
                            for element in contact_elements:
                                text = element.get_text()
                                emails, phones = self.extract_contacts_from_text(text)
                                
                                if emails or phones:
                                    # Chercher le nom de l'association dans les éléments parents
                                    nom = self.find_association_name_nearby(element)
                                    rna = f"PJ{hash(nom) % 1000000000:09d}"  # RNA fictif
                                    
                                    self.add_association_with_contacts(
                                        rna=rna,
                                        nom=nom,
                                        source='pages-jaunes',
                                        email=emails[0] if emails else None,
                                        phone=phones[0] if phones else None,
                                        ville=city
                                    )
                            
                            logging.info(f"   🎯 '{term}' à '{city}': {len(contact_elements)} éléments avec contacts")
                        
                        time.sleep(2)  # Respecter le site
                        
                    except Exception as e:
                        logging.debug(f"   ❌ Erreur {term}/{city}: {e}")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur Pages Jaunes: {e}")
    
    def source_3_associations_gouv_contacts(self):
        """SOURCE 3: associations.gouv.fr - Focus contacts"""
        logging.info("🏛️ SOURCE 3: associations.gouv.fr - Extraction contacts")
        
        try:
            # Utiliser Selenium pour les pages dynamiques
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            
            try:
                driver.get("https://www.associations.gouv.fr")
                time.sleep(5)
                
                # Chercher des formulaires de recherche
                search_inputs = driver.find_elements(By.XPATH, "//input[@type='text' or @type='search']")
                
                for search_input in search_inputs:
                    try:
                        search_input.clear()
                        search_input.send_keys("association")
                        search_input.submit()
                        time.sleep(3)
                        
                        # Analyser les résultats
                        soup = BeautifulSoup(driver.page_source, 'html.parser')
                        
                        # Chercher des liens vers des pages détaillées
                        detail_links = []
                        for link in soup.find_all('a', href=True):
                            if any(keyword in link['href'] for keyword in ['detail', 'fiche', 'association']):
                                detail_links.append(link['href'])
                        
                        # Visiter les pages détaillées
                        for detail_url in detail_links[:20]:  # Limiter
                            try:
                                if detail_url.startswith('/'):
                                    detail_url = 'https://www.associations.gouv.fr' + detail_url
                                
                                driver.get(detail_url)
                                time.sleep(2)
                                
                                page_text = driver.page_source
                                emails, phones = self.extract_contacts_from_text(page_text)
                                
                                if emails or phones:
                                    # Extraire le nom de l'association
                                    title_elem = driver.find_element(By.TAG_NAME, "title")
                                    nom = title_elem.text if title_elem else "Association gouvernementale"
                                    
                                    rna_match = re.search(r'W[0-9]{9}[A-Z]?', page_text)
                                    rna = rna_match.group() if rna_match else f"AG{hash(nom) % 1000000000:09d}"
                                    
                                    self.add_association_with_contacts(
                                        rna=rna,
                                        nom=nom,
                                        source='associations.gouv.fr',
                                        email=emails[0] if emails else None,
                                        phone=phones[0] if phones else None,
                                        url=detail_url
                                    )
                            
                            except Exception as e:
                                logging.debug(f"      ❌ Erreur page détail: {e}")
                        
                        break  # Un seul formulaire de recherche
                        
                    except Exception as e:
                        logging.debug(f"   ❌ Erreur recherche: {e}")
            
            finally:
                driver.quit()
        
        except Exception as e:
            logging.error(f"   ❌ Erreur associations.gouv.fr: {e}")
    
    def source_4_data_asso_individual_pages(self):
        """SOURCE 4: data-asso.fr - Pages individuelles avec contacts"""
        logging.info("🔍 SOURCE 4: data-asso.fr - Pages individuelles")
        
        try:
            # Utiliser les RNAs du mega scraper précédent
            try:
                with open('mega_scraper_all_associations.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    associations = data.get('associations', [])
                    
                logging.info(f"   📊 {len(associations)} associations à vérifier")
                
                # Visiter les pages individuelles pour chercher des contacts
                for i, assoc in enumerate(associations[:500]):  # Limiter pour les tests
                    try:
                        rna = assoc.get('rna')
                        if rna:
                            url = f"https://www.data-asso.fr/association/{rna}"
                            response = self.session.get(url, timeout=10)
                            
                            if response.status_code == 200:
                                emails, phones = self.extract_contacts_from_text(response.text)
                                
                                if emails or phones:
                                    self.add_association_with_contacts(
                                        rna=rna,
                                        nom=assoc.get('nom', 'Association data-asso'),
                                        source='data-asso-individual',
                                        email=emails[0] if emails else None,
                                        phone=phones[0] if phones else None,
                                        url=url
                                    )
                            
                            if i % 50 == 0:
                                logging.info(f"      📊 {i}/500 pages vérifiées")
                            
                            time.sleep(0.5)  # Respecter le site
                    
                    except Exception as e:
                        logging.debug(f"      ❌ Erreur RNA {rna}: {e}")
            
            except FileNotFoundError:
                logging.warning("   ⚠️ Fichier mega_scraper non trouvé, passage à la méthode alternative")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur data-asso pages individuelles: {e}")
    
    def extract_contacts_from_association_page(self, url, source):
        """Extrait les contacts d'une page d'association spécifique"""
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                emails, phones = self.extract_contacts_from_text(response.text)
                
                if emails or phones:
                    # Extraire le nom depuis le titre de la page
                    soup = BeautifulSoup(response.content, 'html.parser')
                    title = soup.find('title')
                    nom = title.text if title else url.split('/')[-1]
                    
                    rna = f"{source.upper()}{hash(nom) % 1000000000:09d}"
                    
                    self.add_association_with_contacts(
                        rna=rna,
                        nom=nom,
                        source=source,
                        email=emails[0] if emails else None,
                        phone=phones[0] if phones else None,
                        url=url
                    )
                    
                    return True
        except Exception as e:
            logging.debug(f"Erreur extraction {url}: {e}")
        
        return False
    
    def find_association_name_nearby(self, element):
        """Trouve le nom de l'association près d'un élément de contact"""
        try:
            # Chercher dans les éléments parents et frères
            for parent in [element.parent, element.parent.parent if element.parent else None]:
                if parent:
                    text = parent.get_text(strip=True)
                    if len(text) > 5 and len(text) < 200:
                        return text
            
            return "Association trouvée"
        except:
            return "Association"
    
    def run_contacts_extraction(self):
        """Lance l'extraction de contacts en parallèle"""
        logging.info("📧 LANCEMENT EXTRACTION CONTACTS - PRIORITÉ EMAIL/TÉLÉPHONE")
        
        sources = [
            ("HelloAsso", self.source_1_helloasso_detailed),
            ("Pages Jaunes", self.source_2_pages_jaunes_contacts),
            ("associations.gouv.fr", self.source_3_associations_gouv_contacts),
            ("data-asso pages", self.source_4_data_asso_individual_pages),
        ]
        
        with ThreadPoolExecutor(max_workers=2) as executor:
            future_to_source = {executor.submit(source_func): source_name 
                              for source_name, source_func in sources}
            
            for future in as_completed(future_to_source):
                source_name = future_to_source[future]
                try:
                    future.result()
                    logging.info(f"✅ {source_name} terminé")
                except Exception as e:
                    logging.error(f"❌ {source_name} échoué: {e}")
        
        return self.stats
    
    def save_contacts_results(self):
        """Sauvegarde UNIQUEMENT les associations avec contacts"""
        associations_list = list(self.associations_with_contacts.values())
        
        if not associations_list:
            logging.warning("⚠️ Aucune association avec contacts trouvée")
            return
        
        # JSON des contacts
        contacts_results = {
            'metadata': {
                'total_associations_with_contacts': len(associations_list),
                'with_email_only': self.stats['with_email'] - self.stats['with_both'],
                'with_phone_only': self.stats['with_phone'] - self.stats['with_both'],
                'with_both_email_and_phone': self.stats['with_both'],
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'focus': 'CONTACTS_PRIORITY',
                'stats': self.stats
            },
            'associations': associations_list
        }
        
        with open('associations_contacts_priority.json', 'w', encoding='utf-8') as f:
            json.dump(contacts_results, f, ensure_ascii=False, indent=2)
        
        # CSV des contacts - PRIORITÉ EMAIL/TÉLÉPHONE
        with open('associations_contacts_priority.csv', 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['rna', 'nom', 'email', 'telephone', 'source', 'url', 'ville']
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            
            # Trier par priorité : email + téléphone d'abord, puis email seul, puis téléphone seul
            sorted_associations = sorted(associations_list, key=lambda x: (
                -(bool(x.get('email')) and bool(x.get('telephone'))),  # Both (priority 1)
                -bool(x.get('email')),  # Email only (priority 2)
                -bool(x.get('telephone'))  # Phone only (priority 3)
            ))
            
            for assoc in sorted_associations:
                writer.writerow(assoc)
        
        # CSV séparé pour les associations avec EMAIL
        email_associations = [a for a in associations_list if a.get('email')]
        if email_associations:
            with open('associations_emails_only.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'email', 'telephone', 'source', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()
                for assoc in email_associations:
                    writer.writerow(assoc)
        
        # CSV séparé pour les associations avec TÉLÉPHONE
        phone_associations = [a for a in associations_list if a.get('telephone')]
        if phone_associations:
            with open('associations_phones_only.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'telephone', 'email', 'source', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()
                for assoc in phone_associations:
                    writer.writerow(assoc)
        
        logging.info("💾 Résultats contacts sauvegardés:")
        logging.info("   📄 associations_contacts_priority.json")
        logging.info("   📊 associations_contacts_priority.csv (PRIORITÉ EMAIL/TÉLÉPHONE)")
        if email_associations:
            logging.info(f"   📧 associations_emails_only.csv ({len(email_associations)} avec emails)")
        if phone_associations:
            logging.info(f"   📞 associations_phones_only.csv ({len(phone_associations)} avec téléphones)")

def main():
    """
    Scraper principal - PRIORITÉ CONTACTS
    """
    print("📧 SCRAPER CONTACTS - PRIORITÉ EMAIL/TÉLÉPHONE")
    print("=" * 70)
    print("Ce scraper se concentre UNIQUEMENT sur les associations")
    print("qui ont des CONTACTS (email et/ou téléphone):")
    print()
    print("🎯 PRIORITÉ 1: Associations avec EMAIL + TÉLÉPHONE")
    print("🎯 PRIORITÉ 2: Associations avec EMAIL seulement")
    print("🎯 PRIORITÉ 3: Associations avec TÉLÉPHONE seulement")
    print()
    print("Sources spécialisées:")
    print("1. HelloAsso - Pages détaillées")
    print("2. Pages Jaunes - Focus contacts")
    print("3. associations.gouv.fr - Pages individuelles")
    print("4. data-asso.fr - Vérification contacts")
    print()
    
    response = input("Voulez-vous lancer l'extraction CONTACTS? (o/n): ").lower().strip()
    if response != 'o':
        print("Extraction annulée.")
        return
    
    scraper = ContactsScraperPriority()
    
    try:
        start_time = time.time()
        
        # Extraction contacts
        stats = scraper.run_contacts_extraction()
        
        # Sauvegarder
        scraper.save_contacts_results()
        
        # Afficher le résumé final
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📧 EXTRACTION CONTACTS TERMINÉE!")
        print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
        print(f"   🏢 Associations avec contacts: {stats['total_associations']}")
        print(f"   📧 Avec email: {stats['with_email']}")
        print(f"   📞 Avec téléphone: {stats['with_phone']}")
        print(f"   🎯 Avec les deux: {stats['with_both']}")
        
        print(f"\n📊 RÉSULTATS PAR SOURCE:")
        for source, count in stats['by_source'].items():
            print(f"   📊 {source}: {count} associations")
        
        if scraper.associations_with_contacts:
            print(f"\n📧 EXEMPLES D'ASSOCIATIONS AVEC CONTACTS:")
            for i, assoc in enumerate(list(scraper.associations_with_contacts.values())[:5]):
                print(f"   {i+1}. {assoc['nom'][:50]}...")
                if assoc.get('email'):
                    print(f"      📧 Email: {assoc['email']}")
                if assoc.get('telephone'):
                    print(f"      📞 Téléphone: {assoc['telephone']}")
                print(f"      🔗 Source: {assoc['source']}")
        
        print(f"\n💾 Fichiers contacts sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
