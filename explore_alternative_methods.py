import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('alternative_methods.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AlternativeMethodsExplorer:
    def __init__(self):
        """
        Explorateur de méthodes alternatives pour récupérer plus d'associations
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        
        # Session requests pour les tests API
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Referer': 'https://www.data-asso.fr/annuaire'
        })
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 15)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def explore_site_structure(self):
        """
        Explore la structure complète du site pour trouver d'autres pages
        """
        logging.info("🔍 EXPLORATION DE LA STRUCTURE DU SITE")
        
        # Pages à explorer
        pages_to_explore = [
            "https://www.data-asso.fr",
            "https://www.data-asso.fr/annuaire",
            "https://www.data-asso.fr/search",
            "https://www.data-asso.fr/associations",
            "https://www.data-asso.fr/liste",
            "https://www.data-asso.fr/export",
            "https://www.data-asso.fr/api",
            "https://www.data-asso.fr/data",
            "https://www.data-asso.fr/download",
            "https://www.data-asso.fr/csv",
            "https://www.data-asso.fr/json"
        ]
        
        structure_results = {}
        
        for page_url in pages_to_explore:
            try:
                logging.info(f"🌐 Exploration: {page_url}")
                
                self.driver.get(page_url)
                time.sleep(3)
                
                # Analyser la page
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # Chercher des liens vers des associations
                association_links = [link for link in soup.find_all('a', href=True) 
                                   if '/association/' in link['href']]
                
                # Chercher des boutons/liens de téléchargement
                download_elements = []
                for elem in soup.find_all(['a', 'button']):
                    text = elem.get_text(strip=True).lower()
                    if any(keyword in text for keyword in ['télécharger', 'download', 'export', 'csv', 'json', 'extraire']):
                        download_elements.append({
                            'tag': elem.name,
                            'text': elem.get_text(strip=True),
                            'href': elem.get('href', ''),
                            'onclick': elem.get('onclick', ''),
                            'class': elem.get('class', [])
                        })
                
                # Chercher des formulaires
                forms = []
                for form in soup.find_all('form'):
                    inputs = form.find_all(['input', 'select', 'textarea'])
                    forms.append({
                        'action': form.get('action', ''),
                        'method': form.get('method', 'GET'),
                        'inputs': [{'type': inp.get('type'), 'name': inp.get('name')} for inp in inputs]
                    })
                
                structure_results[page_url] = {
                    'status': 'success',
                    'title': self.driver.title,
                    'associations_count': len(association_links),
                    'download_elements': download_elements,
                    'forms': forms,
                    'page_size': len(soup.get_text())
                }
                
                logging.info(f"   ✅ {len(association_links)} associations, {len(download_elements)} éléments de téléchargement")
                
            except Exception as e:
                structure_results[page_url] = {
                    'status': 'error',
                    'error': str(e)
                }
                logging.error(f"   ❌ Erreur: {e}")
            
            time.sleep(1)
        
        return structure_results
    
    def test_api_endpoints_comprehensive(self):
        """
        Test complet des endpoints API possibles
        """
        logging.info("🔗 TEST COMPLET DES ENDPOINTS API")
        
        # Endpoints à tester
        api_endpoints = [
            # APIs découvertes précédemment
            "https://www.data-asso.fr/gw/api-server/search",
            "https://www.data-asso.fr/gw/api-server/referentiels",
            
            # Autres endpoints possibles
            "https://www.data-asso.fr/api/associations",
            "https://www.data-asso.fr/api/search",
            "https://www.data-asso.fr/api/annuaire",
            "https://www.data-asso.fr/api/export",
            "https://www.data-asso.fr/data/associations",
            "https://www.data-asso.fr/data/export",
            "https://www.data-asso.fr/export/csv",
            "https://www.data-asso.fr/export/json",
            
            # Endpoints avec paramètres
            "https://www.data-asso.fr/gw/api-server/search?q=",
            "https://www.data-asso.fr/gw/api-server/search?limit=1000",
            "https://www.data-asso.fr/gw/api-server/search?size=1000",
            "https://www.data-asso.fr/gw/api-server/search?all=true",
        ]
        
        api_results = {}
        
        for endpoint in api_endpoints:
            try:
                logging.info(f"🔗 Test API: {endpoint}")
                
                # Test GET
                response = self.session.get(endpoint, timeout=10)
                
                result = {
                    'method': 'GET',
                    'status_code': response.status_code,
                    'content_type': response.headers.get('content-type', ''),
                    'content_length': len(response.content),
                    'success': response.status_code == 200
                }
                
                if response.status_code == 200:
                    try:
                        # Essayer de parser en JSON
                        data = response.json()
                        result['is_json'] = True
                        result['json_keys'] = list(data.keys()) if isinstance(data, dict) else []
                        result['json_length'] = len(data) if isinstance(data, (list, dict)) else 0
                        
                        # Chercher des associations dans la réponse
                        associations_found = self.find_associations_in_json(data)
                        result['associations_found'] = len(associations_found)
                        
                        if associations_found:
                            logging.info(f"   🎯 {len(associations_found)} associations trouvées!")
                            result['sample_associations'] = associations_found[:3]
                        
                    except json.JSONDecodeError:
                        result['is_json'] = False
                        result['content_preview'] = response.text[:200]
                
                # Test POST si GET échoue
                if response.status_code != 200:
                    post_response = self.session.post(endpoint, json={}, timeout=10)
                    if post_response.status_code == 200:
                        result['post_success'] = True
                        result['post_status'] = post_response.status_code
                
                api_results[endpoint] = result
                logging.info(f"   📊 Status: {result['status_code']}, JSON: {result.get('is_json', False)}")
                
            except Exception as e:
                api_results[endpoint] = {
                    'error': str(e),
                    'success': False
                }
                logging.debug(f"   ❌ Erreur: {e}")
            
            time.sleep(0.5)
        
        return api_results
    
    def find_associations_in_json(self, data):
        """
        Cherche des associations dans une structure JSON
        """
        associations = []
        
        def search_recursive(obj, path=""):
            if isinstance(obj, dict):
                # Vérifier si c'est une association
                if self.looks_like_association(obj):
                    associations.append(obj)
                
                # Continuer la recherche
                for key, value in obj.items():
                    search_recursive(value, f"{path}.{key}")
                    
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_recursive(item, f"{path}[{i}]")
        
        search_recursive(data)
        return associations
    
    def looks_like_association(self, obj):
        """
        Détermine si un objet JSON ressemble à une association
        """
        if not isinstance(obj, dict):
            return False
        
        # Chercher des champs typiques
        association_indicators = [
            'rna', 'siren', 'nom', 'name', 'denomination',
            'adresse', 'address', 'telephone', 'email'
        ]
        
        found_indicators = 0
        for key in obj.keys():
            key_lower = key.lower()
            for indicator in association_indicators:
                if indicator in key_lower:
                    found_indicators += 1
                    break
        
        return found_indicators >= 2
    
    def test_extract_button_functionality(self):
        """
        Teste spécifiquement le bouton "Extraire la liste"
        """
        logging.info("🔘 TEST DU BOUTON 'EXTRAIRE LA LISTE'")
        
        try:
            # Aller sur la page d'annuaire
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Chercher le bouton "Extraire la liste"
            extract_button = None
            try:
                extract_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Extraire la liste')]")
                logging.info("   ✅ Bouton 'Extraire la liste' trouvé")
            except:
                logging.info("   ❌ Bouton 'Extraire la liste' non trouvé")
                return None
            
            if extract_button:
                # Capturer l'état avant le clic
                initial_url = self.driver.current_url
                initial_page_source = self.driver.page_source
                
                # Cliquer sur le bouton
                logging.info("   🔘 Clic sur 'Extraire la liste'...")
                extract_button.click()
                time.sleep(5)
                
                # Analyser ce qui s'est passé
                new_url = self.driver.current_url
                new_page_source = self.driver.page_source
                
                # Vérifier s'il y a eu un téléchargement ou une redirection
                result = {
                    'button_found': True,
                    'initial_url': initial_url,
                    'new_url': new_url,
                    'url_changed': initial_url != new_url,
                    'page_changed': initial_page_source != new_page_source,
                    'page_size_before': len(initial_page_source),
                    'page_size_after': len(new_page_source)
                }
                
                # Chercher des indices de téléchargement
                if 'download' in new_url.lower() or 'export' in new_url.lower():
                    result['download_detected'] = True
                    logging.info("   🎯 Téléchargement détecté!")
                
                # Chercher des nouvelles associations
                new_soup = BeautifulSoup(new_page_source, 'html.parser')
                new_associations = [link for link in new_soup.find_all('a', href=True) 
                                  if '/association/' in link['href']]
                result['associations_after'] = len(new_associations)
                
                logging.info(f"   📊 URL changée: {result['url_changed']}")
                logging.info(f"   📊 Page changée: {result['page_changed']}")
                logging.info(f"   📊 Associations après: {result['associations_after']}")
                
                return result
        
        except Exception as e:
            logging.error(f"   ❌ Erreur test bouton: {e}")
            return {'error': str(e)}
    
    def comprehensive_exploration(self):
        """
        Exploration complète de toutes les méthodes alternatives
        """
        logging.info("🚀 EXPLORATION COMPLÈTE DES MÉTHODES ALTERNATIVES")
        
        results = {
            'site_structure': None,
            'api_endpoints': None,
            'extract_button': None,
            'recommendations': []
        }
        
        try:
            # 1. Explorer la structure du site
            results['site_structure'] = self.explore_site_structure()
            
            # 2. Tester les endpoints API
            results['api_endpoints'] = self.test_api_endpoints_comprehensive()
            
            # 3. Tester le bouton d'extraction
            results['extract_button'] = self.test_extract_button_functionality()
            
            # 4. Analyser et faire des recommandations
            recommendations = []
            
            # Analyser les APIs
            successful_apis = [url for url, result in results['api_endpoints'].items() 
                             if result.get('success') and result.get('associations_found', 0) > 0]
            
            if successful_apis:
                recommendations.append(f"✅ APIs fonctionnelles trouvées: {len(successful_apis)}")
                for api in successful_apis:
                    assoc_count = results['api_endpoints'][api].get('associations_found', 0)
                    recommendations.append(f"   🔗 {api} -> {assoc_count} associations")
            
            # Analyser le bouton d'extraction
            if results['extract_button'] and results['extract_button'].get('download_detected'):
                recommendations.append("✅ Bouton d'extraction fonctionnel - téléchargement possible")
            
            # Analyser la structure du site
            download_pages = [url for url, result in results['site_structure'].items() 
                            if result.get('status') == 'success' and result.get('download_elements')]
            
            if download_pages:
                recommendations.append(f"✅ Pages avec téléchargement trouvées: {len(download_pages)}")
            
            if not successful_apis and not download_pages:
                recommendations.append("❌ Aucune méthode alternative trouvée")
                recommendations.append("💡 Le site semble limité à 20 associations maximum")
            
            results['recommendations'] = recommendations
            
            # Sauvegarder les résultats
            with open('alternative_methods_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logging.info("✅ EXPLORATION COMPLÈTE TERMINÉE")
            
        except Exception as e:
            logging.error(f"❌ Erreur exploration: {e}")
            results['error'] = str(e)
        
        return results

def main():
    """
    Exploration principale des méthodes alternatives
    """
    print("🔍 EXPLORATION DES MÉTHODES ALTERNATIVES")
    print("=" * 60)
    print("Recherche de moyens d'obtenir plus de 20 associations...")
    print()
    
    explorer = AlternativeMethodsExplorer()
    
    try:
        explorer.start_driver()
        
        # Exploration complète
        results = explorer.comprehensive_exploration()
        
        # Afficher les résultats
        print(f"\n📊 RÉSULTATS DE L'EXPLORATION:")
        
        if results.get('recommendations'):
            print(f"\n💡 RECOMMANDATIONS:")
            for rec in results['recommendations']:
                print(f"   {rec}")
        
        # APIs réussies
        if results.get('api_endpoints'):
            successful_apis = [url for url, result in results['api_endpoints'].items() 
                             if result.get('success') and result.get('associations_found', 0) > 0]
            if successful_apis:
                print(f"\n🔗 APIS PROMETTEUSES:")
                for api in successful_apis:
                    count = results['api_endpoints'][api].get('associations_found', 0)
                    print(f"   ✅ {api} -> {count} associations")
        
        print(f"\n💾 Résultats détaillés sauvegardés dans: alternative_methods_results.json")
        print(f"📄 Log détaillé dans: alternative_methods.log")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        explorer.close_driver()

if __name__ == "__main__":
    main()
