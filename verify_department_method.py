import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('verification.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DepartmentMethodVerifier:
    def __init__(self):
        """
        Vérificateur de la méthode par département
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 15)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def test_department_uniqueness(self):
        """
        Teste si les départements donnent vraiment des associations différentes
        """
        logging.info("🔍 TEST DE L'UNICITÉ DES DÉPARTEMENTS")
        
        # Tester 5 départements différents
        test_departments = ['01', '02', '03', '75', '69']
        all_associations = {}
        all_rnas = set()
        
        for dept in test_departments:
            logging.info(f"🏛️ Test département {dept}")
            
            url = f"https://www.data-asso.fr/annuaire?departement={dept}"
            self.driver.get(url)
            time.sleep(5)
            
            # Extraire les associations
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            dept_associations = []
            dept_rnas = set()
            
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    # Extraire le RNA depuis l'URL
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        dept_rnas.add(rna)
                        dept_associations.append({
                            'rna': rna,
                            'url': link['href'],
                            'text': link.get_text(strip=True)[:100]  # Premiers 100 caractères
                        })
            
            all_associations[dept] = dept_associations
            all_rnas.update(dept_rnas)
            
            logging.info(f"   📊 Département {dept}: {len(dept_associations)} associations")
            logging.info(f"   📋 RNAs: {list(dept_rnas)[:3]}...")  # Afficher les 3 premiers
        
        # Analyser les résultats
        total_found = sum(len(assocs) for assocs in all_associations.values())
        unique_rnas = len(all_rnas)
        
        logging.info(f"\n📊 RÉSULTATS DU TEST D'UNICITÉ:")
        logging.info(f"   🏛️ Départements testés: {len(test_departments)}")
        logging.info(f"   🏢 Total associations trouvées: {total_found}")
        logging.info(f"   🆔 RNAs uniques: {unique_rnas}")
        logging.info(f"   🔄 Doublons: {total_found - unique_rnas}")
        
        # Vérifier les doublons entre départements
        duplicates_found = []
        for dept1 in test_departments:
            for dept2 in test_departments:
                if dept1 < dept2:  # Éviter de comparer deux fois
                    rnas1 = {a['rna'] for a in all_associations[dept1]}
                    rnas2 = {a['rna'] for a in all_associations[dept2]}
                    common = rnas1.intersection(rnas2)
                    if common:
                        duplicates_found.append({
                            'dept1': dept1,
                            'dept2': dept2,
                            'common_rnas': list(common)
                        })
                        logging.info(f"   🔄 Doublons entre {dept1} et {dept2}: {list(common)}")
        
        return {
            'departments_tested': test_departments,
            'total_associations': total_found,
            'unique_rnas': unique_rnas,
            'duplicates_count': total_found - unique_rnas,
            'duplicates_between_departments': duplicates_found,
            'associations_by_department': all_associations
        }
    
    def test_department_coverage(self):
        """
        Teste la couverture des départements français
        """
        logging.info("🗺️ TEST DE LA COUVERTURE DES DÉPARTEMENTS")
        
        # Départements à tester
        test_departments = [
            # Métropole
            '01', '02', '03', '04', '05',  # Premiers
            '75', '69', '13', '59', '33',  # Grandes villes
            '94', '95',                    # Derniers métropole
            # DOM-TOM
            '971', '972', '973', '974'     # Guadeloupe, Martinique, Guyane, Réunion
        ]
        
        coverage_results = {}
        
        for dept in test_departments:
            try:
                logging.info(f"🏛️ Test couverture département {dept}")
                
                url = f"https://www.data-asso.fr/annuaire?departement={dept}"
                self.driver.get(url)
                time.sleep(3)
                
                # Vérifier si la page charge correctement
                page_title = self.driver.title
                current_url = self.driver.current_url
                
                # Compter les associations
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                association_links = [link for link in soup.find_all('a', href=True) 
                                   if '/association/' in link['href']]
                
                coverage_results[dept] = {
                    'status': 'success',
                    'associations_count': len(association_links),
                    'page_title': page_title,
                    'url_works': current_url == url,
                    'sample_associations': [link['href'] for link in association_links[:3]]
                }
                
                logging.info(f"   ✅ Département {dept}: {len(association_links)} associations")
                
            except Exception as e:
                coverage_results[dept] = {
                    'status': 'error',
                    'error': str(e)
                }
                logging.error(f"   ❌ Département {dept}: {e}")
            
            time.sleep(1)  # Pause respectueuse
        
        # Analyser les résultats
        successful_depts = [d for d, r in coverage_results.items() if r['status'] == 'success']
        failed_depts = [d for d, r in coverage_results.items() if r['status'] == 'error']
        total_associations = sum(r.get('associations_count', 0) for r in coverage_results.values() if r['status'] == 'success')
        
        logging.info(f"\n🗺️ RÉSULTATS DE LA COUVERTURE:")
        logging.info(f"   ✅ Départements fonctionnels: {len(successful_depts)}/{len(test_departments)}")
        logging.info(f"   ❌ Départements échoués: {len(failed_depts)}")
        logging.info(f"   🏢 Total associations trouvées: {total_associations}")
        
        if failed_depts:
            logging.info(f"   ⚠️ Départements échoués: {failed_depts}")
        
        return coverage_results
    
    def test_association_details_consistency(self):
        """
        Teste la cohérence des détails des associations
        """
        logging.info("🔍 TEST DE LA COHÉRENCE DES DÉTAILS")
        
        # Prendre quelques associations de différents départements
        test_urls = []
        
        # Récupérer des URLs d'associations depuis 2 départements
        for dept in ['01', '75']:
            url = f"https://www.data-asso.fr/annuaire?departement={dept}"
            self.driver.get(url)
            time.sleep(3)
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            links = [link['href'] for link in soup.find_all('a', href=True) 
                    if '/association/' in link['href']]
            
            # Prendre les 2 premières de chaque département
            for link in links[:2]:
                full_url = 'https://www.data-asso.fr' + link if link.startswith('/') else link
                test_urls.append(full_url)
        
        consistency_results = []
        
        for url in test_urls:
            try:
                logging.info(f"🔍 Test détails: {url}")
                
                self.driver.get(url)
                time.sleep(3)
                
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # Extraire les informations clés
                details = {
                    'url': url,
                    'title': self.driver.title,
                    'has_h1': bool(soup.find('h1')),
                    'has_rna': bool(re.search(r'RNA', soup.get_text(), re.IGNORECASE)),
                    'has_siren': bool(re.search(r'SIREN', soup.get_text(), re.IGNORECASE)),
                    'has_address': bool(re.search(r'Adresse', soup.get_text(), re.IGNORECASE)),
                    'has_phone': bool(re.search(r'Téléphone', soup.get_text(), re.IGNORECASE)),
                    'has_email': bool(re.search(r'Courriel|Email', soup.get_text(), re.IGNORECASE)),
                    'page_length': len(soup.get_text())
                }
                
                # Extraire RNA depuis l'URL
                rna_match = re.search(r'/association/([A-Z0-9]+)', url)
                if rna_match:
                    details['rna_from_url'] = rna_match.group(1)
                
                consistency_results.append(details)
                
                logging.info(f"   ✅ RNA: {details.get('rna_from_url', 'N/A')}")
                logging.info(f"   📋 Champs: RNA={details['has_rna']}, SIREN={details['has_siren']}, Adresse={details['has_address']}")
                
            except Exception as e:
                logging.error(f"   ❌ Erreur {url}: {e}")
        
        return consistency_results
    
    def comprehensive_verification(self):
        """
        Vérification complète de la méthode
        """
        logging.info("🚀 VÉRIFICATION COMPLÈTE DE LA MÉTHODE PAR DÉPARTEMENT")
        
        results = {
            'uniqueness_test': None,
            'coverage_test': None,
            'consistency_test': None,
            'recommendation': '',
            'estimated_total': 0
        }
        
        try:
            # 1. Test d'unicité
            results['uniqueness_test'] = self.test_department_uniqueness()
            
            # 2. Test de couverture
            results['coverage_test'] = self.test_department_coverage()
            
            # 3. Test de cohérence
            results['consistency_test'] = self.test_association_details_consistency()
            
            # 4. Analyse et recommandation
            uniqueness = results['uniqueness_test']
            coverage = results['coverage_test']
            
            # Calculer l'estimation totale
            successful_depts = [d for d, r in coverage.items() if r['status'] == 'success']
            avg_associations_per_dept = sum(r.get('associations_count', 0) for r in coverage.values() if r['status'] == 'success') / len(successful_depts) if successful_depts else 0
            
            # Estimation pour tous les départements français (101)
            results['estimated_total'] = int(avg_associations_per_dept * 101)
            
            # Recommandation
            duplicate_rate = (uniqueness['duplicates_count'] / uniqueness['total_associations']) * 100 if uniqueness['total_associations'] > 0 else 0
            success_rate = (len(successful_depts) / len(coverage)) * 100
            
            if duplicate_rate < 10 and success_rate > 90:
                results['recommendation'] = "✅ MÉTHODE RECOMMANDÉE - Faible taux de doublons et bonne couverture"
            elif duplicate_rate < 20 and success_rate > 80:
                results['recommendation'] = "⚠️ MÉTHODE ACCEPTABLE - Quelques doublons mais utilisable"
            else:
                results['recommendation'] = "❌ MÉTHODE À REVOIR - Trop de doublons ou couverture insuffisante"
            
            # Sauvegarder les résultats
            with open('verification_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logging.info("✅ VÉRIFICATION COMPLÈTE TERMINÉE")
            
        except Exception as e:
            logging.error(f"❌ Erreur vérification: {e}")
            results['error'] = str(e)
        
        return results

def main():
    """
    Vérification principale
    """
    print("🔍 VÉRIFICATION APPROFONDIE DE LA MÉTHODE PAR DÉPARTEMENT")
    print("=" * 70)
    print("Cette vérification va tester:")
    print("1. Si les départements donnent des associations différentes")
    print("2. Si tous les départements fonctionnent")
    print("3. Si les détails des associations sont cohérents")
    print()
    
    verifier = DepartmentMethodVerifier()
    
    try:
        verifier.start_driver()
        
        # Vérification complète
        results = verifier.comprehensive_verification()
        
        # Afficher les résultats
        print(f"\n📊 RÉSULTATS DE LA VÉRIFICATION:")
        
        if results.get('uniqueness_test'):
            uniqueness = results['uniqueness_test']
            print(f"   🔄 Test d'unicité:")
            print(f"      - Total associations: {uniqueness['total_associations']}")
            print(f"      - RNAs uniques: {uniqueness['unique_rnas']}")
            print(f"      - Doublons: {uniqueness['duplicates_count']}")
            
            if uniqueness['duplicates_between_departments']:
                print(f"      - Doublons entre départements: {len(uniqueness['duplicates_between_departments'])}")
        
        if results.get('coverage_test'):
            coverage = results['coverage_test']
            successful = sum(1 for r in coverage.values() if r['status'] == 'success')
            total = len(coverage)
            print(f"   🗺️ Test de couverture:")
            print(f"      - Départements fonctionnels: {successful}/{total}")
            print(f"      - Taux de succès: {(successful/total)*100:.1f}%")
        
        print(f"   📈 Estimation totale: {results.get('estimated_total', 0)} associations")
        print(f"   💡 Recommandation: {results.get('recommendation', 'N/A')}")
        
        print(f"\n💾 Résultats détaillés sauvegardés dans: verification_results.json")
        print(f"📄 Log détaillé dans: verification.log")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        verifier.close_driver()

if __name__ == "__main__":
    main()
