#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRAPER COMPLET ASSOCIATIONS FRANÇAISES
Un seul fichier pour tout faire :
1. Scraper 100k+ associations depuis data.gouv.fr
2. Chercher les contacts (emails + téléphones)
3. <PERSON><PERSON>er un fichier Excel final
4. Scraper data-asso.fr avec toutes les méthodes

Auteur: Assistant IA
Date: 2025
"""

import time
import json
import csv
import requests
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib.parse
import itertools
import string

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_complet.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ScraperCompletAssociations:
    def __init__(self):
        """Scraper complet pour associations françaises"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.all_associations = {}
        self.associations_with_contacts = {}
        self.lock = threading.Lock()
        
        # Patterns pour contacts
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_patterns = [
            re.compile(r'0[1-9](?:[0-9]{8})'),
            re.compile(r'\+33[1-9](?:[0-9]{8})'),
            re.compile(r'(?:0[1-9])(?:[-.\s]?[0-9]{2}){4}'),
        ]
        
        self.stats = {
            'total_associations': 0,
            'with_contacts': 0,
            'with_email': 0,
            'with_phone': 0,
            'sources': {}
        }
    
    def add_association(self, rna, nom, source, **kwargs):
        """Ajoute une association de manière thread-safe"""
        with self.lock:
            if rna not in self.all_associations:
                self.all_associations[rna] = {
                    'rna': rna,
                    'nom': nom,
                    'source': source,
                    **kwargs
                }
                self.stats['total_associations'] += 1
                self.stats['sources'][source] = self.stats['sources'].get(source, 0) + 1
                return True
        return False
    
    def extract_contacts_from_text(self, text):
        """Extrait emails et téléphones d'un texte"""
        emails = self.email_pattern.findall(text)
        phones = []
        
        for pattern in self.phone_patterns:
            phones.extend(pattern.findall(text))
        
        # Nettoyer
        emails = list(set(emails))
        phones = list(set([re.sub(r'[-.\s]', '', phone) for phone in phones if len(re.sub(r'[-.\s]', '', phone)) >= 10]))
        
        return emails, phones
    
    def scrape_data_gouv_official(self):
        """ÉTAPE 1: Scraper data.gouv.fr - Source officielle"""
        logging.info("🏛️ ÉTAPE 1: data.gouv.fr - Données officielles")
        
        try:
            # API de recherche data.gouv.fr
            search_url = "https://www.data.gouv.fr/api/1/datasets/"
            params = {'q': 'associations répertoire national RNA', 'page_size': 50}
            
            response = self.session.get(search_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            datasets = data.get('data', [])
            
            logging.info(f"   📊 {len(datasets)} datasets trouvés")
            
            for dataset in datasets:
                resources = dataset.get('resources', [])
                for resource in resources:
                    if resource.get('format', '').lower() in ['csv', 'json']:
                        url = resource.get('url')
                        if url:
                            logging.info(f"   📄 Téléchargement: {resource.get('title', 'Dataset')}")
                            self.download_and_process_dataset(url, 'data.gouv.fr')
                            break
            
            logging.info(f"   ✅ data.gouv.fr terminé: {self.stats['sources'].get('data.gouv.fr', 0)} associations")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur data.gouv.fr: {e}")
    
    def download_and_process_dataset(self, url, source):
        """Télécharge et traite un dataset"""
        try:
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '').lower()
            
            if 'json' in content_type:
                data = response.json()
                self.extract_associations_from_json(data, source)
            elif 'csv' in content_type or url.endswith('.csv'):
                lines_processed = 0
                for line in response.iter_lines(decode_unicode=True):
                    if lines_processed > 100000:  # Limiter
                        break
                    
                    rna_matches = re.findall(r'W[0-9]{9}[A-Z]?', line)
                    for rna in rna_matches:
                        parts = line.split(',')
                        nom = parts[1] if len(parts) > 1 else 'Association'
                        
                        self.add_association(
                            rna=rna,
                            nom=nom.strip('"'),
                            source=source
                        )
                    
                    lines_processed += 1
                    
                    if lines_processed % 10000 == 0:
                        logging.info(f"      📊 {lines_processed} lignes traitées...")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur téléchargement {url}: {e}")
    
    def extract_associations_from_json(self, data, source):
        """Extrait des associations depuis du JSON"""
        def search_recursive(obj):
            if isinstance(obj, dict):
                if self.looks_like_association(obj):
                    rna = obj.get('rna') or obj.get('id_rna') or obj.get('RNA')
                    nom = obj.get('nom') or obj.get('name') or obj.get('denomination') or 'Association JSON'
                    
                    if not rna:
                        rna = f"W{hash(nom) % 1000000000:09d}A"
                    
                    self.add_association(rna=rna, nom=nom, source=source, **obj)
                
                for value in obj.values():
                    search_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    search_recursive(item)
        
        try:
            search_recursive(data)
        except Exception as e:
            logging.debug(f"Erreur extraction JSON: {e}")
    
    def looks_like_association(self, obj):
        """Détermine si un objet ressemble à une association"""
        if not isinstance(obj, dict):
            return False
        
        indicators = ['rna', 'siren', 'nom', 'name', 'denomination', 'association', 'club']
        found = sum(1 for key in obj.keys() if any(ind in key.lower() for ind in indicators))
        return found >= 2
    
    def scrape_data_asso_complete(self):
        """ÉTAPE 2: Scraper data-asso.fr avec toutes les méthodes"""
        logging.info("🔍 ÉTAPE 2: data-asso.fr - Toutes les méthodes")
        
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            
            try:
                # Méthode 1: URLs directes avec paramètres
                self.data_asso_method_urls_directes(driver)
                
                # Méthode 2: Recherche combinée
                self.data_asso_method_recherche(driver)
                
                # Méthode 3: Formulaires
                self.data_asso_method_formulaires(driver)
                
            finally:
                driver.quit()
                
            logging.info(f"   ✅ data-asso.fr terminé: {self.stats['sources'].get('data-asso.fr', 0)} associations")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur data-asso.fr: {e}")
    
    def data_asso_method_urls_directes(self, driver):
        """URLs directes avec paramètres"""
        base_urls = ["https://www.data-asso.fr/annuaire"]
        params_combinations = [
            {}, {'page': 1}, {'page': 2}, {'limit': 100}, {'size': 500},
            {'all': 'true'}, {'export': 'csv'}, {'format': 'json'}
        ]
        
        for base_url in base_urls:
            for params in params_combinations:
                try:
                    if params:
                        url = f"{base_url}?" + urllib.parse.urlencode(params)
                    else:
                        url = base_url
                    
                    driver.get(url)
                    time.sleep(2)
                    
                    found = self.extract_associations_from_page(driver, 'data-asso.fr')
                    if found > 0:
                        logging.info(f"      🎯 {url}: {found} associations")
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur {url}: {e}")
    
    def data_asso_method_recherche(self, driver):
        """Recherche combinée"""
        search_terms = list(string.ascii_lowercase) + [str(i) for i in range(20)] + [
            'sport', 'culture', 'association', 'club', 'paris', 'lyon'
        ]
        
        search_params = ['q', 'search', 'query']
        
        for param in search_params:
            for term in search_terms[:50]:  # Limiter
                try:
                    url = f"https://www.data-asso.fr/annuaire?{param}={urllib.parse.quote(term)}"
                    driver.get(url)
                    time.sleep(1)
                    
                    found = self.extract_associations_from_page(driver, 'data-asso.fr')
                    if found > 0:
                        logging.info(f"      🎯 '{term}': {found} associations")
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur {term}: {e}")
    
    def data_asso_method_formulaires(self, driver):
        """Manipulation de formulaires"""
        try:
            driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            inputs = driver.find_elements(By.TAG_NAME, "input")
            test_values = ['association', 'sport', 'culture']
            
            for input_elem in inputs:
                try:
                    input_type = input_elem.get_attribute('type')
                    if input_type in ['text', 'search']:
                        for value in test_values:
                            try:
                                input_elem.clear()
                                input_elem.send_keys(value)
                                input_elem.send_keys(Keys.RETURN)
                                time.sleep(2)
                                
                                found = self.extract_associations_from_page(driver, 'data-asso.fr')
                                if found > 0:
                                    logging.info(f"      🎯 Input '{value}': {found} associations")
                                
                            except Exception as e:
                                logging.debug(f"        ❌ Erreur input {value}: {e}")
                except Exception as e:
                    logging.debug(f"      ❌ Erreur input: {e}")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur formulaires: {e}")
    
    def extract_associations_from_page(self, driver, source):
        """Extrait les associations de la page actuelle"""
        found_count = 0
        try:
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        nom = link.get_text(strip=True)
                        url = 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                        
                        if self.add_association(rna=rna, nom=nom, source=source, url=url):
                            found_count += 1
            
            return found_count
        except Exception as e:
            logging.debug(f"Erreur extraction: {e}")
            return 0
    
    def search_contacts_for_associations(self, max_associations=500):
        """ÉTAPE 3: Chercher les contacts des associations"""
        logging.info(f"📧 ÉTAPE 3: Recherche contacts pour {max_associations} associations")
        
        associations_list = list(self.all_associations.values())[:max_associations]
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_association = {
                executor.submit(self.search_contacts_for_one_association, assoc): assoc 
                for assoc in associations_list
            }
            
            for i, future in enumerate(as_completed(future_to_association)):
                try:
                    result = future.result()
                    
                    if result:
                        with self.lock:
                            self.associations_with_contacts[result['rna']] = result
                            self.stats['with_contacts'] += 1
                            if result.get('email'):
                                self.stats['with_email'] += 1
                            if result.get('telephone'):
                                self.stats['with_phone'] += 1
                        
                        logging.info(f"   🎯 Contact trouvé: {result['nom'][:40]}...")
                    
                    if (i + 1) % 100 == 0:
                        logging.info(f"   📊 {i+1}/{len(associations_list)} associations vérifiées")
                
                except Exception as e:
                    logging.debug(f"Erreur future: {e}")
        
        logging.info(f"   ✅ Contacts terminé: {self.stats['with_contacts']} contacts trouvés")
    
    def search_contacts_for_one_association(self, association):
        """Cherche les contacts d'une association"""
        try:
            rna = association.get('rna', '')
            nom = association.get('nom', '')
            
            # Méthode 1: Page data-asso.fr
            url = f"https://www.data-asso.fr/association/{rna}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                emails, phones = self.extract_contacts_from_text(response.text)
                
                if emails or phones:
                    return {
                        'rna': rna,
                        'nom': nom,
                        'email': emails[0] if emails else '',
                        'telephone': phones[0] if phones else '',
                        'source': association.get('source', ''),
                        'url': url
                    }
            
            return None
            
        except Exception as e:
            logging.debug(f"Erreur contacts {rna}: {e}")
            return None
    
    def create_excel_file(self):
        """ÉTAPE 4: Créer le fichier Excel final"""
        logging.info("📊 ÉTAPE 4: Création fichier Excel")
        
        try:
            # Préparer les données
            excel_data = []
            
            for rna, assoc in self.all_associations.items():
                contact_info = self.associations_with_contacts.get(rna, {})
                
                row = {
                    'RNA': rna,
                    'Nom_Association': assoc.get('nom', ''),
                    'Email': contact_info.get('email', ''),
                    'Telephone': contact_info.get('telephone', ''),
                    'Source': assoc.get('source', ''),
                    'URL': assoc.get('url', ''),
                    'Avec_Contact': 'OUI' if contact_info else 'NON'
                }
                
                excel_data.append(row)
            
            # Créer le DataFrame
            df = pd.DataFrame(excel_data)
            
            # Trier par priorité (contacts d'abord)
            df = df.sort_values(['Avec_Contact', 'Email', 'Telephone'], ascending=[False, False, False])
            
            # Créer le fichier Excel
            excel_filename = 'Associations_Francaises_Complet.xlsx'
            
            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                # Feuille principale
                df.to_excel(writer, sheet_name='Toutes_Associations', index=False)
                
                # Feuille contacts seulement
                df_contacts = df[df['Avec_Contact'] == 'OUI']
                if not df_contacts.empty:
                    df_contacts.to_excel(writer, sheet_name='Avec_Contacts', index=False)
                
                # Feuille statistiques
                stats_data = [
                    ['Statistique', 'Valeur'],
                    ['Total associations', len(df)],
                    ['Avec contacts', self.stats['with_contacts']],
                    ['Avec email', self.stats['with_email']],
                    ['Avec téléphone', self.stats['with_phone']],
                    [''],
                    ['Sources', 'Nombre'],
                ]
                
                for source, count in self.stats['sources'].items():
                    stats_data.append([source, count])
                
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='Statistiques', index=False, header=False)
            
            logging.info(f"   ✅ Fichier Excel créé: {excel_filename}")
            logging.info(f"   📊 {len(df)} associations total")
            logging.info(f"   📧 {self.stats['with_contacts']} avec contacts")
            
            return excel_filename
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Excel: {e}")
            return None
    
    def run_complete_scraping(self):
        """Lance le scraping complet"""
        logging.info("🚀 SCRAPING COMPLET ASSOCIATIONS FRANÇAISES")
        logging.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # ÉTAPE 1: Scraper data.gouv.fr
            self.scrape_data_gouv_official()
            
            # ÉTAPE 2: Scraper data-asso.fr
            self.scrape_data_asso_complete()
            
            # ÉTAPE 3: Chercher les contacts
            self.search_contacts_for_associations(max_associations=1000)
            
            # ÉTAPE 4: Créer Excel
            excel_file = self.create_excel_file()
            
            # Résumé final
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n🎉 SCRAPING COMPLET TERMINÉ!")
            print(f"   ⏱️ Durée totale: {duration/60:.1f} minutes")
            print(f"   📊 Total associations: {self.stats['total_associations']}")
            print(f"   📧 Avec contacts: {self.stats['with_contacts']}")
            print(f"   📧 Avec email: {self.stats['with_email']}")
            print(f"   📞 Avec téléphone: {self.stats['with_phone']}")
            
            print(f"\n📊 SOURCES:")
            for source, count in self.stats['sources'].items():
                print(f"   📊 {source}: {count} associations")
            
            if excel_file:
                print(f"\n📁 Fichier Excel: {excel_file}")
            
            print(f"\n🎯 MISSION ACCOMPLIE!")
            
        except Exception as e:
            logging.error(f"❌ Erreur scraping complet: {e}")

def main():
    """Fonction principale"""
    print("🚀 SCRAPER COMPLET ASSOCIATIONS FRANÇAISES")
    print("=" * 60)
    print("Ce script fait TOUT en un seul fichier:")
    print("1. 📊 Scrape 100k+ associations (data.gouv.fr)")
    print("2. 🔍 Scrape data-asso.fr (toutes méthodes)")
    print("3. 📧 Cherche les contacts (emails + téléphones)")
    print("4. 📊 Crée un fichier Excel final")
    print()
    print("⏱️ Durée estimée: 30-60 minutes")
    print()
    
    response = input("Voulez-vous lancer le scraping complet? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    # Installer les dépendances si nécessaire
    try:
        import pandas
        import openpyxl
    except ImportError:
        print("📦 Installation des dépendances...")
        import subprocess
        subprocess.check_call(['pip', 'install', 'pandas', 'openpyxl', 'selenium', 'beautifulsoup4', 'webdriver-manager'])
    
    scraper = ScraperCompletAssociations()
    scraper.run_complete_scraping()

if __name__ == "__main__":
    main()
