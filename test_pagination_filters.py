import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pagination_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class PaginationFilterTest:
    def __init__(self, headless=False):
        """
        Test de la pagination et des filtres sur data-asso.fr
        """
        self.options = Options()
        if headless:
            self.options.add_argument('--headless')
        
        # Options optimisées
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver Chrome"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 15)
            logging.info("✅ Driver Chrome démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def analyze_page_elements(self):
        """
        Analyse tous les éléments de la page pour trouver pagination/filtres
        """
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🔍 Analyse éléments: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            analysis = {
                'pagination_elements': [],
                'filter_elements': [],
                'buttons': [],
                'forms': [],
                'navigation_elements': [],
                'potential_triggers': []
            }
            
            # 1. Chercher des éléments de pagination
            pagination_keywords = ['page', 'next', 'prev', 'suivant', 'précédent', 'plus', 'charger']
            for keyword in pagination_keywords:
                elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
                for elem in elements:
                    if elem.parent:
                        analysis['pagination_elements'].append({
                            'text': elem.strip(),
                            'tag': elem.parent.name,
                            'class': elem.parent.get('class', []),
                            'id': elem.parent.get('id', '')
                        })
            
            # 2. Chercher des boutons
            buttons = soup.find_all(['button', 'input'], type=['button', 'submit'])
            for button in buttons:
                analysis['buttons'].append({
                    'tag': button.name,
                    'text': button.get_text(strip=True),
                    'type': button.get('type'),
                    'class': button.get('class', []),
                    'id': button.get('id', ''),
                    'onclick': button.get('onclick', '')
                })
            
            # 3. Chercher des liens avec des mots-clés
            links = soup.find_all('a', href=True)
            for link in links:
                text = link.get_text(strip=True).lower()
                href = link.get('href', '').lower()
                if any(keyword in text or keyword in href for keyword in pagination_keywords):
                    analysis['navigation_elements'].append({
                        'text': link.get_text(strip=True),
                        'href': link.get('href'),
                        'class': link.get('class', [])
                    })
            
            # 4. Chercher des formulaires (filtres)
            forms = soup.find_all('form')
            for form in forms:
                inputs = form.find_all(['input', 'select', 'textarea'])
                analysis['forms'].append({
                    'action': form.get('action', ''),
                    'method': form.get('method', ''),
                    'inputs_count': len(inputs),
                    'inputs': [{'type': inp.get('type'), 'name': inp.get('name')} for inp in inputs[:5]]
                })
            
            # 5. Chercher des éléments avec des classes suspectes
            suspicious_classes = ['pagination', 'pager', 'nav', 'filter', 'search', 'load', 'more']
            for class_name in suspicious_classes:
                elements = soup.find_all(class_=re.compile(class_name, re.IGNORECASE))
                for elem in elements:
                    analysis['potential_triggers'].append({
                        'tag': elem.name,
                        'class': elem.get('class', []),
                        'id': elem.get('id', ''),
                        'text': elem.get_text(strip=True)[:100]
                    })
            
            logging.info(f"📊 Analyse terminée:")
            logging.info(f"   📄 Éléments pagination: {len(analysis['pagination_elements'])}")
            logging.info(f"   🔘 Boutons: {len(analysis['buttons'])}")
            logging.info(f"   🔗 Liens navigation: {len(analysis['navigation_elements'])}")
            logging.info(f"   📝 Formulaires: {len(analysis['forms'])}")
            logging.info(f"   🎯 Éléments suspects: {len(analysis['potential_triggers'])}")
            
            return analysis
            
        except Exception as e:
            logging.error(f"❌ Erreur analyse: {e}")
            return None
    
    def test_url_patterns(self):
        """
        Test différents patterns d'URL pour trouver plus d'associations
        """
        base_url = "https://www.data-asso.fr/annuaire"
        url_patterns = [
            f"{base_url}?page=2",
            f"{base_url}?page=1",
            f"{base_url}?limit=100",
            f"{base_url}?size=100",
            f"{base_url}?per_page=100",
            f"{base_url}?offset=20",
            f"{base_url}?start=20",
            f"{base_url}/page/2",
            f"{base_url}?p=2"
        ]
        
        results = {}
        
        for url in url_patterns:
            logging.info(f"🌐 Test URL: {url}")
            
            try:
                self.driver.get(url)
                time.sleep(3)
                
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # Compter les associations
                association_links = []
                for link in soup.find_all('a', href=True):
                    if '/association/' in link['href']:
                        association_links.append(link['href'])
                
                unique_count = len(set(association_links))
                results[url] = {
                    'associations_count': unique_count,
                    'status_code': 'OK' if unique_count > 0 else 'NO_DATA',
                    'sample_links': list(set(association_links))[:3]
                }
                
                logging.info(f"   📊 {unique_count} associations trouvées")
                
                if unique_count > 20:
                    logging.info(f"   🎉 SUCCÈS! Plus de 20 associations trouvées!")
                
            except Exception as e:
                results[url] = {'error': str(e)}
                logging.error(f"   ❌ Erreur: {e}")
            
            time.sleep(1)  # Pause respectueuse
        
        return results
    
    def test_interactive_elements(self):
        """
        Test des éléments interactifs pour déclencher le chargement
        """
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🔘 Test éléments interactifs: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            results = []
            
            # Compter les associations initiales
            initial_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            initial_count = len([link for link in initial_soup.find_all('a', href=True) if '/association/' in link['href']])
            
            logging.info(f"📊 Associations initiales: {initial_count}")
            
            # 1. Chercher et cliquer sur tous les boutons visibles
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            for i, button in enumerate(buttons):
                try:
                    if button.is_displayed() and button.is_enabled():
                        button_text = button.text.strip()
                        logging.info(f"🔘 Test bouton {i+1}: '{button_text}'")
                        
                        button.click()
                        time.sleep(3)
                        
                        # Vérifier si de nouvelles associations sont apparues
                        new_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                        new_count = len([link for link in new_soup.find_all('a', href=True) if '/association/' in link['href']])
                        
                        if new_count > initial_count:
                            results.append({
                                'action': f'Click button: {button_text}',
                                'initial_count': initial_count,
                                'new_count': new_count,
                                'success': True
                            })
                            logging.info(f"   🎉 SUCCÈS! {new_count} associations (+{new_count - initial_count})")
                        else:
                            results.append({
                                'action': f'Click button: {button_text}',
                                'initial_count': initial_count,
                                'new_count': new_count,
                                'success': False
                            })
                        
                        initial_count = new_count  # Mettre à jour pour le prochain test
                        
                except Exception as e:
                    logging.warning(f"   ⚠️ Erreur bouton {i+1}: {e}")
            
            # 2. Chercher des liens "Voir plus", "Charger plus", etc.
            load_more_texts = ['voir plus', 'charger plus', 'plus', 'suivant', 'next', 'load more']
            for text in load_more_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{text}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            logging.info(f"🔗 Test lien: '{text}'")
                            element.click()
                            time.sleep(3)
                            
                            new_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                            new_count = len([link for link in new_soup.find_all('a', href=True) if '/association/' in link['href']])
                            
                            if new_count > initial_count:
                                results.append({
                                    'action': f'Click link: {text}',
                                    'initial_count': initial_count,
                                    'new_count': new_count,
                                    'success': True
                                })
                                logging.info(f"   🎉 SUCCÈS! {new_count} associations (+{new_count - initial_count})")
                                initial_count = new_count
                                break
                            
                except Exception as e:
                    logging.debug(f"Pas de lien '{text}': {e}")
            
            return results
            
        except Exception as e:
            logging.error(f"❌ Erreur test interactif: {e}")
            return []

def main():
    """
    Test principal de pagination et filtres
    """
    print("🧪 TEST PAGINATION ET FILTRES - DATA-ASSO")
    print("=" * 60)
    
    tester = PaginationFilterTest(headless=False)
    
    try:
        tester.start_driver()
        
        # 1. Analyser les éléments de la page
        print("\n1️⃣ ANALYSE DES ÉLÉMENTS")
        analysis = tester.analyze_page_elements()
        
        # 2. Tester différents patterns d'URL
        print("\n2️⃣ TEST DES PATTERNS D'URL")
        url_results = tester.test_url_patterns()
        
        # 3. Tester les éléments interactifs
        print("\n3️⃣ TEST DES ÉLÉMENTS INTERACTIFS")
        interactive_results = tester.test_interactive_elements()
        
        # Compiler les résultats
        final_results = {
            'page_analysis': analysis,
            'url_patterns': url_results,
            'interactive_tests': interactive_results
        }
        
        # Sauvegarder
        with open('pagination_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 RÉSULTATS:")
        
        # Chercher des succès
        max_associations = 20
        successful_methods = []
        
        for url, result in url_results.items():
            if isinstance(result, dict) and result.get('associations_count', 0) > max_associations:
                successful_methods.append(f"URL: {url} -> {result['associations_count']} associations")
                max_associations = result['associations_count']
        
        for test in interactive_results:
            if test.get('success') and test.get('new_count', 0) > 20:
                successful_methods.append(f"Action: {test['action']} -> {test['new_count']} associations")
        
        if successful_methods:
            print(f"   🎉 MÉTHODES RÉUSSIES:")
            for method in successful_methods:
                print(f"      ✅ {method}")
        else:
            print(f"   ⚠️ Aucune méthode pour obtenir plus de 20 associations")
            print(f"   📊 Maximum trouvé: {max_associations} associations")
        
        print(f"\n💾 Résultats détaillés sauvegardés dans: pagination_test_results.json")
        print(f"📄 Log détaillé dans: pagination_test.log")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
