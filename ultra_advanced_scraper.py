import time
import json
import csv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultra_advanced_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class UltraAdvancedScraper:
    def __init__(self):
        """
        Scraper ultra-avancé avec toutes les techniques possibles
        """
        self.options = Options()
        # Mode visible pour détecter les problèmes
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Désactiver les images pour aller plus vite
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        self.options.add_experimental_option("prefs", prefs)
        
        self.driver = None
        self.wait = None
        self.all_associations = {}  # Dict pour éviter doublons
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 30)
            logging.info("✅ Driver ultra-avancé démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def wait_for_page_load(self):
        """
        Attend que la page soit complètement chargée
        """
        try:
            # Attendre que le DOM soit prêt
            self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            
            # Attendre que jQuery soit chargé (si présent)
            try:
                self.wait.until(lambda driver: driver.execute_script("return typeof jQuery !== 'undefined' && jQuery.active == 0"))
            except:
                pass
            
            # Attendre un peu plus pour les requêtes AJAX
            time.sleep(2)
            
        except Exception as e:
            logging.debug(f"Erreur attente chargement: {e}")
    
    def extract_associations_advanced(self):
        """
        Extraction avancée des associations avec toutes les techniques
        """
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            current_associations = []
            
            # Méthode 1: Liens d'associations
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        
                        if rna not in self.all_associations:
                            association_info = {
                                'rna': rna,
                                'url': 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href'],
                                'nom': link.get_text(strip=True),
                                'extraction_method': 'link_parsing'
                            }
                            
                            current_associations.append(association_info)
                            self.all_associations[rna] = association_info
            
            # Méthode 2: Recherche dans le texte brut
            text_content = soup.get_text()
            rna_matches = re.findall(r'RNA[:\s]*([A-Z0-9]{10})', text_content, re.IGNORECASE)
            
            for rna in rna_matches:
                if rna not in self.all_associations:
                    association_info = {
                        'rna': rna,
                        'url': f'https://www.data-asso.fr/association/{rna}',
                        'nom': 'Trouvé par regex',
                        'extraction_method': 'text_regex'
                    }
                    
                    current_associations.append(association_info)
                    self.all_associations[rna] = association_info
            
            # Méthode 3: Recherche dans les attributs data-*
            for element in soup.find_all(attrs={"data-rna": True}):
                rna = element.get('data-rna')
                if rna and rna not in self.all_associations:
                    association_info = {
                        'rna': rna,
                        'url': f'https://www.data-asso.fr/association/{rna}',
                        'nom': element.get_text(strip=True) or 'Trouvé par data-attribute',
                        'extraction_method': 'data_attribute'
                    }
                    
                    current_associations.append(association_info)
                    self.all_associations[rna] = association_info
            
            return current_associations
            
        except Exception as e:
            logging.error(f"Erreur extraction avancée: {e}")
            return []
    
    def ultra_scroll_technique(self, max_attempts=200, target_associations=1000):
        """
        Technique de scroll ultra-avancée avec toutes les méthodes
        """
        logging.info("🚀 TECHNIQUE DE SCROLL ULTRA-AVANCÉE")
        logging.info(f"🎯 Objectif: {target_associations} associations, max {max_attempts} tentatives")
        
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🌐 Accès à {url}")
        
        self.driver.get(url)
        self.wait_for_page_load()
        
        # État initial
        initial_associations = self.extract_associations_advanced()
        logging.info(f"📊 État initial: {len(self.all_associations)} associations")
        
        no_change_count = 0
        last_count = len(self.all_associations)
        
        for attempt in range(max_attempts):
            logging.info(f"\n🔄 TENTATIVE {attempt + 1}/{max_attempts}")
            
            # Technique 1: Scroll progressif avec attentes
            current_position = self.driver.execute_script("return window.pageYOffset")
            page_height = self.driver.execute_script("return document.body.scrollHeight")
            
            # Scroll de différentes manières
            scroll_methods = [
                lambda: self.driver.execute_script("window.scrollBy(0, 500);"),
                lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
                lambda: self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.PAGE_DOWN),
                lambda: self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.END),
                lambda: ActionChains(self.driver).scroll_by_amount(0, 800).perform()
            ]
            
            # Utiliser une méthode différente à chaque fois
            scroll_method = scroll_methods[attempt % len(scroll_methods)]
            scroll_method()
            
            # Attendre le chargement
            time.sleep(3)
            
            # Vérifier si la page a grandi
            new_page_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_page_height > page_height:
                logging.info(f"   📈 Page agrandie: {page_height}px -> {new_page_height}px")
                time.sleep(5)  # Attendre plus longtemps si la page grandit
            
            # Technique 2: Déclencher des événements JavaScript
            try:
                self.driver.execute_script("""
                    // Déclencher des événements de scroll
                    window.dispatchEvent(new Event('scroll'));
                    window.dispatchEvent(new Event('resize'));
                    
                    // Déclencher des événements sur le body
                    document.body.dispatchEvent(new Event('scroll'));
                    document.body.dispatchEvent(new Event('mousewheel'));
                """)
            except:
                pass
            
            # Technique 3: Cliquer sur des éléments potentiels de chargement
            try:
                load_buttons = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Charger') or contains(text(), 'Plus') or contains(text(), 'Suivant')]")
                for button in load_buttons:
                    if button.is_displayed() and button.is_enabled():
                        logging.info("   🔘 Clic sur bouton de chargement")
                        button.click()
                        time.sleep(3)
                        break
            except:
                pass
            
            # Extraire les nouvelles associations
            new_associations = self.extract_associations_advanced()
            current_count = len(self.all_associations)
            
            logging.info(f"   📊 Associations: {last_count} -> {current_count}")
            
            if current_count > last_count:
                new_found = current_count - last_count
                logging.info(f"   🎯 {new_found} NOUVELLES ASSOCIATIONS TROUVÉES!")
                
                # Afficher quelques exemples
                recent_associations = list(self.all_associations.values())[-new_found:]
                for i, assoc in enumerate(recent_associations[:3]):
                    logging.info(f"      {i+1}. {assoc['rna']}: {assoc['nom'][:50]}...")
                
                no_change_count = 0
                last_count = current_count
                
                # Vérifier si on a atteint l'objectif
                if current_count >= target_associations:
                    logging.info(f"   🎉 OBJECTIF ATTEINT! {current_count} associations")
                    break
            else:
                no_change_count += 1
                logging.info(f"   ⚠️ Pas de nouvelles associations ({no_change_count}/10)")
            
            # Si pas de changement depuis 10 tentatives
            if no_change_count >= 10:
                logging.info("   ⏹️ Arrêt: Pas de nouvelles associations depuis 10 tentatives")
                
                # Dernière technique: Attendre très longtemps
                logging.info("   ⏳ Dernière tentative: Attente prolongée...")
                time.sleep(15)
                
                final_associations = self.extract_associations_advanced()
                final_count = len(self.all_associations)
                
                if final_count > current_count:
                    logging.info(f"   🎯 {final_count - current_count} associations supplémentaires après attente!")
                
                break
            
            # Pause entre les tentatives
            time.sleep(1)
        
        final_results = {
            'total_attempts': attempt + 1,
            'initial_count': len(initial_associations),
            'final_count': len(self.all_associations),
            'new_associations_found': len(self.all_associations) - len(initial_associations),
            'success': len(self.all_associations) > len(initial_associations),
            'all_associations': list(self.all_associations.values())
        }
        
        logging.info(f"\n🎉 RÉSULTATS ULTRA-AVANCÉS:")
        logging.info(f"   📊 Tentatives: {final_results['total_attempts']}")
        logging.info(f"   📊 Associations initiales: {final_results['initial_count']}")
        logging.info(f"   📊 Associations finales: {final_results['final_count']}")
        logging.info(f"   📊 Nouvelles associations: {final_results['new_associations_found']}")
        logging.info(f"   ✅ Succès: {'OUI' if final_results['success'] else 'NON'}")
        
        return final_results
    
    def save_ultra_results(self, results, filename_base='associations_ultra'):
        """
        Sauvegarde les résultats ultra-avancés
        """
        if not results.get('all_associations'):
            logging.warning("⚠️ Aucune association à sauvegarder")
            return
        
        associations = results['all_associations']
        
        # JSON complet
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_associations': len(associations),
                    'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'method': 'ultra_advanced_scraping',
                    'attempts': results['total_attempts'],
                    'success': results['success']
                },
                'associations': associations
            }, f, ensure_ascii=False, indent=2)
        
        # CSV pour Excel
        csv_file = f"{filename_base}.csv"
        fieldnames = ['rna', 'nom', 'url', 'extraction_method']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for assoc in associations:
                writer.writerow(assoc)
        
        logging.info(f"💾 Résultats ultra-avancés sauvegardés:")
        logging.info(f"   📄 {json_file}")
        logging.info(f"   📊 {csv_file}")

def main():
    """
    Scraper ultra-avancé principal
    """
    print("🚀 SCRAPER ULTRA-AVANCÉ DATA-ASSO")
    print("=" * 60)
    print("Ce scraper utilise TOUTES les techniques avancées:")
    print("- Scroll ultra-sophistiqué avec 5 méthodes différentes")
    print("- Extraction multi-méthodes (liens, regex, attributs)")
    print("- Déclenchement d'événements JavaScript")
    print("- Détection de boutons de chargement")
    print("- Attentes prolongées et adaptatives")
    print()
    
    target = input("Nombre d'associations cible (défaut: 1000): ").strip()
    try:
        target = int(target) if target else 1000
    except:
        target = 1000
    
    max_attempts = input("Nombre max de tentatives (défaut: 200): ").strip()
    try:
        max_attempts = int(max_attempts) if max_attempts else 200
    except:
        max_attempts = 200
    
    print(f"\n🎯 Objectif: {target} associations en max {max_attempts} tentatives")
    
    scraper = UltraAdvancedScraper()
    
    try:
        scraper.start_driver()
        
        # Scraping ultra-avancé
        results = scraper.ultra_scroll_technique(
            max_attempts=max_attempts,
            target_associations=target
        )
        
        # Sauvegarder
        scraper.save_ultra_results(results)
        
        # Afficher le résumé
        print(f"\n🎉 SCRAPING ULTRA-AVANCÉ TERMINÉ!")
        print(f"   📊 Associations trouvées: {results['final_count']}")
        print(f"   📈 Nouvelles associations: {results['new_associations_found']}")
        print(f"   🔄 Tentatives utilisées: {results['total_attempts']}")
        
        if results['success']:
            print(f"   ✅ SUCCÈS! Plus d'associations trouvées que prévu!")
            
            # Afficher quelques exemples
            if results.get('all_associations'):
                print(f"\n📋 EXEMPLES D'ASSOCIATIONS:")
                for i, assoc in enumerate(results['all_associations'][:10]):
                    print(f"   {i+1}. {assoc['rna']}: {assoc['nom'][:50]}...")
        else:
            print(f"   ⚠️ Pas de nouvelles associations détectées")
        
        print(f"\n💾 Résultats sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()
