import time
import json
import csv
import os
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class FinalDataAssoScraper:
    def __init__(self):
        """
        Scraper final utilisant le bouton "Extraire la liste"
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Configurer le dossier de téléchargement
        self.download_dir = os.path.join(os.getcwd(), "final_downloads")
        os.makedirs(self.download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        self.options.add_experimental_option("prefs", prefs)
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def extract_associations_via_button(self):
        """
        Extrait les associations en utilisant le bouton "Extraire la liste"
        """
        logging.info("🔘 EXTRACTION VIA BOUTON 'EXTRAIRE LA LISTE'")
        
        try:
            # Nettoyer le dossier de téléchargement
            self.clean_download_dir()
            
            # Aller sur la page d'annuaire
            url = "https://www.data-asso.fr/annuaire"
            logging.info(f"🌐 Accès à {url}")
            self.driver.get(url)
            time.sleep(10)
            
            # Chercher et cliquer sur le bouton
            extract_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Extraire la liste')]"))
            )
            
            logging.info("🔘 Clic sur 'Extraire la liste'...")
            extract_button.click()
            
            # Attendre le téléchargement
            downloaded_file = self.wait_for_download()
            
            if downloaded_file:
                logging.info(f"✅ Fichier téléchargé: {downloaded_file}")
                return downloaded_file
            else:
                logging.error("❌ Aucun fichier téléchargé")
                return None
                
        except Exception as e:
            logging.error(f"❌ Erreur extraction: {e}")
            return None
    
    def clean_download_dir(self):
        """Nettoie le dossier de téléchargement"""
        try:
            for file in os.listdir(self.download_dir):
                file_path = os.path.join(self.download_dir, file)
                os.remove(file_path)
            logging.info("🧹 Dossier de téléchargement nettoyé")
        except Exception as e:
            logging.debug(f"Erreur nettoyage: {e}")
    
    def wait_for_download(self, timeout=30):
        """Attend qu'un fichier soit téléchargé"""
        logging.info(f"⏳ Attente de téléchargement (max {timeout}s)...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            files = os.listdir(self.download_dir)
            
            # Chercher des fichiers CSV terminés
            csv_files = [f for f in files if f.endswith('.csv') and not f.endswith('.crdownload')]
            
            if csv_files:
                return os.path.join(self.download_dir, csv_files[0])
            
            time.sleep(1)
        
        logging.error("⏰ Timeout - Aucun téléchargement")
        return None
    
    def process_downloaded_csv(self, csv_file_path):
        """
        Traite le fichier CSV téléchargé et enrichit les données
        """
        logging.info(f"📊 Traitement du fichier: {csv_file_path}")
        
        try:
            # Lire le CSV avec pandas
            df = pd.read_csv(csv_file_path, sep=';', encoding='utf-8')
            
            logging.info(f"📋 {len(df)} associations dans le fichier")
            logging.info(f"📋 Colonnes: {list(df.columns)}")
            
            # Enrichir les données
            enriched_data = []
            
            for index, row in df.iterrows():
                # Extraire les données de base
                association = {
                    'nom': row.get('nom', ''),
                    'rna': row.get('id_rna', ''),
                    'siren': row.get('id_siren', ''),
                    'siret': row.get('id_siret_siege', ''),
                    'adresse': row.get('adresse', ''),
                    'theme': row.get('lib_theme1', ''),
                    'latitude': row.get('latitude', ''),
                    'longitude': row.get('longitude', ''),
                    'active': row.get('active', ''),
                    'source': 'bouton_extraction'
                }
                
                # Enrichir avec des données supplémentaires si possible
                if association['rna']:
                    # Essayer d'obtenir plus de détails depuis la page de l'association
                    details = self.get_association_details(association['rna'])
                    if details:
                        association.update(details)
                
                enriched_data.append(association)
                
                logging.info(f"   ✅ {index+1}/{len(df)}: {association['nom']}")
            
            return enriched_data
            
        except Exception as e:
            logging.error(f"❌ Erreur traitement CSV: {e}")
            return None
    
    def get_association_details(self, rna):
        """
        Récupère les détails supplémentaires d'une association
        """
        try:
            # URL de l'association
            association_url = f"https://www.data-asso.fr/association/{rna}"
            
            logging.debug(f"🔍 Détails pour {rna}")
            self.driver.get(association_url)
            time.sleep(2)
            
            # Extraire les détails supplémentaires
            details = {}
            
            # Essayer d'extraire téléphone et email
            page_text = self.driver.page_source
            
            # Recherche d'email
            import re
            email_match = re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', page_text)
            if email_match:
                details['email'] = email_match.group()
            
            # Recherche de téléphone
            phone_match = re.search(r'0[1-9](?:[0-9]{8})', page_text)
            if phone_match:
                details['telephone'] = phone_match.group()
            
            return details
            
        except Exception as e:
            logging.debug(f"Erreur détails {rna}: {e}")
            return {}
    
    def save_final_results(self, data, filename_base='associations_final'):
        """
        Sauvegarde les résultats finaux dans plusieurs formats
        """
        if not data:
            logging.warning("⚠️ Aucune donnée à sauvegarder")
            return
        
        # JSON complet
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_associations': len(data),
                    'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'method': 'bouton_extraction',
                    'source': 'data-asso.fr'
                },
                'associations': data
            }, f, ensure_ascii=False, indent=2)
        
        # CSV pour Excel
        csv_file = f"{filename_base}.csv"
        fieldnames = ['nom', 'rna', 'siren', 'siret', 'adresse', 'theme', 
                     'latitude', 'longitude', 'email', 'telephone', 'active', 'source']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in data:
                writer.writerow(item)
        
        # CSV des contacts uniquement
        contacts_data = [assoc for assoc in data if assoc.get('email') or assoc.get('telephone')]
        if contacts_data:
            contacts_file = f"{filename_base}_contacts.csv"
            with open(contacts_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for item in contacts_data:
                    writer.writerow(item)
            logging.info(f"📧 {len(contacts_data)} associations avec contacts sauvegardées")
        
        logging.info(f"💾 Fichiers sauvegardés:")
        logging.info(f"   📄 {json_file} - Données complètes JSON")
        logging.info(f"   📊 {csv_file} - Toutes les associations CSV")
        if contacts_data:
            logging.info(f"   📧 {contacts_file} - Associations avec contacts")
    
    def run_complete_extraction(self):
        """
        Lance l'extraction complète
        """
        logging.info("🚀 EXTRACTION COMPLÈTE - MÉTHODE BOUTON")
        
        start_time = time.time()
        
        try:
            # 1. Extraire via bouton
            csv_file = self.extract_associations_via_button()
            
            if not csv_file:
                logging.error("❌ Échec de l'extraction")
                return None
            
            # 2. Traiter le CSV
            associations = self.process_downloaded_csv(csv_file)
            
            if not associations:
                logging.error("❌ Échec du traitement")
                return None
            
            # 3. Sauvegarder
            self.save_final_results(associations)
            
            # 4. Statistiques
            end_time = time.time()
            duration = end_time - start_time
            
            emails_count = sum(1 for a in associations if a.get('email'))
            phones_count = sum(1 for a in associations if a.get('telephone'))
            
            logging.info(f"\n🎉 EXTRACTION TERMINÉE!")
            logging.info(f"   ⏱️ Durée: {duration:.1f} secondes")
            logging.info(f"   🏢 Associations: {len(associations)}")
            logging.info(f"   📧 Emails: {emails_count}")
            logging.info(f"   📞 Téléphones: {phones_count}")
            
            return associations
            
        except Exception as e:
            logging.error(f"❌ Erreur extraction complète: {e}")
            return None

def main():
    """
    Scraper principal final
    """
    print("🎯 SCRAPER FINAL DATA-ASSO - MÉTHODE BOUTON")
    print("=" * 60)
    print("Ce scraper utilise le bouton 'Extraire la liste' pour")
    print("télécharger directement le fichier CSV des associations.")
    print()
    
    scraper = FinalDataAssoScraper()
    
    try:
        scraper.start_driver()
        
        # Extraction complète
        results = scraper.run_complete_extraction()
        
        if results:
            print(f"\n🎉 SUCCÈS! {len(results)} associations extraites")
            
            # Afficher quelques exemples
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS:")
            for i, assoc in enumerate(results[:5]):
                print(f"\n{i+1}. {assoc.get('nom', 'N/A')}")
                print(f"   🏢 RNA: {assoc.get('rna', 'N/A')}")
                print(f"   🎯 Thème: {assoc.get('theme', 'N/A')}")
                print(f"   📍 Adresse: {assoc.get('adresse', 'N/A')}")
                print(f"   📧 Email: {assoc.get('email', 'N/A')}")
                print(f"   📞 Téléphone: {assoc.get('telephone', 'N/A')}")
            
            print(f"\n💾 Fichiers sauvegardés dans le répertoire courant")
            
        else:
            print("\n❌ Échec de l'extraction")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()
