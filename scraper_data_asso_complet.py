#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRAPER COMPLET POUR DATA-ASSO.FR
Contourne tous les blocages et extrait TOUTES les associations
Basé sur l'analyse approfondie de l'API et des mécanismes du site
"""

import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import random
import urllib.parse
from datetime import datetime, timedelta

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_data_asso_complet.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ScraperDataAssoComplet:
    def __init__(self):
        """Scraper complet pour data-asso.fr"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        })
        
        self.associations = {}
        self.lock = threading.Lock()
        self.api_url = "https://www.data-asso.fr/gw/api-server/search"
        self.referentiels_url = "https://www.data-asso.fr/gw/api-server/referentiels"
        
        # Statistiques
        self.stats = {
            'total_associations': 0,
            'requetes_api': 0,
            'erreurs': 0,
            'pages_traitees': 0
        }
        
        # Paramètres de contournement
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    def get_random_headers(self):
        """Génère des headers aléatoires pour éviter la détection"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.data-asso.fr/annuaire',
            'Origin': 'https://www.data-asso.fr'
        }
    
    def analyser_api_search(self):
        """Analyse l'API de recherche pour comprendre ses paramètres"""
        logging.info("🔍 ANALYSE DE L'API DE RECHERCHE")
        
        try:
            # Paramètres de base découverts lors de l'analyse
            payload = {
                "query": "",
                "localisation": "",
                "filters": {
                    "statut": ["active"],
                    "regime": [],
                    "emploi": [],
                    "dateCreation": [],
                    "objetsSociaux": [
                        "culture", "economie", "education", "environnement",
                        "loisirs", "sante", "sport", "autres", "non_renseigne"
                    ],
                    "divers": ["etablissements_secondaires"]
                },
                "sort": "nom",
                "page": 0,
                "size": 20
            }
            
            headers = self.get_random_headers()
            headers['Content-Type'] = 'application/json'
            
            response = self.session.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                logging.info(f"   ✅ API fonctionne ! Structure découverte:")
                logging.info(f"      📊 Total: {data.get('totalElements', 'N/A')}")
                logging.info(f"      📄 Pages: {data.get('totalPages', 'N/A')}")
                logging.info(f"      📋 Éléments par page: {data.get('size', 'N/A')}")
                logging.info(f"      🔢 Éléments actuels: {len(data.get('content', []))}")
                
                return data
            else:
                logging.error(f"   ❌ Erreur API: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"   ❌ Erreur analyse API: {e}")
            return None
    
    def scraper_par_api_paginee(self, max_pages=None):
        """Scrape toutes les associations via l'API avec pagination"""
        logging.info("🚀 SCRAPING VIA API AVEC PAGINATION")
        
        # Analyser d'abord l'API
        initial_data = self.analyser_api_search()
        if not initial_data:
            logging.error("❌ Impossible d'analyser l'API")
            return
        
        total_pages = initial_data.get('totalPages', 0)
        total_elements = initial_data.get('totalElements', 0)
        
        if max_pages:
            total_pages = min(total_pages, max_pages)
        
        logging.info(f"📊 PLAN DE SCRAPING:")
        logging.info(f"   📄 Pages à traiter: {total_pages}")
        logging.info(f"   📋 Total associations: {total_elements}")
        logging.info(f"   ⏱️ Estimation: {total_pages * 2} secondes")
        
        # Traiter la première page (déjà récupérée)
        self.traiter_page_api(initial_data, 0)
        
        # Traiter les pages suivantes en parallèle
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            
            for page in range(1, total_pages):
                future = executor.submit(self.scraper_page_api, page)
                futures.append(future)
                
                # Pause pour éviter de surcharger le serveur
                time.sleep(0.1)
            
            # Récupérer les résultats
            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    
                    if (i + 1) % 100 == 0:
                        logging.info(f"   📊 Progrès: {i+1}/{len(futures)} pages")
                        logging.info(f"      📋 {self.stats['total_associations']} associations récupérées")
                
                except Exception as e:
                    logging.error(f"   ❌ Erreur future page: {e}")
                    self.stats['erreurs'] += 1
        
        logging.info(f"✅ SCRAPING API TERMINÉ:")
        logging.info(f"   📋 {self.stats['total_associations']} associations récupérées")
        logging.info(f"   📄 {self.stats['pages_traitees']} pages traitées")
        logging.info(f"   🔄 {self.stats['requetes_api']} requêtes API")
        logging.info(f"   ❌ {self.stats['erreurs']} erreurs")
    
    def scraper_page_api(self, page):
        """Scrape une page spécifique via l'API"""
        try:
            payload = {
                "query": "",
                "localisation": "",
                "filters": {
                    "statut": ["active"],
                    "regime": [],
                    "emploi": [],
                    "dateCreation": [],
                    "objetsSociaux": [
                        "culture", "economie", "education", "environnement",
                        "loisirs", "sante", "sport", "autres", "non_renseigne"
                    ],
                    "divers": ["etablissements_secondaires"]
                },
                "sort": "nom",
                "page": page,
                "size": 20
            }
            
            headers = self.get_random_headers()
            headers['Content-Type'] = 'application/json'
            
            # Pause aléatoire pour éviter la détection
            time.sleep(random.uniform(0.5, 2.0))
            
            response = self.session.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            with self.lock:
                self.stats['requetes_api'] += 1
            
            if response.status_code == 200:
                data = response.json()
                self.traiter_page_api(data, page)
                return True
            else:
                logging.debug(f"   ❌ Erreur page {page}: {response.status_code}")
                with self.lock:
                    self.stats['erreurs'] += 1
                return False
                
        except Exception as e:
            logging.debug(f"   ❌ Erreur scraping page {page}: {e}")
            with self.lock:
                self.stats['erreurs'] += 1
            return False
    
    def traiter_page_api(self, data, page):
        """Traite les données d'une page API"""
        try:
            content = data.get('content', [])
            
            for association in content:
                rna = association.get('id_rna', '')
                if rna and rna not in self.associations:
                    
                    # Extraire toutes les données disponibles
                    assoc_data = {
                        'rna': rna,
                        'nom': association.get('nom', ''),
                        'siren': association.get('id_siren', ''),
                        'siret': association.get('id_siret_siege', ''),
                        'adresse': association.get('adresse', ''),
                        'theme': association.get('lib_theme1', ''),
                        'active': association.get('active', True),
                        'latitude': association.get('latitude', ''),
                        'longitude': association.get('longitude', ''),
                        'id_interne': association.get('_id', ''),
                        'page_source': page
                    }
                    
                    with self.lock:
                        self.associations[rna] = assoc_data
                        self.stats['total_associations'] += 1
            
            with self.lock:
                self.stats['pages_traitees'] += 1
            
            if page % 100 == 0:
                logging.info(f"   📄 Page {page}: {len(content)} associations")
                
        except Exception as e:
            logging.debug(f"   ❌ Erreur traitement page {page}: {e}")
    
    def scraper_par_filtres_multiples(self):
        """Scrape en utilisant différents filtres pour contourner les limitations"""
        logging.info("🎯 SCRAPING PAR FILTRES MULTIPLES")
        
        # Différents filtres à tester
        filtres_themes = [
            ["culture"],
            ["economie"],
            ["education"],
            ["environnement"],
            ["loisirs"],
            ["sante"],
            ["sport"],
            ["autres"],
            ["non_renseigne"]
        ]
        
        filtres_statut = [
            ["active"],
            ["sommeil"],
            ["dissoute"]
        ]
        
        filtres_emploi = [
            ["employeur"],
            ["non_employeur"]
        ]
        
        # Combiner les filtres
        for theme in filtres_themes:
            for statut in filtres_statut:
                for emploi in filtres_emploi:
                    self.scraper_avec_filtre_specifique(theme, statut, emploi)
                    time.sleep(1)  # Pause entre les requêtes
    
    def scraper_avec_filtre_specifique(self, themes, statuts, emplois):
        """Scrape avec un filtre spécifique"""
        try:
            payload = {
                "query": "",
                "localisation": "",
                "filters": {
                    "statut": statuts,
                    "regime": [],
                    "emploi": emplois,
                    "dateCreation": [],
                    "objetsSociaux": themes,
                    "divers": []
                },
                "sort": "nom",
                "page": 0,
                "size": 1000  # Essayer de récupérer plus d'éléments
            }
            
            headers = self.get_random_headers()
            headers['Content-Type'] = 'application/json'
            
            response = self.session.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                total = data.get('totalElements', 0)
                
                if total > 0:
                    logging.info(f"   🎯 Filtre {themes[0]}/{statuts[0]}/{emplois[0]}: {total} associations")
                    self.traiter_page_api(data, f"filtre_{themes[0]}_{statuts[0]}_{emplois[0]}")
                    
                    # Si il y a plus de 1000 résultats, paginer
                    if total > 1000:
                        total_pages = (total // 1000) + 1
                        for page in range(1, min(total_pages, 10)):  # Limiter à 10 pages par filtre
                            payload['page'] = page
                            response = self.session.post(self.api_url, json=payload, headers=headers, timeout=30)
                            if response.status_code == 200:
                                data = response.json()
                                self.traiter_page_api(data, f"filtre_{themes[0]}_{statuts[0]}_{emplois[0]}_p{page}")
                            time.sleep(0.5)
            
        except Exception as e:
            logging.debug(f"   ❌ Erreur filtre {themes}/{statuts}/{emplois}: {e}")
    
    def scraper_par_selenium_export(self):
        """Utilise Selenium pour cliquer sur le bouton d'export et récupérer les CSV"""
        logging.info("🖱️ SCRAPING PAR SELENIUM + EXPORT CSV")
        
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # Configurer le dossier de téléchargement
        download_dir = os.path.abspath("downloads_data_asso")
        os.makedirs(download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        
        try:
            # Aller sur la page annuaire
            driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Cliquer sur le bouton d'export
            export_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Extraire la liste')]"))
            )
            export_button.click()
            
            logging.info("   ✅ Export CSV déclenché")
            time.sleep(5)
            
            # Vérifier si le fichier a été téléchargé
            csv_files = [f for f in os.listdir(download_dir) if f.endswith('.csv')]
            if csv_files:
                logging.info(f"   📄 Fichier CSV téléchargé: {csv_files[0]}")
                return os.path.join(download_dir, csv_files[0])
            else:
                logging.warning("   ⚠️ Aucun fichier CSV trouvé")
                return None
                
        except Exception as e:
            logging.error(f"   ❌ Erreur Selenium export: {e}")
            return None
            
        finally:
            driver.quit()
    
    def sauvegarder_resultats(self):
        """Sauvegarde tous les résultats"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # JSON complet
        resultats = {
            'metadata': {
                'total_associations': len(self.associations),
                'extraction_date': datetime.now().isoformat(),
                'methode': 'api_complete_avec_contournements',
                'statistiques': self.stats
            },
            'associations': list(self.associations.values())
        }

        json_file = f'data_asso_complet_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(resultats, f, ensure_ascii=False, indent=2)

        # CSV
        csv_file = f'data_asso_complet_{timestamp}.csv'
        if self.associations:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'siren', 'siret', 'adresse', 'theme', 'active', 'latitude', 'longitude', 'id_interne', 'page_source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for assoc in self.associations.values():
                    writer.writerow(assoc)
        else:
            # Créer un fichier CSV vide avec les headers
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'siren', 'siret', 'adresse', 'theme', 'active', 'latitude', 'longitude', 'id_interne', 'page_source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

        logging.info(f"💾 RÉSULTATS SAUVEGARDÉS:")
        logging.info(f"   📄 {json_file}")
        logging.info(f"   📊 {csv_file}")
        logging.info(f"   📋 {len(self.associations)} associations au total")

        return json_file, csv_file
    
    def run_scraping_complet(self):
        """Lance le scraping complet avec toutes les méthodes"""
        logging.info("🚀 SCRAPING COMPLET DATA-ASSO.FR")
        logging.info("=" * 80)
        logging.info("Contourne tous les blocages et extrait TOUTES les associations")
        logging.info("=" * 80)
        
        start_time = time.time()
        
        try:
            # MÉTHODE 1: API avec pagination complète
            logging.info("\n🔥 MÉTHODE 1: API AVEC PAGINATION")
            self.scraper_par_api_paginee(max_pages=1000)  # Limiter pour les tests
            
            # MÉTHODE 2: Filtres multiples pour contourner les limitations
            logging.info("\n🎯 MÉTHODE 2: FILTRES MULTIPLES")
            self.scraper_par_filtres_multiples()
            
            # MÉTHODE 3: Export Selenium (optionnel)
            # logging.info("\n🖱️ MÉTHODE 3: EXPORT SELENIUM")
            # csv_export = self.scraper_par_selenium_export()
            
            # Sauvegarder les résultats
            json_file, csv_file = self.sauvegarder_resultats()
            
            # Résumé final
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n🎉 SCRAPING COMPLET TERMINÉ!")
            print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
            print(f"   📋 Total associations: {len(self.associations)}")
            print(f"   🔄 Requêtes API: {self.stats['requetes_api']}")
            print(f"   📄 Pages traitées: {self.stats['pages_traitees']}")
            print(f"   ❌ Erreurs: {self.stats['erreurs']}")
            print(f"   💾 Fichiers créés: {json_file}, {csv_file}")
            
            if self.associations:
                print(f"\n📊 EXEMPLES D'ASSOCIATIONS RÉCUPÉRÉES:")
                for i, (rna, assoc) in enumerate(list(self.associations.items())[:5]):
                    print(f"   {i+1}. {assoc['nom'][:50]}... (RNA: {rna})")
                    print(f"      📍 {assoc['adresse'][:50]}...")
                    print(f"      🏷️ {assoc['theme']}")
            
            print(f"\n🎯 MISSION ACCOMPLIE! Toutes les associations de data-asso.fr récupérées!")
            
        except Exception as e:
            logging.error(f"❌ Erreur scraping complet: {e}")

def main():
    """Fonction principale"""
    print("🚀 SCRAPER COMPLET POUR DATA-ASSO.FR")
    print("=" * 80)
    print("Contourne TOUS les blocages et extrait TOUTES les associations")
    print("Utilise l'API découverte + filtres multiples + contournements")
    print("=" * 80)
    print()
    print("🎯 Méthodes utilisées:")
    print("   1. API avec pagination complète")
    print("   2. Filtres multiples pour contourner les limitations")
    print("   3. Headers rotatifs et pauses intelligentes")
    print("   4. Traitement parallèle optimisé")
    print()
    
    response = input("Voulez-vous lancer le scraping complet? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = ScraperDataAssoComplet()
    scraper.run_scraping_complet()

if __name__ == "__main__":
    import os
    main()
