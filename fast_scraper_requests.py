import requests
import json
import csv
import time
import re
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from urllib.parse import urljoin, urlparse
import random

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fast_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class FastDataAssoScraper:
    def __init__(self, max_workers=5):
        """
        Scraper ultra-rapide avec requests au lieu de Selenium
        """
        self.session = requests.Session()
        self.max_workers = max_workers
        
        # Headers pour éviter la détection
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        self.session.headers.update(self.headers)
        
        # Statistiques
        self.stats = {
            'total_associations': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'emails_found': 0,
            'phones_found': 0,
            'start_time': None,
            'end_time': None
        }
    
    def discover_all_associations(self):
        """
        Découvre toutes les associations disponibles en testant différentes méthodes
        """
        logging.info("🔍 Découverte de toutes les associations...")
        
        all_links = set()
        
        # Méthode 1: Page d'annuaire standard
        links1 = self.get_associations_from_url("https://www.data-asso.fr/annuaire")
        all_links.update(links1)
        logging.info(f"   📋 Méthode 1 (annuaire): {len(links1)} associations")
        
        # Méthode 2: Tester différents paramètres
        test_urls = [
            "https://www.data-asso.fr/annuaire?page=1",
            "https://www.data-asso.fr/annuaire?page=2",
            "https://www.data-asso.fr/annuaire?page=3",
            "https://www.data-asso.fr/annuaire?limit=100",
            "https://www.data-asso.fr/annuaire?size=100",
            "https://www.data-asso.fr/annuaire?per_page=100",
            "https://www.data-asso.fr/annuaire?offset=20",
            "https://www.data-asso.fr/annuaire?start=20",
        ]
        
        for url in test_urls:
            try:
                links = self.get_associations_from_url(url)
                if links and len(links) > len(links1):  # Si on trouve plus d'associations
                    all_links.update(links)
                    logging.info(f"   🎯 URL prometteuse: {url} -> {len(links)} associations")
                time.sleep(0.5)  # Pause respectueuse
            except Exception as e:
                logging.debug(f"Erreur URL {url}: {e}")
        
        # Méthode 3: Chercher des pages de départements/régions
        dept_codes = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10']  # Test premiers départements
        for dept in dept_codes:
            try:
                dept_url = f"https://www.data-asso.fr/annuaire?departement={dept}"
                links = self.get_associations_from_url(dept_url)
                if links:
                    all_links.update(links)
                    logging.info(f"   🏛️ Département {dept}: {len(links)} associations")
                time.sleep(0.3)
            except Exception as e:
                logging.debug(f"Erreur département {dept}: {e}")
        
        logging.info(f"🎉 Total découvert: {len(all_links)} associations uniques")
        return list(all_links)
    
    def get_associations_from_url(self, url):
        """
        Récupère les liens d'associations depuis une URL
        """
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            association_links = []
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    full_url = urljoin(url, link['href'])
                    association_links.append(full_url)
            
            return list(set(association_links))  # Supprimer doublons
            
        except Exception as e:
            logging.debug(f"Erreur récupération {url}: {e}")
            return []
    
    def extract_association_data_fast(self, association_url):
        """
        Extraction rapide des données d'une association
        """
        try:
            # Ajouter un délai aléatoire pour éviter la détection
            time.sleep(random.uniform(0.1, 0.5))
            
            response = self.session.get(association_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Structure de données
            data = {
                'url': association_url,
                'nom': '',
                'rna': '',
                'siren': '',
                'adresse': '',
                'adresse_gestion': '',
                'telephone': '',
                'email': '',
                'site_web': '',
                'ville': '',
                'code_postal': '',
                'statut': 'Échec'
            }
            
            # Extraire RNA depuis l'URL
            rna_match = re.search(r'/association/([A-Z0-9]+)', association_url)
            if rna_match:
                data['rna'] = rna_match.group(1)
            
            # Extraire le nom (plusieurs méthodes)
            title_selectors = ['h1', 'title', '.association-name', '.title']
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem and title_elem.get_text(strip=True):
                    data['nom'] = title_elem.get_text(strip=True)
                    break
            
            # Extraire SIREN
            text_content = soup.get_text()
            siren_match = re.search(r'SIREN[:\s]*([0-9]{9})', text_content, re.IGNORECASE)
            if siren_match:
                data['siren'] = siren_match.group(1)
            
            # Extraire les coordonnées avec méthodes multiples
            fields_mapping = {
                'Adresse': 'adresse',
                'Adresse de gestion': 'adresse_gestion',
                'Téléphone': 'telephone',
                'Courriel': 'email',
                'Email': 'email',
                'Site internet': 'site_web',
                'Site web': 'site_web'
            }
            
            for field_name, data_key in fields_mapping.items():
                value = self.extract_field_value_fast(soup, field_name)
                if value and value != '-':
                    data[data_key] = value
            
            # Extraire ville et code postal
            if data['adresse']:
                postal_match = re.search(r'(\d{5})\s*([A-Z\s\-À-ÿ]+)', data['adresse'], re.IGNORECASE)
                if postal_match:
                    data['code_postal'] = postal_match.group(1)
                    data['ville'] = postal_match.group(2).strip()
            
            # Déterminer le statut
            has_contact = any([data['telephone'], data['email'], data['site_web']])
            has_address = bool(data['adresse'])
            has_name = bool(data['nom'])
            
            if has_name and has_address and has_contact:
                data['statut'] = 'Données complètes'
            elif data['rna'] and (has_address or has_contact):
                data['statut'] = 'Données partielles'
            elif data['rna']:
                data['statut'] = 'RNA seulement'
            else:
                data['statut'] = 'Échec'
            
            # Mettre à jour les statistiques
            if data['email'] and data['email'] != '-':
                self.stats['emails_found'] += 1
            if data['telephone'] and data['telephone'] != '-':
                self.stats['phones_found'] += 1
            
            if data['statut'] != 'Échec':
                self.stats['successful_extractions'] += 1
            else:
                self.stats['failed_extractions'] += 1
            
            return data
            
        except Exception as e:
            logging.debug(f"Erreur extraction {association_url}: {e}")
            self.stats['failed_extractions'] += 1
            return None
    
    def extract_field_value_fast(self, soup, field_name):
        """
        Extraction rapide d'un champ spécifique
        """
        try:
            # Méthode 1: Recherche directe dans le texte
            pattern = rf'{re.escape(field_name)}\s*:?\s*([^\n\r]+)'
            text_content = soup.get_text()
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if value and value != '-':
                    return value
            
            # Méthode 2: Recherche dans les éléments
            for element in soup.find_all(text=re.compile(field_name, re.IGNORECASE)):
                parent = element.parent
                if parent:
                    # Chercher dans les éléments suivants
                    next_elem = parent.find_next()
                    if next_elem:
                        value = next_elem.get_text(strip=True)
                        if value and value != '-':
                            return value
            
        except Exception as e:
            logging.debug(f"Erreur extraction champ {field_name}: {e}")
        
        return None
    
    def scrape_all_parallel(self, association_links, max_associations=None):
        """
        Scraping parallèle ultra-rapide
        """
        if max_associations:
            association_links = association_links[:max_associations]
        
        self.stats['total_associations'] = len(association_links)
        self.stats['start_time'] = time.time()
        
        logging.info(f"🚀 Scraping parallèle de {len(association_links)} associations")
        logging.info(f"⚡ {self.max_workers} threads simultanés")
        
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Soumettre toutes les tâches
            future_to_url = {
                executor.submit(self.extract_association_data_fast, url): url 
                for url in association_links
            }
            
            # Récupérer les résultats au fur et à mesure
            for i, future in enumerate(as_completed(future_to_url)):
                url = future_to_url[future]
                try:
                    data = future.result()
                    if data:
                        results.append(data)
                        
                        # Afficher le progrès
                        if (i + 1) % 10 == 0 or (i + 1) == len(association_links):
                            elapsed = time.time() - self.stats['start_time']
                            rate = (i + 1) / elapsed
                            logging.info(f"📊 Progrès: {i+1}/{len(association_links)} ({rate:.1f} assoc/sec)")
                
                except Exception as e:
                    logging.error(f"❌ Erreur {url}: {e}")
        
        self.stats['end_time'] = time.time()
        return results
    
    def save_results_fast(self, data, filename_base='associations_fast'):
        """
        Sauvegarde rapide des résultats
        """
        if not data:
            logging.warning("⚠️ Aucune donnée à sauvegarder")
            return
        
        # JSON
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # CSV
        csv_file = f"{filename_base}.csv"
        fieldnames = ['nom', 'rna', 'siren', 'adresse', 'adresse_gestion', 
                     'telephone', 'email', 'site_web', 'ville', 'code_postal', 
                     'statut', 'url']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in data:
                writer.writerow(item)
        
        logging.info(f"💾 Sauvegardé: {json_file} et {csv_file}")
    
    def print_stats(self):
        """
        Affiche les statistiques de performance
        """
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            rate = self.stats['total_associations'] / duration if duration > 0 else 0
            
            print(f"\n📊 STATISTIQUES DE PERFORMANCE:")
            print(f"   ⏱️ Durée totale: {duration:.1f} secondes")
            print(f"   🚀 Vitesse: {rate:.1f} associations/seconde")
            print(f"   🏢 Total associations: {self.stats['total_associations']}")
            print(f"   ✅ Extractions réussies: {self.stats['successful_extractions']}")
            print(f"   ❌ Extractions échouées: {self.stats['failed_extractions']}")
            print(f"   📧 Emails trouvés: {self.stats['emails_found']}")
            print(f"   📞 Téléphones trouvés: {self.stats['phones_found']}")
            
            if self.stats['successful_extractions'] > 0:
                success_rate = (self.stats['successful_extractions'] / self.stats['total_associations']) * 100
                print(f"   📈 Taux de succès: {success_rate:.1f}%")

def main():
    """
    Scraper principal ultra-rapide
    """
    print("⚡ SCRAPER ULTRA-RAPIDE DATA-ASSO")
    print("=" * 50)
    
    scraper = FastDataAssoScraper(max_workers=8)  # 8 threads simultanés
    
    try:
        # 1. Découvrir toutes les associations
        print("\n1️⃣ DÉCOUVERTE DES ASSOCIATIONS")
        all_links = scraper.discover_all_associations()
        
        if not all_links:
            print("❌ Aucune association trouvée")
            return
        
        print(f"🎯 {len(all_links)} associations découvertes")
        
        # 2. Scraping parallèle
        print("\n2️⃣ SCRAPING PARALLÈLE")
        results = scraper.scrape_all_parallel(all_links, max_associations=100)  # Limiter à 100 pour test
        
        # 3. Sauvegarder
        if results:
            scraper.save_results_fast(results)
            
            # Afficher quelques exemples
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS EXTRAITES:")
            for i, assoc in enumerate(results[:5]):
                print(f"\n{i+1}. {assoc.get('nom', 'N/A')}")
                print(f"   🏢 RNA: {assoc.get('rna', 'N/A')}")
                print(f"   📞 Téléphone: {assoc.get('telephone', 'N/A')}")
                print(f"   📧 Email: {assoc.get('email', 'N/A')}")
                print(f"   📊 Statut: {assoc.get('statut', 'N/A')}")
        
        # 4. Statistiques
        scraper.print_stats()
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
