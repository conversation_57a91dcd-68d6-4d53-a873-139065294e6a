import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import itertools
import string
import random
import urllib.parse

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('all_methods_data_asso.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AllMethodsDataAssoScraper:
    def __init__(self):
        """
        TOUTES les méthodes possibles sur data-asso.fr
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        self.session = requests.Session()
        self.all_associations = {}
        
        # Headers pour les requêtes
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 30)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def extract_associations_from_page(self):
        """Extrait les associations de la page actuelle"""
        found = []
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        if rna not in self.all_associations:
                            assoc = {
                                'rna': rna,
                                'nom': link.get_text(strip=True),
                                'url': 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                            }
                            found.append(assoc)
                            self.all_associations[rna] = assoc
            return found
        except Exception as e:
            logging.debug(f"Erreur extraction: {e}")
            return []
    
    def method_1_direct_urls(self):
        """MÉTHODE 1: URLs directes avec tous les paramètres possibles"""
        logging.info("🔍 MÉTHODE 1: URLs directes avec paramètres")
        
        base_urls = [
            "https://www.data-asso.fr/annuaire",
            "https://www.data-asso.fr/annuaire/",
            "https://data-asso.fr/annuaire",
            "https://data-asso.fr/annuaire/",
        ]
        
        # Tous les paramètres possibles
        params_combinations = [
            {},
            {'page': 1}, {'page': 2}, {'page': 3}, {'page': 10}, {'page': 100},
            {'limit': 50}, {'limit': 100}, {'limit': 500}, {'limit': 1000},
            {'size': 50}, {'size': 100}, {'size': 500}, {'size': 1000},
            {'per_page': 50}, {'per_page': 100}, {'per_page': 500},
            {'count': 100}, {'count': 500}, {'count': 1000},
            {'all': 'true'}, {'all': '1'}, {'all': 'yes'},
            {'export': 'true'}, {'export': '1'}, {'export': 'csv'},
            {'format': 'json'}, {'format': 'csv'}, {'format': 'xml'},
            {'full': 'true'}, {'full': '1'}, {'complete': 'true'},
            {'page': 1, 'limit': 100}, {'page': 1, 'size': 100},
            {'page': 2, 'limit': 100}, {'page': 3, 'limit': 100},
            {'all': 'true', 'format': 'json'}, {'export': 'true', 'format': 'csv'},
        ]
        
        total_found = 0
        
        for base_url in base_urls:
            for params in params_combinations:
                try:
                    if params:
                        url = f"{base_url}?" + urllib.parse.urlencode(params)
                    else:
                        url = base_url
                    
                    logging.info(f"   🌐 Test: {url}")
                    self.driver.get(url)
                    time.sleep(2)
                    
                    found = self.extract_associations_from_page()
                    if found:
                        logging.info(f"      🎯 {len(found)} nouvelles associations!")
                        total_found += len(found)
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur {url}: {e}")
        
        logging.info(f"   📊 Total méthode 1: {total_found} associations")
        return total_found
    
    def method_2_all_search_combinations(self):
        """MÉTHODE 2: Toutes les combinaisons de recherche possibles"""
        logging.info("🔍 MÉTHODE 2: Combinaisons de recherche")
        
        # Générer TOUS les termes possibles
        search_terms = []
        
        # Alphabet complet
        alphabet = string.ascii_lowercase
        search_terms.extend(alphabet)
        
        # Nombres
        search_terms.extend([str(i) for i in range(100)])
        
        # Départements
        search_terms.extend([f"{i:02d}" for i in range(1, 96)])
        search_terms.extend(['971', '972', '973', '974', '975', '976'])
        
        # Combinaisons de 2 lettres
        for a, b in itertools.product(alphabet[:10], repeat=2):
            search_terms.append(f"{a}{b}")
        
        # Mots-clés spécialisés
        keywords = [
            'sport', 'culture', 'social', 'education', 'sante', 'environnement',
            'association', 'club', 'federation', 'union', 'comite', 'groupe',
            'paris', 'lyon', 'marseille', 'toulouse', 'nice', 'nantes',
            'football', 'tennis', 'basketball', 'musique', 'theatre', 'danse',
            'aide', 'soutien', 'solidarite', 'humanitaire', 'benevolat',
            'jeunesse', 'seniors', 'enfants', 'famille', 'femmes', 'handicap'
        ]
        search_terms.extend(keywords)
        
        # Combinaisons de mots-clés
        for k1, k2 in itertools.combinations(keywords[:20], 2):
            search_terms.append(f"{k1} {k2}")
        
        total_found = 0
        
        # Tester avec différents paramètres de recherche
        search_params = ['q', 'search', 'query', 'term', 'keyword', 'nom', 'name']
        
        for param in search_params:
            for term in search_terms[:500]:  # Limiter pour les tests
                try:
                    url = f"https://www.data-asso.fr/annuaire?{param}={urllib.parse.quote(term)}"
                    self.driver.get(url)
                    time.sleep(1)
                    
                    found = self.extract_associations_from_page()
                    if found:
                        logging.info(f"      🎯 '{term}' ({param}): {len(found)} associations")
                        total_found += len(found)
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur {term}: {e}")
        
        logging.info(f"   📊 Total méthode 2: {total_found} associations")
        return total_found
    
    def method_3_form_manipulation_advanced(self):
        """MÉTHODE 3: Manipulation avancée de tous les formulaires"""
        logging.info("🔍 MÉTHODE 3: Manipulation avancée de formulaires")
        
        total_found = 0
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Trouver TOUS les éléments interactifs
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            selects = self.driver.find_elements(By.TAG_NAME, "select")
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            textareas = self.driver.find_elements(By.TAG_NAME, "textarea")
            
            logging.info(f"   📊 Éléments trouvés: {len(inputs)} inputs, {len(selects)} selects, {len(buttons)} boutons")
            
            # Test de toutes les combinaisons d'inputs
            test_values = ['association', 'sport', 'culture', 'paris', '75', 'a', '1', '']
            
            for input_elem in inputs:
                try:
                    input_type = input_elem.get_attribute('type')
                    input_name = input_elem.get_attribute('name')
                    
                    if input_type in ['text', 'search', 'email', 'tel']:
                        for value in test_values:
                            try:
                                input_elem.clear()
                                input_elem.send_keys(value)
                                input_elem.send_keys(Keys.RETURN)
                                time.sleep(2)
                                
                                found = self.extract_associations_from_page()
                                if found:
                                    logging.info(f"      🎯 Input '{input_name}' = '{value}': {len(found)} associations")
                                    total_found += len(found)
                                
                            except Exception as e:
                                logging.debug(f"        ❌ Erreur input {value}: {e}")
                                
                except Exception as e:
                    logging.debug(f"      ❌ Erreur input: {e}")
            
            # Test de tous les selects
            for select_elem in selects:
                try:
                    select = Select(select_elem)
                    options = select.options
                    
                    for option in options:
                        try:
                            select.select_by_visible_text(option.text)
                            time.sleep(2)
                            
                            found = self.extract_associations_from_page()
                            if found:
                                logging.info(f"      🎯 Select '{option.text}': {len(found)} associations")
                                total_found += len(found)
                            
                        except Exception as e:
                            logging.debug(f"        ❌ Erreur option {option.text}: {e}")
                            
                except Exception as e:
                    logging.debug(f"      ❌ Erreur select: {e}")
            
            # Test de tous les boutons
            for button in buttons:
                try:
                    button_text = button.text.strip()
                    if button_text and button.is_displayed() and button.is_enabled():
                        logging.info(f"      🔘 Clic sur: '{button_text}'")
                        button.click()
                        time.sleep(3)
                        
                        found = self.extract_associations_from_page()
                        if found:
                            logging.info(f"        🎯 Bouton '{button_text}': {len(found)} associations")
                            total_found += len(found)
                        
                except Exception as e:
                    logging.debug(f"        ❌ Erreur bouton: {e}")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur méthode 3: {e}")
        
        logging.info(f"   📊 Total méthode 3: {total_found} associations")
        return total_found
    
    def method_4_javascript_injection(self):
        """MÉTHODE 4: Injection JavaScript pour découvrir des données cachées"""
        logging.info("🔍 MÉTHODE 4: Injection JavaScript")
        
        total_found = 0
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Scripts JavaScript pour découvrir des données
            js_scripts = [
                # Chercher dans les variables globales
                """
                var associations = [];
                for (var key in window) {
                    try {
                        if (typeof window[key] === 'object' && window[key] !== null) {
                            var str = JSON.stringify(window[key]);
                            if (str.includes('RNA') || str.includes('association') || str.includes('W0') || str.includes('W1') || str.includes('W2')) {
                                associations.push({key: key, data: window[key]});
                            }
                        }
                    } catch(e) {}
                }
                return associations;
                """,
                
                # Chercher dans le localStorage
                """
                var data = [];
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    var value = localStorage.getItem(key);
                    if (value.includes('RNA') || value.includes('association')) {
                        data.push({key: key, value: value});
                    }
                }
                return data;
                """,
                
                # Chercher dans sessionStorage
                """
                var data = [];
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    var value = sessionStorage.getItem(key);
                    if (value.includes('RNA') || value.includes('association')) {
                        data.push({key: key, value: value});
                    }
                }
                return data;
                """,
                
                # Déclencher des événements
                """
                // Déclencher tous les événements possibles
                var events = ['scroll', 'resize', 'load', 'click', 'mouseover', 'focus'];
                events.forEach(function(eventType) {
                    var event = new Event(eventType);
                    document.dispatchEvent(event);
                    document.body.dispatchEvent(event);
                });
                return 'Events triggered';
                """,
                
                # Chercher des fonctions cachées
                """
                var functions = [];
                for (var key in window) {
                    if (typeof window[key] === 'function') {
                        var funcStr = window[key].toString();
                        if (funcStr.includes('association') || funcStr.includes('RNA') || funcStr.includes('search')) {
                            functions.push({name: key, func: funcStr.substring(0, 200)});
                        }
                    }
                }
                return functions;
                """
            ]
            
            for i, script in enumerate(js_scripts):
                try:
                    logging.info(f"      🔧 Script JavaScript {i+1}")
                    result = self.driver.execute_script(script)
                    
                    if result:
                        logging.info(f"        📊 Résultat: {len(result) if isinstance(result, list) else 'Data found'}")
                        
                        # Analyser les résultats pour des associations
                        if isinstance(result, list):
                            for item in result:
                                if isinstance(item, dict):
                                    # Chercher des RNAs dans les données
                                    item_str = json.dumps(item)
                                    rna_matches = re.findall(r'W[0-9]{9}[A-Z]?', item_str)
                                    for rna in rna_matches:
                                        if rna not in self.all_associations:
                                            assoc = {
                                                'rna': rna,
                                                'nom': 'Trouvé en JavaScript',
                                                'url': f'https://www.data-asso.fr/association/{rna}',
                                                'method': 'javascript'
                                            }
                                            self.all_associations[rna] = assoc
                                            total_found += 1
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logging.debug(f"        ❌ Erreur script {i+1}: {e}")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur méthode 4: {e}")
        
        logging.info(f"   📊 Total méthode 4: {total_found} associations")
        return total_found
    
    def method_5_network_analysis(self):
        """MÉTHODE 5: Analyse complète du réseau"""
        logging.info("🔍 MÉTHODE 5: Analyse réseau")
        
        total_found = 0
        
        try:
            # Activer la capture réseau
            self.driver.execute_cdp_cmd('Network.enable', {})
            
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Déclencher des actions pour générer du trafic réseau
            actions = [
                lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
                lambda: self.driver.refresh(),
                lambda: self.driver.execute_script("location.reload();"),
                lambda: self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.F5),
            ]
            
            for action in actions:
                try:
                    action()
                    time.sleep(3)
                except:
                    pass
            
            # Récupérer les logs réseau
            logs = self.driver.get_log('performance')
            
            api_urls = set()
            for log in logs:
                try:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.responseReceived':
                        response = message['message']['params']['response']
                        url = response['url']
                        
                        # Filtrer les URLs intéressantes
                        if any(keyword in url.lower() for keyword in ['api', 'search', 'association', 'data', 'json', 'csv']):
                            api_urls.add(url)
                            
                except:
                    continue
            
            logging.info(f"      📡 {len(api_urls)} URLs réseau découvertes")
            
            # Tester chaque URL découverte
            for api_url in api_urls:
                try:
                    logging.info(f"      🔗 Test: {api_url}")
                    response = self.session.get(api_url, timeout=10)
                    
                    if response.status_code == 200:
                        try:
                            # Essayer JSON
                            data = response.json()
                            rna_count = self.find_rnas_in_data(data)
                            if rna_count > 0:
                                logging.info(f"        🎯 {rna_count} RNAs trouvés dans l'API!")
                                total_found += rna_count
                        except:
                            # Essayer regex sur le texte
                            rna_matches = re.findall(r'W[0-9]{9}[A-Z]?', response.text)
                            if rna_matches:
                                logging.info(f"        🎯 {len(rna_matches)} RNAs trouvés dans le texte!")
                                total_found += len(rna_matches)
                    
                except Exception as e:
                    logging.debug(f"        ❌ Erreur API {api_url}: {e}")
        
        except Exception as e:
            logging.error(f"   ❌ Erreur méthode 5: {e}")
        
        logging.info(f"   📊 Total méthode 5: {total_found} associations")
        return total_found
    
    def find_rnas_in_data(self, data):
        """Trouve des RNAs dans des données JSON"""
        count = 0
        
        def search_recursive(obj):
            nonlocal count
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, str) and re.match(r'W[0-9]{9}[A-Z]?', value):
                        if value not in self.all_associations:
                            self.all_associations[value] = {
                                'rna': value,
                                'nom': 'Trouvé via API',
                                'url': f'https://www.data-asso.fr/association/{value}',
                                'method': 'api'
                            }
                            count += 1
                    else:
                        search_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    search_recursive(item)
        
        search_recursive(data)
        return count
    
    def run_all_methods(self):
        """Lance TOUTES les méthodes sur data-asso.fr"""
        logging.info("🚀 LANCEMENT DE TOUTES LES MÉTHODES SUR DATA-ASSO.FR")
        
        methods = [
            ("URLs directes", self.method_1_direct_urls),
            ("Recherche combinée", self.method_2_all_search_combinations),
            ("Formulaires avancés", self.method_3_form_manipulation_advanced),
            ("JavaScript injection", self.method_4_javascript_injection),
            ("Analyse réseau", self.method_5_network_analysis),
        ]
        
        results = {}
        total_associations = 0
        
        for method_name, method_func in methods:
            logging.info(f"\n{'='*60}")
            logging.info(f"MÉTHODE: {method_name.upper()}")
            
            try:
                found = method_func()
                results[method_name] = found
                total_associations += found
                
                logging.info(f"✅ {method_name}: {found} associations")
                
            except Exception as e:
                logging.error(f"❌ Erreur {method_name}: {e}")
                results[method_name] = 0
        
        # Résultats finaux
        final_results = {
            'total_unique_associations': len(self.all_associations),
            'methods_results': results,
            'all_associations': list(self.all_associations.values())
        }
        
        logging.info(f"\n🎉 TOUTES LES MÉTHODES TERMINÉES!")
        logging.info(f"   📊 Associations uniques trouvées: {len(self.all_associations)}")
        
        return final_results
    
    def save_results(self, results):
        """Sauvegarde tous les résultats"""
        # JSON
        with open('all_methods_data_asso_results.json', 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'site': 'data-asso.fr',
                    'methods_used': 'ALL_POSSIBLE_METHODS'
                },
                'results': results
            }, f, ensure_ascii=False, indent=2)
        
        # CSV
        if results['all_associations']:
            with open('all_methods_data_asso_results.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'url', 'method']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for assoc in results['all_associations']:
                    writer.writerow(assoc)
        
        logging.info("💾 Résultats sauvegardés:")
        logging.info("   📄 all_methods_data_asso_results.json")
        logging.info("   📊 all_methods_data_asso_results.csv")

def main():
    """
    Scraper principal - TOUTES les méthodes sur data-asso.fr
    """
    print("🚀 TOUTES LES MÉTHODES POSSIBLES SUR DATA-ASSO.FR")
    print("=" * 70)
    print("Ce scraper va tester ABSOLUMENT TOUTES les méthodes possibles")
    print("sur le site data-asso.fr pour extraire le maximum d'associations:")
    print()
    print("1. URLs directes avec tous les paramètres")
    print("2. Toutes les combinaisons de recherche")
    print("3. Manipulation avancée de formulaires")
    print("4. Injection JavaScript")
    print("5. Analyse complète du réseau")
    print()
    print("⚠️ ATTENTION: Ce processus peut prendre 2-3 heures!")
    print()
    
    response = input("Voulez-vous lancer TOUTES les méthodes? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = AllMethodsDataAssoScraper()
    
    try:
        scraper.start_driver()
        
        # Lancer toutes les méthodes
        results = scraper.run_all_methods()
        
        # Sauvegarder
        scraper.save_results(results)
        
        # Afficher le résumé final
        print(f"\n🎉 TOUTES LES MÉTHODES TERMINÉES!")
        print(f"   📊 Associations uniques: {results['total_unique_associations']}")
        
        print(f"\n📋 RÉSULTATS PAR MÉTHODE:")
        for method, count in results['methods_results'].items():
            print(f"   📊 {method}: {count} associations")
        
        if results['all_associations']:
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS TROUVÉES:")
            for i, assoc in enumerate(results['all_associations'][:10]):
                print(f"   {i+1}. {assoc['rna']}: {assoc['nom'][:50]}...")
        
        print(f"\n💾 Résultats complets sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()
