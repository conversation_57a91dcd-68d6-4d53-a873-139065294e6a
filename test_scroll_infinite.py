import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scroll_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ScrollInfiniteTest:
    def __init__(self, headless=False):
        """
        Test du scroll infini sur data-asso.fr
        """
        self.options = Options()
        if headless:
            self.options.add_argument('--headless')
        
        # Options optimisées
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver Chrome"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 15)
            logging.info("✅ Driver Chrome démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def test_scroll_infinite(self, max_scrolls=20, target_associations=100):
        """
        Test du scroll infini pour charger plus d'associations
        """
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🌐 Test scroll infini sur {url}")
        logging.info(f"📊 Objectif: {target_associations} associations, max {max_scrolls} scrolls")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            # Attendre que la page se charge
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            associations_count_history = []
            scroll_count = 0
            no_change_count = 0
            
            while scroll_count < max_scrolls:
                # Compter les associations actuelles
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # Différentes méthodes pour compter les associations
                association_links = []
                for link in soup.find_all('a', href=True):
                    if '/association/' in link['href']:
                        full_url = 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                        association_links.append(full_url)
                
                # Supprimer les doublons
                unique_associations = list(set(association_links))
                current_count = len(unique_associations)
                
                associations_count_history.append(current_count)
                
                logging.info(f"📋 Scroll {scroll_count + 1}: {current_count} associations trouvées")
                
                # Vérifier si on a atteint l'objectif
                if current_count >= target_associations:
                    logging.info(f"🎯 Objectif atteint: {current_count} associations")
                    break
                
                # Vérifier si le nombre n'a pas changé
                if len(associations_count_history) >= 2:
                    if associations_count_history[-1] == associations_count_history[-2]:
                        no_change_count += 1
                        logging.info(f"⚠️ Pas de changement ({no_change_count}/3)")
                        
                        if no_change_count >= 3:
                            logging.info("🛑 Arrêt: Pas de nouvelles associations après 3 tentatives")
                            break
                    else:
                        no_change_count = 0
                
                # Scroll vers le bas
                logging.info(f"⬇️ Scroll {scroll_count + 1}...")
                
                # Méthode 1: Scroll vers le bas de la page
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)
                
                # Méthode 2: Scroll progressif si pas de changement
                if no_change_count > 0:
                    logging.info("🔄 Scroll progressif...")
                    for i in range(5):
                        self.driver.execute_script(f"window.scrollBy(0, {500 * (i + 1)});")
                        time.sleep(1)
                
                # Méthode 3: Chercher et cliquer sur un bouton "Charger plus"
                try:
                    load_more_selectors = [
                        "//button[contains(text(), 'Charger')]",
                        "//button[contains(text(), 'Plus')]",
                        "//button[contains(text(), 'Suivant')]",
                        "//a[contains(text(), 'Charger')]",
                        "//a[contains(text(), 'Plus')]",
                        "//*[@class*='load-more']",
                        "//*[@class*='next']",
                        "//*[@id*='load']"
                    ]
                    
                    for selector in load_more_selectors:
                        try:
                            button = self.driver.find_element(By.XPATH, selector)
                            if button.is_displayed() and button.is_enabled():
                                logging.info(f"🔘 Bouton trouvé: {selector}")
                                button.click()
                                time.sleep(3)
                                break
                        except:
                            continue
                            
                except Exception as e:
                    logging.debug(f"Pas de bouton 'Charger plus': {e}")
                
                scroll_count += 1
                time.sleep(2)  # Pause respectueuse
            
            # Résultats finaux
            final_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            final_links = []
            for link in final_soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    full_url = 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                    final_links.append(full_url)
            
            final_unique = list(set(final_links))
            
            results = {
                'total_scrolls': scroll_count,
                'associations_found': len(final_unique),
                'scroll_history': associations_count_history,
                'sample_links': final_unique[:10],  # 10 premiers liens
                'all_links': final_unique
            }
            
            logging.info(f"\n🎉 RÉSULTATS FINAUX:")
            logging.info(f"   📊 Total scrolls: {scroll_count}")
            logging.info(f"   🏢 Associations trouvées: {len(final_unique)}")
            logging.info(f"   📈 Progression: {associations_count_history}")
            
            return results
            
        except Exception as e:
            logging.error(f"❌ Erreur test scroll: {e}")
            return None
    
    def analyze_page_structure(self):
        """
        Analyse la structure de la page pour comprendre le chargement
        """
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🔍 Analyse structure: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            # Analyser le JavaScript et les requêtes réseau
            logs = self.driver.get_log('performance')
            network_requests = []
            
            for log in logs:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.responseReceived':
                    url_req = message['message']['params']['response']['url']
                    if 'api' in url_req.lower() or 'ajax' in url_req.lower():
                        network_requests.append(url_req)
            
            # Analyser le HTML
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Chercher des indices de pagination/scroll infini
            pagination_indicators = []
            
            # Scripts qui pourraient gérer le scroll
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    if any(keyword in script.string.lower() for keyword in ['scroll', 'infinite', 'load', 'page', 'next']):
                        pagination_indicators.append("Script avec mots-clés scroll/load détecté")
            
            # Éléments de pagination
            pagination_elements = soup.find_all(['div', 'nav', 'ul'], class_=re.compile(r'pag|nav|load|more', re.I))
            
            analysis = {
                'network_requests': network_requests[:5],  # 5 premiers
                'pagination_indicators': pagination_indicators,
                'pagination_elements': len(pagination_elements),
                'total_scripts': len(scripts)
            }
            
            logging.info(f"📊 Analyse terminée:")
            logging.info(f"   🌐 Requêtes réseau API: {len(network_requests)}")
            logging.info(f"   📜 Scripts avec scroll: {len(pagination_indicators)}")
            logging.info(f"   🔘 Éléments pagination: {len(pagination_elements)}")
            
            return analysis
            
        except Exception as e:
            logging.error(f"❌ Erreur analyse: {e}")
            return None

def main():
    """
    Test principal du scroll infini
    """
    print("🧪 TEST DU SCROLL INFINI - DATA-ASSO")
    print("=" * 60)
    
    tester = ScrollInfiniteTest(headless=False)
    
    try:
        tester.start_driver()
        
        # 1. Analyser la structure
        print("\n1️⃣ ANALYSE DE LA STRUCTURE")
        analysis = tester.analyze_page_structure()
        
        # 2. Test du scroll infini
        print("\n2️⃣ TEST DU SCROLL INFINI")
        results = tester.test_scroll_infinite(max_scrolls=15, target_associations=50)
        
        if results:
            print(f"\n📊 RÉSULTATS:")
            print(f"   🏢 Associations trouvées: {results['associations_found']}")
            print(f"   📊 Scrolls effectués: {results['total_scrolls']}")
            print(f"   📈 Progression: {results['scroll_history']}")
            
            # Sauvegarder les résultats
            with open('scroll_test_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 Résultats sauvegardés dans: scroll_test_results.json")
            print(f"📄 Log détaillé dans: scroll_test.log")
            
            # Afficher quelques exemples
            print(f"\n📋 Exemples d'associations trouvées:")
            for i, link in enumerate(results['sample_links'][:5]):
                rna_match = re.search(r'/association/([A-Z0-9]+)', link)
                rna = rna_match.group(1) if rna_match else 'N/A'
                print(f"   {i+1}. RNA: {rna}")
        
        else:
            print("\n❌ Échec du test")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
