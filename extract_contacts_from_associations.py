import requests
import json
import csv
import re
import time
import logging
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import threading
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_contacts.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ContactsExtractor:
    def __init__(self):
        """
        Extracteur de contacts (email + téléphone) pour les associations
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.associations_with_contacts = {}
        self.stats = {
            'total_processed': 0,
            'with_email': 0,
            'with_phone': 0,
            'with_both': 0,
            'errors': 0
        }
        
        self.lock = threading.Lock()
        
        # Patterns pour extraire emails et téléphones
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_patterns = [
            re.compile(r'0[1-9](?:[0-9]{8})'),  # Format français 0X XX XX XX XX
            re.compile(r'\+33[1-9](?:[0-9]{8})'),  # Format international +33
            re.compile(r'(?:0[1-9]|33[1-9])(?:[-.\s]?[0-9]{2}){4}'),  # Avec séparateurs
            re.compile(r'(?:0[1-9])(?:\s?[0-9]{2}){4}'),  # Avec espaces
        ]
        
    def extract_contacts_from_text(self, text):
        """Extrait emails et téléphones d'un texte"""
        emails = self.email_pattern.findall(text)
        phones = []
        
        for pattern in self.phone_patterns:
            phones.extend(pattern.findall(text))
        
        # Nettoyer les téléphones
        cleaned_phones = []
        for phone in phones:
            cleaned = re.sub(r'[-.\s]', '', phone)
            if len(cleaned) >= 10:
                cleaned_phones.append(cleaned)
        
        # Supprimer les doublons
        emails = list(set(emails))
        cleaned_phones = list(set(cleaned_phones))
        
        return emails, cleaned_phones
    
    def get_association_page_content(self, rna):
        """Récupère le contenu de la page d'une association"""
        try:
            # URL de la page association sur data-asso.fr
            url = f"https://www.data-asso.fr/association/{rna}"
            
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                return response.text
            else:
                return None
                
        except Exception as e:
            logging.debug(f"Erreur récupération {rna}: {e}")
            return None
    
    def search_contacts_google(self, association_name, rna):
        """Recherche les contacts via Google"""
        try:
            # Recherche Google pour l'association
            search_query = f'"{association_name}" contact email telephone site:*.fr'
            google_url = f"https://www.google.com/search?q={requests.utils.quote(search_query)}"
            
            response = self.session.get(google_url, timeout=10)
            
            if response.status_code == 200:
                emails, phones = self.extract_contacts_from_text(response.text)
                return emails, phones
            
        except Exception as e:
            logging.debug(f"Erreur Google {rna}: {e}")
        
        return [], []
    
    def search_contacts_pages_jaunes(self, association_name):
        """Recherche les contacts via Pages Jaunes"""
        try:
            # Recherche Pages Jaunes
            search_url = f"https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui={requests.utils.quote(association_name)}"
            
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher spécifiquement les éléments de contact
                contact_elements = soup.find_all(class_=lambda x: x and any(
                    keyword in str(x).lower() for keyword in ['contact', 'tel', 'phone', 'mail', 'email']
                ))
                
                all_text = ' '.join([elem.get_text() for elem in contact_elements])
                emails, phones = self.extract_contacts_from_text(all_text)
                
                return emails, phones
            
        except Exception as e:
            logging.debug(f"Erreur Pages Jaunes {association_name}: {e}")
        
        return [], []
    
    def process_association(self, association):
        """Traite une association pour extraire ses contacts"""
        rna = association.get('rna', '')
        nom = association.get('nom', '')
        
        if not rna or not nom:
            return None
        
        try:
            all_emails = []
            all_phones = []
            
            # Méthode 1: Page data-asso.fr
            page_content = self.get_association_page_content(rna)
            if page_content:
                emails, phones = self.extract_contacts_from_text(page_content)
                all_emails.extend(emails)
                all_phones.extend(phones)
            
            # Méthode 2: Recherche Google (si pas de contacts trouvés)
            if not all_emails and not all_phones:
                emails, phones = self.search_contacts_google(nom, rna)
                all_emails.extend(emails)
                all_phones.extend(phones)
            
            # Méthode 3: Pages Jaunes (si toujours pas de contacts)
            if not all_emails and not all_phones:
                emails, phones = self.search_contacts_pages_jaunes(nom)
                all_emails.extend(emails)
                all_phones.extend(phones)
            
            # Supprimer les doublons
            all_emails = list(set(all_emails))
            all_phones = list(set(all_phones))
            
            # Mettre à jour les statistiques
            with self.lock:
                self.stats['total_processed'] += 1
                
                if all_emails or all_phones:
                    contact_info = {
                        'rna': rna,
                        'nom': nom,
                        'email': all_emails[0] if all_emails else '',
                        'emails_all': all_emails,
                        'telephone': all_phones[0] if all_phones else '',
                        'telephones_all': all_phones,
                        'source': association.get('source', ''),
                        'url': f"https://www.data-asso.fr/association/{rna}"
                    }
                    
                    self.associations_with_contacts[rna] = contact_info
                    
                    if all_emails:
                        self.stats['with_email'] += 1
                    if all_phones:
                        self.stats['with_phone'] += 1
                    if all_emails and all_phones:
                        self.stats['with_both'] += 1
                    
                    return contact_info
            
            return None
            
        except Exception as e:
            with self.lock:
                self.stats['errors'] += 1
            logging.debug(f"Erreur traitement {rna}: {e}")
            return None
    
    def extract_contacts_parallel(self, associations, max_workers=5, max_associations=1000):
        """Extrait les contacts en parallèle"""
        logging.info(f"📧 EXTRACTION CONTACTS EN PARALLÈLE")
        logging.info(f"📊 {len(associations)} associations à traiter (max {max_associations})")
        
        # Limiter le nombre d'associations pour les tests
        associations_to_process = associations[:max_associations]
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Soumettre les tâches
            future_to_association = {
                executor.submit(self.process_association, assoc): assoc 
                for assoc in associations_to_process
            }
            
            # Traiter les résultats
            for i, future in enumerate(as_completed(future_to_association)):
                try:
                    result = future.result()
                    
                    if result:
                        logging.info(f"   🎯 {result['rna']}: {result['nom'][:50]}...")
                        if result['email']:
                            logging.info(f"      📧 Email: {result['email']}")
                        if result['telephone']:
                            logging.info(f"      📞 Téléphone: {result['telephone']}")
                    
                    # Afficher le progrès
                    if (i + 1) % 50 == 0:
                        logging.info(f"   📊 Progression: {i+1}/{len(associations_to_process)} ({(i+1)/len(associations_to_process)*100:.1f}%)")
                        logging.info(f"      📧 Avec email: {self.stats['with_email']}")
                        logging.info(f"      📞 Avec téléphone: {self.stats['with_phone']}")
                        logging.info(f"      🎯 Avec les deux: {self.stats['with_both']}")
                
                except Exception as e:
                    logging.error(f"Erreur future: {e}")
                
                # Pause respectueuse
                time.sleep(0.1)
        
        return self.stats
    
    def save_contacts_results(self):
        """Sauvegarde les résultats avec contacts"""
        if not self.associations_with_contacts:
            logging.warning("⚠️ Aucun contact trouvé")
            return
        
        associations_list = list(self.associations_with_contacts.values())
        
        # JSON des contacts
        contacts_results = {
            'metadata': {
                'total_associations_with_contacts': len(associations_list),
                'with_email_only': self.stats['with_email'] - self.stats['with_both'],
                'with_phone_only': self.stats['with_phone'] - self.stats['with_both'],
                'with_both_email_and_phone': self.stats['with_both'],
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'stats': self.stats
            },
            'associations': associations_list
        }
        
        with open('associations_avec_contacts.json', 'w', encoding='utf-8') as f:
            json.dump(contacts_results, f, ensure_ascii=False, indent=2)
        
        # CSV PRIORITÉ CONTACTS
        with open('associations_avec_contacts.csv', 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['rna', 'nom', 'email', 'telephone', 'emails_all', 'telephones_all', 'source', 'url']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            # Trier par priorité : email + téléphone d'abord
            sorted_associations = sorted(associations_list, key=lambda x: (
                -(bool(x.get('email')) and bool(x.get('telephone'))),  # Both first
                -bool(x.get('email')),  # Email only second
                -bool(x.get('telephone'))  # Phone only third
            ))
            
            for assoc in sorted_associations:
                # Convertir les listes en chaînes pour CSV
                assoc_copy = assoc.copy()
                assoc_copy['emails_all'] = '; '.join(assoc.get('emails_all', []))
                assoc_copy['telephones_all'] = '; '.join(assoc.get('telephones_all', []))
                writer.writerow(assoc_copy)
        
        # CSV EMAILS SEULEMENT
        email_associations = [a for a in associations_list if a.get('email')]
        if email_associations:
            with open('associations_emails.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'email', 'emails_all', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for assoc in email_associations:
                    assoc_copy = assoc.copy()
                    assoc_copy['emails_all'] = '; '.join(assoc.get('emails_all', []))
                    writer.writerow(assoc_copy)
        
        # CSV TÉLÉPHONES SEULEMENT
        phone_associations = [a for a in associations_list if a.get('telephone')]
        if phone_associations:
            with open('associations_telephones.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'telephone', 'telephones_all', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for assoc in phone_associations:
                    assoc_copy = assoc.copy()
                    assoc_copy['telephones_all'] = '; '.join(assoc.get('telephones_all', []))
                    writer.writerow(assoc_copy)
        
        logging.info("💾 Résultats contacts sauvegardés:")
        logging.info("   📄 associations_avec_contacts.json")
        logging.info("   📊 associations_avec_contacts.csv (PRIORITÉ EMAIL+TÉLÉPHONE)")
        if email_associations:
            logging.info(f"   📧 associations_emails.csv ({len(email_associations)} avec emails)")
        if phone_associations:
            logging.info(f"   📞 associations_telephones.csv ({len(phone_associations)} avec téléphones)")

def main():
    """
    Extracteur principal de contacts
    """
    print("📧 EXTRACTEUR DE CONTACTS - EMAIL + TÉLÉPHONE")
    print("=" * 60)
    print("Ce script va extraire les CONTACTS de vos associations:")
    print("🎯 PRIORITÉ 1: Email + Téléphone")
    print("🎯 PRIORITÉ 2: Email seulement")
    print("🎯 PRIORITÉ 3: Téléphone seulement")
    print()
    print("Méthodes d'extraction:")
    print("1. Pages individuelles data-asso.fr")
    print("2. Recherche Google")
    print("3. Pages Jaunes")
    print()
    
    # Paramètres
    max_associations = input("Nombre max d'associations à traiter (défaut: 1000): ").strip()
    try:
        max_associations = int(max_associations) if max_associations else 1000
    except:
        max_associations = 1000
    
    max_workers = input("Nombre de threads parallèles (défaut: 5): ").strip()
    try:
        max_workers = int(max_workers) if max_workers else 5
    except:
        max_workers = 5
    
    print(f"\n🚀 Extraction de contacts pour {max_associations} associations avec {max_workers} threads")
    
    extractor = ContactsExtractor()
    
    try:
        # Charger les associations
        with open('mega_scraper_all_associations.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        associations = data.get('associations', [])
        print(f"📊 {len(associations)} associations chargées")
        
        start_time = time.time()
        
        # Extraction des contacts
        stats = extractor.extract_contacts_parallel(
            associations, 
            max_workers=max_workers, 
            max_associations=max_associations
        )
        
        # Sauvegarder
        extractor.save_contacts_results()
        
        # Afficher le résumé final
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📧 EXTRACTION CONTACTS TERMINÉE!")
        print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
        print(f"   📊 Associations traitées: {stats['total_processed']}")
        print(f"   📧 Avec email: {stats['with_email']}")
        print(f"   📞 Avec téléphone: {stats['with_phone']}")
        print(f"   🎯 Avec les deux: {stats['with_both']}")
        print(f"   ❌ Erreurs: {stats['errors']}")
        
        if extractor.associations_with_contacts:
            print(f"\n📧 EXEMPLES DE CONTACTS TROUVÉS:")
            for i, assoc in enumerate(list(extractor.associations_with_contacts.values())[:5]):
                print(f"   {i+1}. {assoc['nom'][:40]}...")
                if assoc.get('email'):
                    print(f"      📧 {assoc['email']}")
                if assoc.get('telephone'):
                    print(f"      📞 {assoc['telephone']}")
        
        print(f"\n💾 Fichiers contacts créés et prêts à utiliser!")
        
    except FileNotFoundError:
        print("❌ Fichier mega_scraper_all_associations.json non trouvé")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
