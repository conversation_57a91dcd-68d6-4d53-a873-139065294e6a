#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SURVEILLANCE EN TEMPS RÉEL DU SCRAPER DATA-ASSO.FR
Surveille les fichiers générés et affiche les statistiques en temps réel
"""

import time
import json
import os
import glob
from datetime import datetime
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SurveillanceScraper:
    def __init__(self):
        """Surveillance en temps réel du scraper"""
        self.derniere_verification = datetime.now()
        self.associations_precedentes = 0
        self.fichiers_precedents = 0
        
    def compter_associations_actuelles(self):
        """Compte le nombre total d'associations dans tous les fichiers"""
        associations = set()
        
        # Fichiers JSON
        json_files = glob.glob("data_asso_*.json")
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for assoc in data.get('associations', []):
                        rna = assoc.get('rna', '')
                        if rna:
                            associations.add(rna)
            except:
                pass
        
        # Fichiers CSV téléchargés
        download_dir = "downloads_data_asso"
        if os.path.exists(download_dir):
            csv_files = glob.glob(os.path.join(download_dir, "*.csv"))
            for csv_file in csv_files:
                try:
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.strip().split('\n')
                        if len(lines) > 1:
                            headers = lines[0].split(';')
                            if 'id_rna' in headers:
                                rna_index = headers.index('id_rna')
                                for line in lines[1:]:
                                    values = line.split(';')
                                    if len(values) > rna_index:
                                        rna = values[rna_index]
                                        if rna:
                                            associations.add(rna)
                except:
                    pass
        
        return len(associations)
    
    def compter_fichiers_actuels(self):
        """Compte le nombre total de fichiers générés"""
        fichiers = 0
        
        # Fichiers JSON
        fichiers += len(glob.glob("data_asso_*.json"))
        
        # Fichiers CSV
        fichiers += len(glob.glob("data_asso_*.csv"))
        
        # Fichiers CSV téléchargés
        download_dir = "downloads_data_asso"
        if os.path.exists(download_dir):
            fichiers += len(glob.glob(os.path.join(download_dir, "*.csv")))
        
        return fichiers
    
    def analyser_log_scraper(self):
        """Analyse le fichier de log du scraper pour extraire les infos"""
        try:
            with open('scraper_selenium.log', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Chercher la dernière stratégie
            derniere_strategie = "N/A"
            total_strategies = 68
            strategie_actuelle = 0
            
            for line in reversed(lines):
                if "Stratégie" in line and "/" in line:
                    # Extraire "Stratégie X/Y"
                    import re
                    match = re.search(r'Stratégie (\d+)/(\d+)', line)
                    if match:
                        strategie_actuelle = int(match.group(1))
                        total_strategies = int(match.group(2))
                        # Extraire le terme de recherche
                        term_match = re.search(r"'([^']+)'", line)
                        if term_match:
                            derniere_strategie = term_match.group(1)
                        break
            
            return {
                'strategie_actuelle': strategie_actuelle,
                'total_strategies': total_strategies,
                'derniere_strategie': derniere_strategie,
                'progression': (strategie_actuelle / total_strategies * 100) if total_strategies > 0 else 0
            }
            
        except:
            return {
                'strategie_actuelle': 0,
                'total_strategies': 68,
                'derniere_strategie': "N/A",
                'progression': 0
            }
    
    def afficher_statistiques(self):
        """Affiche les statistiques actuelles"""
        maintenant = datetime.now()
        
        # Compter les associations et fichiers
        associations_actuelles = self.compter_associations_actuelles()
        fichiers_actuels = self.compter_fichiers_actuels()
        
        # Analyser le log
        info_scraper = self.analyser_log_scraper()
        
        # Calculer les différences
        nouvelles_associations = associations_actuelles - self.associations_precedentes
        nouveaux_fichiers = fichiers_actuels - self.fichiers_precedents
        
        # Calculer la vitesse
        temps_ecoule = (maintenant - self.derniere_verification).total_seconds()
        vitesse_associations = nouvelles_associations / (temps_ecoule / 60) if temps_ecoule > 0 else 0
        
        # Estimation temps restant
        strategies_restantes = info_scraper['total_strategies'] - info_scraper['strategie_actuelle']
        temps_restant_min = strategies_restantes * 2.5  # ~2.5 min par stratégie
        
        # Affichage
        print(f"\n🔍 SURVEILLANCE SCRAPER DATA-ASSO.FR")
        print(f"⏰ {maintenant.strftime('%H:%M:%S')}")
        print(f"=" * 60)
        
        print(f"📊 ASSOCIATIONS:")
        print(f"   Total unique: {associations_actuelles}")
        print(f"   Nouvelles (+{nouvelles_associations} depuis dernière vérif)")
        print(f"   Vitesse: {vitesse_associations:.1f} assoc/min")
        
        print(f"📄 FICHIERS:")
        print(f"   Total: {fichiers_actuels}")
        print(f"   Nouveaux: +{nouveaux_fichiers}")
        
        print(f"🎯 PROGRESSION SCRAPER:")
        print(f"   Stratégie: {info_scraper['strategie_actuelle']}/{info_scraper['total_strategies']}")
        print(f"   Actuelle: '{info_scraper['derniere_strategie']}'")
        print(f"   Progression: {info_scraper['progression']:.1f}%")
        print(f"   Temps restant estimé: {temps_restant_min:.0f} minutes")
        
        # Barre de progression
        barre_longueur = 40
        progression_barre = int(info_scraper['progression'] / 100 * barre_longueur)
        barre = "█" * progression_barre + "░" * (barre_longueur - progression_barre)
        print(f"   [{barre}] {info_scraper['progression']:.1f}%")
        
        # Mettre à jour les valeurs précédentes
        self.associations_precedentes = associations_actuelles
        self.fichiers_precedents = fichiers_actuels
        self.derniere_verification = maintenant
    
    def surveiller_en_continu(self, intervalle=30):
        """Surveille en continu avec un intervalle donné (en secondes)"""
        print("🚀 SURVEILLANCE EN TEMPS RÉEL DÉMARRÉE")
        print(f"Actualisation toutes les {intervalle} secondes")
        print("Appuyez sur Ctrl+C pour arrêter")
        print("=" * 60)
        
        try:
            while True:
                self.afficher_statistiques()
                time.sleep(intervalle)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Surveillance arrêtée par l'utilisateur")
        except Exception as e:
            print(f"\n❌ Erreur surveillance: {e}")
    
    def afficher_resume_final(self):
        """Affiche un résumé final quand le scraper est terminé"""
        associations_finales = self.compter_associations_actuelles()
        fichiers_finaux = self.compter_fichiers_actuels()
        
        print(f"\n🎉 SCRAPING TERMINÉ - RÉSUMÉ FINAL")
        print(f"=" * 60)
        print(f"📊 Total associations uniques: {associations_finales}")
        print(f"📄 Total fichiers générés: {fichiers_finaux}")
        print(f"⏰ Fin: {datetime.now().strftime('%H:%M:%S')}")
        
        # Lister les fichiers générés
        print(f"\n📁 FICHIERS GÉNÉRÉS:")
        
        json_files = glob.glob("data_asso_*.json")
        for f in json_files:
            size = os.path.getsize(f) / 1024  # KB
            print(f"   📄 {f} ({size:.1f} KB)")
        
        csv_files = glob.glob("data_asso_*.csv")
        for f in csv_files:
            size = os.path.getsize(f) / 1024  # KB
            print(f"   📊 {f} ({size:.1f} KB)")
        
        download_dir = "downloads_data_asso"
        if os.path.exists(download_dir):
            csv_downloads = glob.glob(os.path.join(download_dir, "*.csv"))
            for f in csv_downloads:
                size = os.path.getsize(f) / 1024  # KB
                filename = os.path.basename(f)
                print(f"   📥 {filename} ({size:.1f} KB)")
        
        print(f"\n💡 PROCHAINES ÉTAPES:")
        print(f"   1. Lancer l'analyseur: python analyseur_resultats_data_asso.py")
        print(f"   2. Consulter les fichiers Excel générés")
        print(f"   3. Utiliser les données pour votre projet")

def main():
    """Fonction principale"""
    print("🔍 SURVEILLANCE SCRAPER DATA-ASSO.FR")
    print("=" * 60)
    print("Surveille les fichiers générés et affiche les stats en temps réel")
    print("=" * 60)
    print()
    
    surveillance = SurveillanceScraper()
    
    print("Choisissez une option:")
    print("1. Surveillance en continu (30s)")
    print("2. Surveillance rapide (10s)")
    print("3. Vérification unique")
    print("4. Résumé final")
    
    choix = input("\nVotre choix (1-4): ").strip()
    
    if choix == "1":
        surveillance.surveiller_en_continu(30)
    elif choix == "2":
        surveillance.surveiller_en_continu(10)
    elif choix == "3":
        surveillance.afficher_statistiques()
    elif choix == "4":
        surveillance.afficher_resume_final()
    else:
        print("Choix invalide")

if __name__ == "__main__":
    main()
