import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('manual_observation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ManualScrollObserver:
    def __init__(self):
        """
        Observateur qui surveille les changements pendant que vous scrollez manuellement
        """
        self.options = Options()
        # Mode visible pour que vous puissiez scroller manuellement
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Activer les logs réseau
        self.options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        self.driver = None
        self.observation_data = []
        self.all_rnas = set()
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            logging.info("✅ Driver démarré - VOUS POUVEZ MAINTENANT SCROLLER MANUELLEMENT")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def get_current_state(self):
        """
        Récupère l'état actuel de la page
        """
        try:
            # Informations de scroll
            scroll_info = {
                'scroll_y': self.driver.execute_script("return window.pageYOffset"),
                'scroll_height': self.driver.execute_script("return document.body.scrollHeight"),
                'viewport_height': self.driver.execute_script("return window.innerHeight"),
                'timestamp': time.time()
            }
            
            # Extraire les associations
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            current_associations = []
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        current_associations.append({
                            'rna': rna,
                            'text': link.get_text(strip=True)[:100]
                        })
            
            # Compter les nouvelles associations
            new_rnas = []
            for assoc in current_associations:
                if assoc['rna'] not in self.all_rnas:
                    self.all_rnas.add(assoc['rna'])
                    new_rnas.append(assoc['rna'])
            
            # Requêtes réseau récentes
            try:
                logs = self.driver.get_log('performance')
                recent_requests = []
                for log in logs:
                    try:
                        message = json.loads(log['message'])
                        if message['message']['method'] == 'Network.responseReceived':
                            response = message['message']['params']['response']
                            url = response['url']
                            if 'data-asso' in url and any(keyword in url.lower() for keyword in ['api', 'search', 'load', 'ajax']):
                                recent_requests.append(url)
                    except:
                        continue
            except:
                recent_requests = []
            
            state = {
                'scroll_info': scroll_info,
                'total_associations': len(current_associations),
                'unique_associations': len(self.all_rnas),
                'new_associations': new_rnas,
                'new_associations_count': len(new_rnas),
                'recent_network_requests': recent_requests,
                'page_title': self.driver.title,
                'current_url': self.driver.current_url
            }
            
            return state
            
        except Exception as e:
            logging.error(f"Erreur état: {e}")
            return None
    
    def observe_manual_scrolling(self, duration_minutes=10):
        """
        Observe les changements pendant que vous scrollez manuellement
        """
        logging.info("👀 OBSERVATION DU SCROLL MANUEL")
        logging.info(f"⏱️ Durée d'observation: {duration_minutes} minutes")
        logging.info("🖱️ VOUS POUVEZ MAINTENANT SCROLLER MANUELLEMENT DANS LA FENÊTRE DU NAVIGATEUR")
        
        # Aller sur la page
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🌐 Ouverture de {url}")
        self.driver.get(url)
        time.sleep(5)
        
        # État initial
        initial_state = self.get_current_state()
        if initial_state:
            self.observation_data.append(initial_state)
            logging.info(f"📊 État initial: {initial_state['total_associations']} associations")
        
        # Observer pendant la durée spécifiée
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        observation_count = 0
        last_association_count = initial_state['total_associations'] if initial_state else 0
        
        print(f"\n🖱️ SCROLLEZ MAINTENANT DANS LA FENÊTRE DU NAVIGATEUR!")
        print(f"⏱️ Observation pendant {duration_minutes} minutes...")
        print("📊 Je vais afficher les changements en temps réel:")
        print()
        
        while time.time() < end_time:
            time.sleep(2)  # Observer toutes les 2 secondes
            
            current_state = self.get_current_state()
            if current_state:
                observation_count += 1
                self.observation_data.append(current_state)
                
                # Afficher les changements significatifs
                if current_state['new_associations_count'] > 0:
                    print(f"🎯 NOUVELLES ASSOCIATIONS DÉTECTÉES!")
                    print(f"   📊 Total: {last_association_count} -> {current_state['unique_associations']}")
                    print(f"   🆕 Nouvelles: {current_state['new_associations']}")
                    print(f"   📍 Position scroll: {current_state['scroll_info']['scroll_y']}px")
                    print(f"   📏 Hauteur page: {current_state['scroll_info']['scroll_height']}px")
                    print()
                    
                    last_association_count = current_state['unique_associations']
                
                # Afficher les requêtes réseau
                if current_state['recent_network_requests']:
                    print(f"📡 REQUÊTES RÉSEAU DÉTECTÉES:")
                    for req in current_state['recent_network_requests']:
                        print(f"   🌐 {req}")
                    print()
                
                # Afficher le progrès toutes les 30 secondes
                if observation_count % 15 == 0:  # 15 * 2 secondes = 30 secondes
                    elapsed = (time.time() - start_time) / 60
                    remaining = duration_minutes - elapsed
                    print(f"⏱️ Temps écoulé: {elapsed:.1f}min, Restant: {remaining:.1f}min")
                    print(f"📊 Associations actuelles: {current_state['unique_associations']}")
                    print(f"📍 Position: {current_state['scroll_info']['scroll_y']}px")
                    print()
        
        # Résultats finaux
        final_state = self.get_current_state()
        
        results = {
            'duration_minutes': duration_minutes,
            'total_observations': len(self.observation_data),
            'initial_associations': initial_state['total_associations'] if initial_state else 0,
            'final_associations': final_state['unique_associations'] if final_state else 0,
            'new_associations_found': (final_state['unique_associations'] if final_state else 0) - (initial_state['total_associations'] if initial_state else 0),
            'scroll_infinite_confirmed': False,
            'all_observations': self.observation_data,
            'unique_rnas_found': list(self.all_rnas)
        }
        
        if results['new_associations_found'] > 0:
            results['scroll_infinite_confirmed'] = True
            logging.info(f"🎉 SCROLL INFINI CONFIRMÉ!")
            logging.info(f"   📊 {results['new_associations_found']} nouvelles associations trouvées")
        else:
            logging.info(f"⚠️ Pas de nouvelles associations détectées")
        
        return results
    
    def save_observation_results(self, results):
        """
        Sauvegarde les résultats d'observation
        """
        with open('manual_scroll_observation.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # Sauvegarder la liste des RNAs
        if results.get('unique_rnas_found'):
            with open('manual_scroll_rnas.txt', 'w', encoding='utf-8') as f:
                for rna in results['unique_rnas_found']:
                    f.write(f"{rna}\n")
        
        logging.info("💾 Résultats d'observation sauvegardés:")
        logging.info("   📄 manual_scroll_observation.json")
        logging.info("   📋 manual_scroll_rnas.txt")

def main():
    """
    Observation principale du scroll manuel
    """
    print("👀 OBSERVATEUR DE SCROLL MANUEL")
    print("=" * 60)
    print("Ce script va ouvrir le navigateur et observer en temps réel")
    print("ce qui se passe pendant que VOUS scrollez manuellement.")
    print()
    print("Instructions:")
    print("1. Le script va ouvrir data-asso.fr/annuaire")
    print("2. Vous pourrez scroller manuellement dans la fenêtre")
    print("3. Le script détectera automatiquement les nouvelles associations")
    print("4. Les résultats seront affichés en temps réel")
    print()
    
    duration = input("Durée d'observation en minutes (défaut: 5): ").strip()
    try:
        duration = int(duration) if duration else 5
    except:
        duration = 5
    
    print(f"\n🚀 Démarrage de l'observation pour {duration} minutes...")
    
    observer = ManualScrollObserver()
    
    try:
        observer.start_driver()
        
        # Observer le scroll manuel
        results = observer.observe_manual_scrolling(duration_minutes=duration)
        
        # Sauvegarder
        observer.save_observation_results(results)
        
        # Afficher le résumé final
        print(f"\n🎉 OBSERVATION TERMINÉE!")
        print(f"   ⏱️ Durée: {results['duration_minutes']} minutes")
        print(f"   📊 Observations: {results['total_observations']}")
        print(f"   🏢 Associations initiales: {results['initial_associations']}")
        print(f"   🏢 Associations finales: {results['final_associations']}")
        print(f"   🆕 Nouvelles associations: {results['new_associations_found']}")
        print(f"   ✅ Scroll infini: {'CONFIRMÉ' if results['scroll_infinite_confirmed'] else 'NON DÉTECTÉ'}")
        
        if results['scroll_infinite_confirmed']:
            print(f"\n🎯 SCROLL INFINI DÉTECTÉ!")
            print(f"   📋 Total RNAs uniques: {len(results.get('unique_rnas_found', []))}")
            
            # Afficher quelques exemples
            if results.get('unique_rnas_found'):
                print(f"\n📋 EXEMPLES DE RNAs:")
                for i, rna in enumerate(results['unique_rnas_found'][:10]):
                    print(f"   {i+1}. {rna}")
        
        print(f"\n💾 Résultats détaillés sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        observer.close_driver()

if __name__ == "__main__":
    main()
