import requests
import json
import csv
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import logging
import re

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class UltraFastAPIScraper:
    def __init__(self):
        """
        Scraper ultra-rapide utilisant directement l'API de data-asso.fr
        """
        self.session = requests.Session()
        
        # Headers pour imiter un navigateur
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.data-asso.fr/annuaire',
            'Origin': 'https://www.data-asso.fr',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        
        self.session.headers.update(self.headers)
        
        # URLs API découvertes
        self.api_base = "https://www.data-asso.fr/gw/api-server"
        self.search_api = f"{self.api_base}/search"
        self.referentiels_api = f"{self.api_base}/referentiels"
        
        # Statistiques
        self.stats = {
            'total_found': 0,
            'total_processed': 0,
            'emails_found': 0,
            'phones_found': 0,
            'start_time': None,
            'end_time': None
        }
    
    def test_api_endpoints(self):
        """
        Teste les différents endpoints API pour comprendre leur fonctionnement
        """
        logging.info("🔍 Test des endpoints API...")
        
        endpoints_to_test = [
            # API de recherche avec différents paramètres
            (self.search_api, {}),
            (self.search_api, {'q': ''}),
            (self.search_api, {'query': ''}),
            (self.search_api, {'search': ''}),
            (self.search_api, {'limit': 100}),
            (self.search_api, {'size': 100}),
            (self.search_api, {'page': 1}),
            (self.search_api, {'offset': 0}),
            
            # API des référentiels
            (self.referentiels_api, {}),
            (self.referentiels_api, {'type': ''}),
            (self.referentiels_api, {'type': 'association'}),
            (self.referentiels_api, {'type': 'all'}),
        ]
        
        successful_endpoints = []
        
        for url, params in endpoints_to_test:
            try:
                logging.info(f"🌐 Test: {url} avec params {params}")
                
                # Test GET
                response = self.session.get(url, params=params, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logging.info(f"   ✅ GET réussi: {len(str(data))} caractères")
                        
                        # Analyser la structure des données
                        if isinstance(data, dict):
                            if 'associations' in data or 'results' in data or 'data' in data:
                                logging.info(f"   🎯 Données d'associations potentielles trouvées!")
                                successful_endpoints.append((url, params, 'GET', data))
                        elif isinstance(data, list) and len(data) > 0:
                            logging.info(f"   📋 Liste de {len(data)} éléments trouvée")
                            successful_endpoints.append((url, params, 'GET', data))
                            
                    except json.JSONDecodeError:
                        logging.info(f"   ⚠️ Réponse non-JSON: {response.text[:100]}")
                else:
                    logging.info(f"   ❌ Status: {response.status_code}")
                
                # Test POST si GET échoue
                if response.status_code != 200:
                    post_response = self.session.post(url, json=params, timeout=10)
                    if post_response.status_code == 200:
                        try:
                            data = post_response.json()
                            logging.info(f"   ✅ POST réussi: {len(str(data))} caractères")
                            successful_endpoints.append((url, params, 'POST', data))
                        except json.JSONDecodeError:
                            pass
                
                time.sleep(0.5)  # Pause respectueuse
                
            except Exception as e:
                logging.debug(f"   ❌ Erreur {url}: {e}")
        
        return successful_endpoints
    
    def extract_associations_from_api_response(self, data):
        """
        Extrait les associations depuis une réponse API
        """
        associations = []
        
        def extract_recursive(obj, path=""):
            """Recherche récursive d'associations dans la structure JSON"""
            if isinstance(obj, dict):
                # Chercher des clés qui pourraient contenir des associations
                for key, value in obj.items():
                    if any(keyword in key.lower() for keyword in ['association', 'result', 'data', 'item', 'record']):
                        if isinstance(value, list):
                            for item in value:
                                if isinstance(item, dict) and self.looks_like_association(item):
                                    associations.append(item)
                        elif isinstance(value, dict) and self.looks_like_association(value):
                            associations.append(value)
                    
                    # Continuer la recherche récursive
                    extract_recursive(value, f"{path}.{key}")
                    
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, dict) and self.looks_like_association(item):
                        associations.append(item)
                    else:
                        extract_recursive(item, f"{path}[{i}]")
        
        extract_recursive(data)
        return associations
    
    def looks_like_association(self, obj):
        """
        Détermine si un objet ressemble à une association
        """
        if not isinstance(obj, dict):
            return False
        
        # Chercher des champs typiques d'une association
        association_fields = [
            'rna', 'siren', 'nom', 'name', 'title', 'denomination',
            'adresse', 'address', 'telephone', 'phone', 'email', 'mail',
            'association', 'id'
        ]
        
        found_fields = sum(1 for field in association_fields if any(key.lower().find(field) != -1 for key in obj.keys()))
        
        # Si on trouve au moins 2 champs typiques, c'est probablement une association
        return found_fields >= 2
    
    def search_all_associations(self):
        """
        Recherche toutes les associations via l'API
        """
        logging.info("🔍 Recherche de toutes les associations via API...")
        
        self.stats['start_time'] = time.time()
        
        # 1. Tester les endpoints
        successful_endpoints = self.test_api_endpoints()
        
        if not successful_endpoints:
            logging.error("❌ Aucun endpoint API fonctionnel trouvé")
            return []
        
        # 2. Extraire les associations de tous les endpoints réussis
        all_associations = []
        
        for url, params, method, data in successful_endpoints:
            logging.info(f"📊 Extraction depuis {url} ({method})")
            associations = self.extract_associations_from_api_response(data)
            
            if associations:
                logging.info(f"   🎯 {len(associations)} associations trouvées")
                all_associations.extend(associations)
            else:
                logging.info("   ⚠️ Aucune association dans cette réponse")
        
        # 3. Supprimer les doublons
        unique_associations = []
        seen_ids = set()
        
        for assoc in all_associations:
            # Utiliser RNA ou un autre identifiant unique
            assoc_id = assoc.get('rna') or assoc.get('id') or assoc.get('siren') or str(assoc)
            if assoc_id not in seen_ids:
                seen_ids.add(assoc_id)
                unique_associations.append(assoc)
        
        self.stats['total_found'] = len(unique_associations)
        logging.info(f"🎉 Total unique: {len(unique_associations)} associations")
        
        return unique_associations
    
    def process_association_data(self, raw_association):
        """
        Traite et normalise les données d'une association depuis l'API
        """
        try:
            # Structure de données normalisée
            data = {
                'nom': '',
                'rna': '',
                'siren': '',
                'adresse': '',
                'adresse_gestion': '',
                'telephone': '',
                'email': '',
                'site_web': '',
                'ville': '',
                'code_postal': '',
                'statut': 'API',
                'source': 'API',
                'raw_data': raw_association
            }
            
            # Mapping des champs possibles
            field_mappings = {
                'nom': ['nom', 'name', 'title', 'denomination', 'libelle'],
                'rna': ['rna', 'numero_rna', 'rna_number'],
                'siren': ['siren', 'numero_siren', 'siren_number'],
                'adresse': ['adresse', 'address', 'adresse_siege'],
                'adresse_gestion': ['adresse_gestion', 'adresse_correspondance'],
                'telephone': ['telephone', 'phone', 'tel'],
                'email': ['email', 'mail', 'courriel', 'e_mail'],
                'site_web': ['site_web', 'website', 'url', 'site_internet']
            }
            
            # Extraire les données avec mapping flexible
            for target_field, possible_keys in field_mappings.items():
                for key in possible_keys:
                    # Recherche insensible à la casse
                    for raw_key, raw_value in raw_association.items():
                        if key.lower() in raw_key.lower() and raw_value:
                            data[target_field] = str(raw_value).strip()
                            break
                    if data[target_field]:
                        break
            
            # Extraire ville et code postal depuis l'adresse
            if data['adresse']:
                postal_match = re.search(r'(\d{5})\s*([A-Z\s\-À-ÿ]+)', data['adresse'], re.IGNORECASE)
                if postal_match:
                    data['code_postal'] = postal_match.group(1)
                    data['ville'] = postal_match.group(2).strip()
            
            # Mettre à jour les statistiques
            if data['email']:
                self.stats['emails_found'] += 1
            if data['telephone']:
                self.stats['phones_found'] += 1
            
            self.stats['total_processed'] += 1
            
            return data
            
        except Exception as e:
            logging.error(f"❌ Erreur traitement association: {e}")
            return None
    
    def scrape_all_via_api(self):
        """
        Scraping complet via API
        """
        logging.info("🚀 SCRAPING ULTRA-RAPIDE VIA API")
        
        # 1. Rechercher toutes les associations
        raw_associations = self.search_all_associations()
        
        if not raw_associations:
            logging.error("❌ Aucune association trouvée via API")
            return []
        
        # 2. Traiter toutes les associations
        logging.info(f"⚡ Traitement de {len(raw_associations)} associations...")
        
        processed_associations = []
        for i, raw_assoc in enumerate(raw_associations):
            processed = self.process_association_data(raw_assoc)
            if processed:
                processed_associations.append(processed)
            
            if (i + 1) % 100 == 0:
                logging.info(f"📊 Traité: {i+1}/{len(raw_associations)}")
        
        self.stats['end_time'] = time.time()
        
        return processed_associations
    
    def save_results(self, data, filename_base='associations_api'):
        """
        Sauvegarde les résultats
        """
        if not data:
            logging.warning("⚠️ Aucune donnée à sauvegarder")
            return
        
        # JSON
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # CSV
        csv_file = f"{filename_base}.csv"
        fieldnames = ['nom', 'rna', 'siren', 'adresse', 'adresse_gestion', 
                     'telephone', 'email', 'site_web', 'ville', 'code_postal', 
                     'statut', 'source']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in data:
                # Exclure raw_data du CSV
                csv_item = {k: v for k, v in item.items() if k != 'raw_data'}
                writer.writerow(csv_item)
        
        logging.info(f"💾 Sauvegardé: {json_file} et {csv_file}")
    
    def print_stats(self):
        """
        Affiche les statistiques
        """
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            
            print(f"\n📊 STATISTIQUES API:")
            print(f"   ⏱️ Durée: {duration:.1f} secondes")
            print(f"   🏢 Associations trouvées: {self.stats['total_found']}")
            print(f"   ✅ Associations traitées: {self.stats['total_processed']}")
            print(f"   📧 Emails: {self.stats['emails_found']}")
            print(f"   📞 Téléphones: {self.stats['phones_found']}")
            
            if self.stats['total_processed'] > 0:
                rate = self.stats['total_processed'] / duration
                print(f"   🚀 Vitesse: {rate:.1f} associations/seconde")

def main():
    """
    Scraper principal via API
    """
    print("⚡ SCRAPER API ULTRA-RAPIDE DATA-ASSO")
    print("=" * 60)
    
    scraper = UltraFastAPIScraper()
    
    try:
        # Scraping via API
        results = scraper.scrape_all_via_api()
        
        if results:
            print(f"\n🎉 {len(results)} associations extraites via API!")
            
            # Sauvegarder
            scraper.save_results(results)
            
            # Afficher quelques exemples
            print(f"\n📋 EXEMPLES:")
            for i, assoc in enumerate(results[:5]):
                print(f"\n{i+1}. {assoc.get('nom', 'N/A')}")
                print(f"   🏢 RNA: {assoc.get('rna', 'N/A')}")
                print(f"   📞 Téléphone: {assoc.get('telephone', 'N/A')}")
                print(f"   📧 Email: {assoc.get('email', 'N/A')}")
        
        # Statistiques
        scraper.print_stats()
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
