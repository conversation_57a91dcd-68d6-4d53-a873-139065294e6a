#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VÉRIFICATEUR DE CONTACTS ASSOCIATIONS
Vérifie combien d'associations ont des emails ou téléphones
"""

import pandas as pd
import re
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)

def analyser_contacts_excel():
    """Analyse les contacts dans le fichier Excel"""
    print("📧 ANALYSE DES CONTACTS DANS LE FICHIER EXCEL")
    print("=" * 60)
    
    try:
        # Charger le fichier Excel
        df = pd.read_excel('Associations_Francaises_Complet.xlsx', sheet_name='Toutes_Associations')
        
        print(f"📊 Total associations: {len(df)}")
        
        # Vérifier les colonnes disponibles
        print(f"\n📋 Colonnes disponibles:")
        for col in df.columns:
            print(f"   - {col}")
        
        # Analyser les emails
        if 'Email' in df.columns:
            emails_non_vides = df['Email'].notna() & (df['Email'] != '')
            nb_emails = emails_non_vides.sum()
            
            print(f"\n📧 EMAILS:")
            print(f"   📊 Associations avec email: {nb_emails}")
            print(f"   📊 Pourcentage: {nb_emails/len(df)*100:.2f}%")
            
            if nb_emails > 0:
                print(f"\n📧 Exemples d'emails trouvés:")
                emails_exemples = df[emails_non_vides]['Email'].head(10)
                for i, email in enumerate(emails_exemples, 1):
                    print(f"   {i}. {email}")
        else:
            print(f"\n❌ Colonne 'Email' non trouvée")
            nb_emails = 0
        
        # Analyser les téléphones
        if 'Telephone' in df.columns:
            telephones_non_vides = df['Telephone'].notna() & (df['Telephone'] != '')
            nb_telephones = telephones_non_vides.sum()
            
            print(f"\n📞 TÉLÉPHONES:")
            print(f"   📊 Associations avec téléphone: {nb_telephones}")
            print(f"   📊 Pourcentage: {nb_telephones/len(df)*100:.2f}%")
            
            if nb_telephones > 0:
                print(f"\n📞 Exemples de téléphones trouvés:")
                telephones_exemples = df[telephones_non_vides]['Telephone'].head(10)
                for i, tel in enumerate(telephones_exemples, 1):
                    print(f"   {i}. {tel}")
        else:
            print(f"\n❌ Colonne 'Telephone' non trouvée")
            nb_telephones = 0
        
        # Analyser les associations avec les deux
        if 'Email' in df.columns and 'Telephone' in df.columns:
            avec_les_deux = emails_non_vides & telephones_non_vides
            nb_les_deux = avec_les_deux.sum()
            
            print(f"\n🎯 ASSOCIATIONS AVEC EMAIL ET TÉLÉPHONE:")
            print(f"   📊 Avec les deux: {nb_les_deux}")
            print(f"   📊 Pourcentage: {nb_les_deux/len(df)*100:.2f}%")
        else:
            nb_les_deux = 0
        
        # Analyser les associations avec au moins un contact
        if 'Email' in df.columns and 'Telephone' in df.columns:
            avec_contact = emails_non_vides | telephones_non_vides
            nb_avec_contact = avec_contact.sum()
            
            print(f"\n📧 ASSOCIATIONS AVEC AU MOINS UN CONTACT:")
            print(f"   📊 Avec email OU téléphone: {nb_avec_contact}")
            print(f"   📊 Pourcentage: {nb_avec_contact/len(df)*100:.2f}%")
        else:
            nb_avec_contact = max(nb_emails, nb_telephones)
        
        # Analyser par source
        print(f"\n📊 RÉPARTITION DES CONTACTS PAR SOURCE:")
        if 'Source' in df.columns:
            for source in df['Source'].unique():
                df_source = df[df['Source'] == source]
                
                if 'Email' in df.columns:
                    emails_source = (df_source['Email'].notna() & (df_source['Email'] != '')).sum()
                else:
                    emails_source = 0
                
                if 'Telephone' in df.columns:
                    tel_source = (df_source['Telephone'].notna() & (df_source['Telephone'] != '')).sum()
                else:
                    tel_source = 0
                
                print(f"   📊 {source}:")
                print(f"      📧 Emails: {emails_source}")
                print(f"      📞 Téléphones: {tel_source}")
                print(f"      📊 Total: {len(df_source)}")
        
        return {
            'total': len(df),
            'avec_email': nb_emails,
            'avec_telephone': nb_telephones,
            'avec_les_deux': nb_les_deux,
            'avec_contact': nb_avec_contact
        }
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def chercher_contacts_dans_noms():
    """Cherche des contacts cachés dans les noms d'associations"""
    print(f"\n🔍 RECHERCHE DE CONTACTS CACHÉS DANS LES NOMS")
    print("=" * 60)
    
    try:
        # Charger le fichier Excel
        df = pd.read_excel('Associations_Francaises_Complet.xlsx', sheet_name='Toutes_Associations')
        
        # Patterns pour emails et téléphones
        email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        phone_patterns = [
            re.compile(r'0[1-9](?:[0-9]{8})'),
            re.compile(r'\+33[1-9](?:[0-9]{8})'),
            re.compile(r'(?:0[1-9])(?:[-.\s]?[0-9]{2}){4}'),
        ]
        
        contacts_trouves = []
        
        for idx, row in df.iterrows():
            nom = str(row.get('Nom_Association', ''))
            rna = row.get('RNA', '')
            
            # Chercher des emails dans le nom
            emails = email_pattern.findall(nom)
            
            # Chercher des téléphones dans le nom
            phones = []
            for pattern in phone_patterns:
                phones.extend(pattern.findall(nom))
            
            if emails or phones:
                contacts_trouves.append({
                    'rna': rna,
                    'nom': nom[:100],
                    'emails': emails,
                    'telephones': phones
                })
        
        print(f"📊 Contacts trouvés dans les noms: {len(contacts_trouves)}")
        
        if contacts_trouves:
            print(f"\n📧 EXEMPLES DE CONTACTS TROUVÉS:")
            for i, contact in enumerate(contacts_trouves[:10], 1):
                print(f"\n   {i}. RNA: {contact['rna']}")
                print(f"      Nom: {contact['nom']}...")
                if contact['emails']:
                    print(f"      📧 Emails: {', '.join(contact['emails'])}")
                if contact['telephones']:
                    print(f"      📞 Téléphones: {', '.join(contact['telephones'])}")
        
        return contacts_trouves
        
    except Exception as e:
        print(f"❌ Erreur recherche contacts: {e}")
        return []

def analyser_fichiers_contacts_existants():
    """Analyse les fichiers de contacts existants"""
    print(f"\n📄 ANALYSE DES FICHIERS DE CONTACTS EXISTANTS")
    print("=" * 60)
    
    fichiers_contacts = [
        'contacts_sources_specialisees.csv',
        'associations_avec_contacts.csv',
        'associations_emails.csv',
        'associations_telephones.csv'
    ]
    
    total_contacts = 0
    
    for fichier in fichiers_contacts:
        try:
            print(f"\n📄 Fichier: {fichier}")
            
            if fichier.endswith('.csv'):
                df = pd.read_csv(fichier)
            else:
                continue
            
            print(f"   📊 Total lignes: {len(df)}")
            
            # Analyser les colonnes
            if 'email' in df.columns:
                emails = (df['email'].notna() & (df['email'] != '')).sum()
                print(f"   📧 Avec email: {emails}")
            
            if 'telephone' in df.columns:
                telephones = (df['telephone'].notna() & (df['telephone'] != '')).sum()
                print(f"   📞 Avec téléphone: {telephones}")
            
            total_contacts += len(df)
            
        except FileNotFoundError:
            print(f"   ⚠️ Fichier {fichier} non trouvé")
        except Exception as e:
            print(f"   ❌ Erreur {fichier}: {e}")
    
    print(f"\n📊 Total contacts dans fichiers séparés: {total_contacts}")
    return total_contacts

def creer_fichier_contacts_complet():
    """Crée un fichier avec tous les contacts trouvés"""
    print(f"\n📊 CRÉATION FICHIER CONTACTS COMPLET")
    print("=" * 50)
    
    try:
        # Charger le fichier principal
        df = pd.read_excel('Associations_Francaises_Complet.xlsx', sheet_name='Toutes_Associations')
        
        # Filtrer les associations avec contacts
        if 'Email' in df.columns and 'Telephone' in df.columns:
            avec_contact = (df['Email'].notna() & (df['Email'] != '')) | (df['Telephone'].notna() & (df['Telephone'] != ''))
            df_contacts = df[avec_contact].copy()
        elif 'Email' in df.columns:
            avec_contact = df['Email'].notna() & (df['Email'] != '')
            df_contacts = df[avec_contact].copy()
        elif 'Telephone' in df.columns:
            avec_contact = df['Telephone'].notna() & (df['Telephone'] != '')
            df_contacts = df[avec_contact].copy()
        else:
            print("❌ Aucune colonne de contact trouvée")
            return 0
        
        if len(df_contacts) > 0:
            # Sauvegarder
            df_contacts.to_csv('Associations_Avec_Contacts_Complet.csv', index=False, encoding='utf-8')
            
            print(f"✅ Fichier créé: Associations_Avec_Contacts_Complet.csv")
            print(f"📊 {len(df_contacts)} associations avec contacts")
            
            return len(df_contacts)
        else:
            print("❌ Aucune association avec contact trouvée")
            return 0
        
    except Exception as e:
        print(f"❌ Erreur création fichier: {e}")
        return 0

def main():
    """Fonction principale"""
    print("📧 VÉRIFICATEUR DE CONTACTS ASSOCIATIONS")
    print("=" * 60)
    
    # Analyser les contacts dans Excel
    stats_contacts = analyser_contacts_excel()
    
    # Chercher des contacts cachés dans les noms
    contacts_caches = chercher_contacts_dans_noms()
    
    # Analyser les fichiers de contacts existants
    total_fichiers_contacts = analyser_fichiers_contacts_existants()
    
    # Créer un fichier contacts complet
    nb_contacts_complet = creer_fichier_contacts_complet()
    
    # Résumé final
    print(f"\n📊 RÉSUMÉ FINAL DES CONTACTS:")
    print("=" * 50)
    
    if stats_contacts:
        print(f"📊 Total associations: {stats_contacts['total']:,}")
        print(f"📧 Avec email: {stats_contacts['avec_email']:,} ({stats_contacts['avec_email']/stats_contacts['total']*100:.2f}%)")
        print(f"📞 Avec téléphone: {stats_contacts['avec_telephone']:,} ({stats_contacts['avec_telephone']/stats_contacts['total']*100:.2f}%)")
        print(f"🎯 Avec les deux: {stats_contacts['avec_les_deux']:,} ({stats_contacts['avec_les_deux']/stats_contacts['total']*100:.2f}%)")
        print(f"📧 Avec au moins un contact: {stats_contacts['avec_contact']:,} ({stats_contacts['avec_contact']/stats_contacts['total']*100:.2f}%)")
    
    print(f"🔍 Contacts cachés dans noms: {len(contacts_caches)}")
    print(f"📄 Contacts fichiers séparés: {total_fichiers_contacts}")
    
    if nb_contacts_complet > 0:
        print(f"✅ Fichier contacts complet créé: {nb_contacts_complet} associations")

if __name__ == "__main__":
    main()
