import time
import json
import csv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import itertools

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('systematic_search.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class SystematicSearchScraper:
    def __init__(self):
        """
        Scraper par recherche systématique avec des milliers de termes
        """
        self.options = Options()
        self.options.add_argument('--headless')  # Mode invisible pour la vitesse
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        
        # Désactiver les images pour aller plus vite
        prefs = {"profile.managed_default_content_settings.images": 2}
        self.options.add_experimental_option("prefs", prefs)
        
        self.driver = None
        self.wait = None
        self.all_associations = {}  # Dict pour éviter doublons
        
        # Termes de recherche systématiques
        self.search_terms = self.generate_search_terms()
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 15)
            logging.info("✅ Driver de recherche systématique démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def generate_search_terms(self):
        """
        Génère des milliers de termes de recherche
        """
        logging.info("📝 Génération des termes de recherche...")
        
        # Catégories d'activités
        activities = [
            'sport', 'culture', 'education', 'social', 'sante', 'environnement',
            'humanitaire', 'jeunesse', 'seniors', 'famille', 'femmes', 'enfants',
            'handicap', 'insertion', 'formation', 'emploi', 'logement', 'justice',
            'droits', 'citoyennete', 'democratie', 'paix', 'solidarite', 'entraide',
            'benevolat', 'volontariat', 'cooperation', 'developpement', 'recherche',
            'innovation', 'technologie', 'numerique', 'communication', 'medias',
            'arts', 'musique', 'theatre', 'danse', 'cinema', 'litterature',
            'patrimoine', 'histoire', 'memoire', 'tradition', 'folklore',
            'football', 'tennis', 'basketball', 'volleyball', 'handball',
            'natation', 'athletisme', 'cyclisme', 'randonnee', 'escalade',
            'yoga', 'fitness', 'danse', 'martial', 'equitation', 'voile'
        ]
        
        # Villes françaises
        cities = [
            'paris', 'marseille', 'lyon', 'toulouse', 'nice', 'nantes',
            'montpellier', 'strasbourg', 'bordeaux', 'lille', 'rennes',
            'reims', 'saint-etienne', 'toulon', 'grenoble', 'dijon',
            'angers', 'nimes', 'villeurbanne', 'clermont-ferrand',
            'aix-en-provence', 'brest', 'limoges', 'tours', 'amiens',
            'perpignan', 'metz', 'besancon', 'orleans', 'rouen',
            'mulhouse', 'caen', 'nancy', 'saint-denis', 'argenteuil'
        ]
        
        # Départements
        departments = [f"{i:02d}" for i in range(1, 96)] + ['971', '972', '973', '974', '975']
        
        # Types d'associations
        types = [
            'association', 'club', 'federation', 'union', 'comite', 'groupe',
            'collectif', 'reseau', 'fondation', 'oeuvre', 'societe', 'amicale',
            'cercle', 'ligue', 'mouvement', 'organisation', 'structure'
        ]
        
        # Mots-clés génériques
        keywords = [
            'aide', 'soutien', 'accompagnement', 'prevention', 'promotion',
            'defense', 'protection', 'sauvegarde', 'preservation', 'valorisation',
            'developpement', 'creation', 'innovation', 'recherche', 'etude',
            'formation', 'education', 'sensibilisation', 'information',
            'animation', 'organisation', 'coordination', 'gestion'
        ]
        
        search_terms = []
        
        # Termes simples
        search_terms.extend(activities)
        search_terms.extend(cities)
        search_terms.extend(types)
        search_terms.extend(keywords)
        
        # Combinaisons activité + ville
        for activity in activities[:20]:  # Limiter pour éviter trop de combinaisons
            for city in cities[:20]:
                search_terms.append(f"{activity} {city}")
        
        # Combinaisons type + activité
        for type_assoc in types[:10]:
            for activity in activities[:20]:
                search_terms.append(f"{type_assoc} {activity}")
        
        # Termes avec départements
        for dept in departments[:20]:  # Limiter
            search_terms.append(dept)
        
        # Alphabet (pour trouver des associations par première lettre)
        alphabet = 'abcdefghijklmnopqrstuvwxyz'
        search_terms.extend(list(alphabet))
        
        # Combinaisons de 2 lettres
        for a, b in itertools.product(alphabet[:10], repeat=2):
            search_terms.append(f"{a}{b}")
        
        logging.info(f"📊 {len(search_terms)} termes de recherche générés")
        return search_terms
    
    def search_with_term(self, search_term):
        """
        Effectue une recherche avec un terme spécifique
        """
        try:
            url = f"https://www.data-asso.fr/annuaire?q={search_term}"
            self.driver.get(url)
            time.sleep(2)
            
            # Extraire les associations
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            found_associations = []
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        
                        if rna not in self.all_associations:
                            association_info = {
                                'rna': rna,
                                'nom': link.get_text(strip=True),
                                'url': 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href'],
                                'search_term': search_term,
                                'found_at': len(self.all_associations)
                            }
                            
                            found_associations.append(association_info)
                            self.all_associations[rna] = association_info
            
            return found_associations
            
        except Exception as e:
            logging.debug(f"Erreur recherche '{search_term}': {e}")
            return []
    
    def systematic_search_all_terms(self, max_terms=1000):
        """
        Recherche systématique avec tous les termes
        """
        logging.info("🔍 RECHERCHE SYSTÉMATIQUE AVEC TOUS LES TERMES")
        logging.info(f"📊 {len(self.search_terms)} termes disponibles, max {max_terms} à tester")
        
        terms_to_use = self.search_terms[:max_terms]
        
        for i, search_term in enumerate(terms_to_use):
            if i % 50 == 0:
                logging.info(f"🔄 Progression: {i}/{len(terms_to_use)} termes ({(i/len(terms_to_use)*100):.1f}%)")
                logging.info(f"   📊 Associations trouvées: {len(self.all_associations)}")
            
            found = self.search_with_term(search_term)
            
            if found:
                logging.info(f"   🎯 '{search_term}': {len(found)} nouvelles associations")
                
                # Afficher quelques exemples
                for assoc in found[:2]:
                    logging.info(f"      - {assoc['rna']}: {assoc['nom'][:50]}...")
            
            # Pause respectueuse
            time.sleep(0.5)
        
        results = {
            'total_terms_searched': len(terms_to_use),
            'total_associations_found': len(self.all_associations),
            'all_associations': list(self.all_associations.values())
        }
        
        logging.info(f"\n🎉 RECHERCHE SYSTÉMATIQUE TERMINÉE!")
        logging.info(f"   📊 Termes recherchés: {results['total_terms_searched']}")
        logging.info(f"   🏢 Associations trouvées: {results['total_associations_found']}")
        
        return results
    
    def save_systematic_results(self, results, filename_base='associations_systematic'):
        """
        Sauvegarde les résultats de recherche systématique
        """
        if not results.get('all_associations'):
            logging.warning("⚠️ Aucune association à sauvegarder")
            return
        
        associations = results['all_associations']
        
        # JSON complet
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_associations': len(associations),
                    'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'method': 'systematic_search',
                    'terms_searched': results['total_terms_searched']
                },
                'associations': associations
            }, f, ensure_ascii=False, indent=2)
        
        # CSV pour Excel
        csv_file = f"{filename_base}.csv"
        fieldnames = ['rna', 'nom', 'url', 'search_term', 'found_at']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for assoc in associations:
                writer.writerow(assoc)
        
        # Analyse des termes les plus productifs
        term_stats = {}
        for assoc in associations:
            term = assoc.get('search_term', 'unknown')
            term_stats[term] = term_stats.get(term, 0) + 1
        
        # Top 20 des termes les plus productifs
        top_terms = sorted(term_stats.items(), key=lambda x: x[1], reverse=True)[:20]
        
        stats_file = f"{filename_base}_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump({
                'top_search_terms': top_terms,
                'total_unique_terms': len(term_stats),
                'average_associations_per_term': len(associations) / len(term_stats) if term_stats else 0
            }, f, ensure_ascii=False, indent=2)
        
        logging.info(f"💾 Résultats systématiques sauvegardés:")
        logging.info(f"   📄 {json_file}")
        logging.info(f"   📊 {csv_file}")
        logging.info(f"   📈 {stats_file}")
        
        logging.info(f"\n📊 TOP 10 TERMES LES PLUS PRODUCTIFS:")
        for i, (term, count) in enumerate(top_terms[:10]):
            logging.info(f"   {i+1}. '{term}': {count} associations")

def main():
    """
    Recherche systématique principale
    """
    print("🔍 SCRAPER PAR RECHERCHE SYSTÉMATIQUE")
    print("=" * 60)
    print("Ce scraper va tester des milliers de termes de recherche")
    print("pour découvrir le maximum d'associations différentes.")
    print()
    print("Stratégie:")
    print("- Activités (sport, culture, social, etc.)")
    print("- Villes françaises")
    print("- Départements")
    print("- Types d'associations")
    print("- Combinaisons de termes")
    print("- Recherche alphabétique")
    print()
    
    max_terms = input("Nombre max de termes à tester (défaut: 1000): ").strip()
    try:
        max_terms = int(max_terms) if max_terms else 1000
    except:
        max_terms = 1000
    
    print(f"\n🎯 Test de {max_terms} termes de recherche")
    
    scraper = SystematicSearchScraper()
    
    try:
        scraper.start_driver()
        
        # Recherche systématique
        results = scraper.systematic_search_all_terms(max_terms=max_terms)
        
        # Sauvegarder
        scraper.save_systematic_results(results)
        
        # Afficher le résumé
        print(f"\n🎉 RECHERCHE SYSTÉMATIQUE TERMINÉE!")
        print(f"   📊 Termes testés: {results['total_terms_searched']}")
        print(f"   🏢 Associations trouvées: {results['total_associations_found']}")
        
        if results.get('all_associations'):
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS TROUVÉES:")
            for i, assoc in enumerate(results['all_associations'][:10]):
                print(f"   {i+1}. {assoc['rna']}: {assoc['nom'][:50]}...")
                print(f"      Trouvé avec: '{assoc['search_term']}'")
        
        print(f"\n💾 Résultats sauvegardés avec statistiques")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()
