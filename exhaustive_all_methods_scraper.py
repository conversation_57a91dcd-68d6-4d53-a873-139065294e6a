import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import itertools
import string

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exhaustive_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ExhaustiveAssociationScraper:
    def __init__(self):
        """
        Scraper EXHAUSTIF - TOUTES les méthodes possibles
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Activer les logs réseau
        self.options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        self.driver = None
        self.wait = None
        self.session = requests.Session()
        self.all_associations = {}  # Dict global pour éviter doublons
        
        # Statistiques
        self.stats = {
            'methods_tested': 0,
            'successful_methods': 0,
            'total_associations_found': 0,
            'method_results': {}
        }
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 30)
            logging.info("✅ Driver exhaustif démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def extract_associations_from_page(self):
        """
        Extrait toutes les associations de la page actuelle avec toutes les méthodes
        """
        found_associations = []

        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Méthode 1: Liens d'associations
            try:
                for link in soup.find_all('a', href=True):
                    if '/association/' in link['href']:
                        rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                        if rna_match:
                            rna = rna_match.group(1)
                            if rna not in self.all_associations:
                                assoc = {
                                    'rna': rna,
                                    'nom': link.get_text(strip=True),
                                    'url': 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href'],
                                    'method': 'link_parsing'
                                }
                                found_associations.append(assoc)
                                self.all_associations[rna] = assoc
            except Exception as e:
                logging.debug(f"Erreur méthode 1 (liens): {e}")
            
            # Méthode 2: Regex dans le texte
            try:
                text_content = soup.get_text()
                rna_patterns = [
                    r'RNA[:\s]*([A-Z0-9]{10})',
                    r'W[0-9]{9}[A-Z]?',
                    r'[A-Z][0-9]{2}[A-Z0-9]{7}',
                ]

                for pattern in rna_patterns:
                    matches = re.findall(pattern, text_content, re.IGNORECASE)
                    for rna in matches:
                        if rna not in self.all_associations:
                            assoc = {
                                'rna': rna,
                                'nom': 'Trouvé par regex',
                                'url': f'https://www.data-asso.fr/association/{rna}',
                                'method': 'regex_text'
                            }
                            found_associations.append(assoc)
                            self.all_associations[rna] = assoc
            except Exception as e:
                logging.debug(f"Erreur méthode 2 (regex): {e}")
            
            # Méthode 3: Attributs data-*
            for element in soup.find_all():
                try:
                    if hasattr(element, 'attrs') and element.attrs:
                        for attr, value in element.attrs.items():
                            if isinstance(attr, str) and isinstance(value, str) and 'rna' in attr.lower() and value:
                                if value not in self.all_associations:
                                    assoc = {
                                        'rna': value,
                                        'nom': element.get_text(strip=True) or 'Trouvé par attribut',
                                        'url': f'https://www.data-asso.fr/association/{value}',
                                        'method': 'data_attribute'
                                    }
                                    found_associations.append(assoc)
                                    self.all_associations[value] = assoc
                except Exception as e:
                    continue
            
            # Méthode 4: JavaScript variables
            try:
                js_vars = self.driver.execute_script("""
                    var associations = [];
                    // Chercher dans window
                    for (var key in window) {
                        if (typeof window[key] === 'object' && window[key] !== null) {
                            var str = JSON.stringify(window[key]);
                            if (str.includes('RNA') || str.includes('association')) {
                                associations.push(str);
                            }
                        }
                    }
                    return associations;
                """)
                
                for js_var in js_vars:
                    rna_matches = re.findall(r'W[0-9]{9}[A-Z]?', js_var)
                    for rna in rna_matches:
                        if rna not in self.all_associations:
                            assoc = {
                                'rna': rna,
                                'nom': 'Trouvé en JavaScript',
                                'url': f'https://www.data-asso.fr/association/{rna}',
                                'method': 'javascript_vars'
                            }
                            found_associations.append(assoc)
                            self.all_associations[rna] = assoc
            except:
                pass
            
            return found_associations
            
        except Exception as e:
            logging.error(f"Erreur extraction: {e}")
            return []
    
    def method_1_basic_scraping(self):
        """MÉTHODE 1: Scraping de base"""
        logging.info("🔍 MÉTHODE 1: Scraping de base")
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            associations = self.extract_associations_from_page()
            
            result = {
                'method': 'basic_scraping',
                'associations_found': len(associations),
                'success': len(associations) > 0
            }
            
            logging.info(f"   📊 Résultat: {len(associations)} associations")
            return result
            
        except Exception as e:
            logging.error(f"   ❌ Erreur méthode 1: {e}")
            return {'method': 'basic_scraping', 'error': str(e), 'success': False}
    
    def method_2_all_url_variations(self):
        """MÉTHODE 2: Toutes les variations d'URL"""
        logging.info("🔍 MÉTHODE 2: Variations d'URL")
        
        urls_to_test = [
            "https://www.data-asso.fr/annuaire",
            "https://www.data-asso.fr/annuaire/",
            "https://www.data-asso.fr/annuaire?page=1",
            "https://www.data-asso.fr/annuaire?limit=100",
            "https://www.data-asso.fr/annuaire?size=100",
            "https://www.data-asso.fr/annuaire?all=true",
            "https://www.data-asso.fr/annuaire?export=true",
            "https://www.data-asso.fr/search",
            "https://www.data-asso.fr/associations",
            "https://www.data-asso.fr/liste",
            "https://www.data-asso.fr/data",
        ]
        
        total_found = 0
        
        for url in urls_to_test:
            try:
                logging.info(f"   🌐 Test: {url}")
                self.driver.get(url)
                time.sleep(3)
                
                associations = self.extract_associations_from_page()
                if associations:
                    logging.info(f"      🎯 {len(associations)} associations trouvées")
                    total_found += len(associations)
                
            except Exception as e:
                logging.debug(f"      ❌ Erreur {url}: {e}")
        
        result = {
            'method': 'url_variations',
            'urls_tested': len(urls_to_test),
            'associations_found': total_found,
            'success': total_found > 0
        }
        
        logging.info(f"   📊 Total: {total_found} associations sur {len(urls_to_test)} URLs")
        return result
    
    def method_3_all_search_terms(self):
        """MÉTHODE 3: Recherche exhaustive avec tous les termes possibles"""
        logging.info("🔍 MÉTHODE 3: Recherche exhaustive")
        
        # Générer tous les termes possibles
        search_terms = []
        
        # Alphabet complet
        alphabet = string.ascii_lowercase
        search_terms.extend(alphabet)
        
        # Combinaisons de 2 lettres
        for a, b in itertools.product(alphabet, repeat=2):
            search_terms.append(f"{a}{b}")
        
        # Nombres
        search_terms.extend([str(i) for i in range(100)])
        
        # Départements
        search_terms.extend([f"{i:02d}" for i in range(1, 96)])
        
        # Mots-clés
        keywords = [
            'sport', 'culture', 'social', 'education', 'sante', 'environnement',
            'association', 'club', 'federation', 'union', 'comite', 'groupe',
            'paris', 'lyon', 'marseille', 'toulouse', 'nice', 'nantes'
        ]
        search_terms.extend(keywords)
        
        total_found = 0
        
        for i, term in enumerate(search_terms[:200]):  # Limiter à 200 pour les tests
            try:
                if i % 50 == 0:
                    logging.info(f"   🔄 Progression: {i}/200 termes")
                
                url = f"https://www.data-asso.fr/annuaire?q={term}"
                self.driver.get(url)
                time.sleep(1)
                
                associations = self.extract_associations_from_page()
                if associations:
                    total_found += len(associations)
                
            except Exception as e:
                logging.debug(f"      ❌ Erreur terme '{term}': {e}")
        
        result = {
            'method': 'exhaustive_search',
            'terms_tested': min(200, len(search_terms)),
            'associations_found': total_found,
            'success': total_found > 0
        }
        
        logging.info(f"   📊 Total: {total_found} associations avec {min(200, len(search_terms))} termes")
        return result
    
    def method_4_api_discovery(self):
        """MÉTHODE 4: Découverte d'APIs cachées"""
        logging.info("🔍 MÉTHODE 4: Découverte d'APIs")
        
        # URLs d'API potentielles
        api_urls = [
            "https://www.data-asso.fr/api/associations",
            "https://www.data-asso.fr/api/search",
            "https://www.data-asso.fr/api/annuaire",
            "https://www.data-asso.fr/gw/api-server/search",
            "https://www.data-asso.fr/gw/api-server/associations",
            "https://api.data-asso.fr/associations",
            "https://api.data-asso.fr/search",
        ]
        
        total_found = 0
        
        for api_url in api_urls:
            try:
                logging.info(f"   🔗 Test API: {api_url}")
                
                # Test GET
                response = self.session.get(api_url, timeout=10)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        associations = self.find_associations_in_json(data)
                        if associations:
                            logging.info(f"      🎯 {len(associations)} associations trouvées via API!")
                            total_found += len(associations)
                            
                            for assoc in associations:
                                if assoc['rna'] not in self.all_associations:
                                    assoc['method'] = 'api_discovery'
                                    self.all_associations[assoc['rna']] = assoc
                    except:
                        pass
                
                # Test POST
                post_response = self.session.post(api_url, json={}, timeout=10)
                if post_response.status_code == 200:
                    try:
                        data = post_response.json()
                        associations = self.find_associations_in_json(data)
                        if associations:
                            logging.info(f"      🎯 {len(associations)} associations trouvées via POST!")
                            total_found += len(associations)
                    except:
                        pass
                
            except Exception as e:
                logging.debug(f"      ❌ Erreur API {api_url}: {e}")
        
        result = {
            'method': 'api_discovery',
            'apis_tested': len(api_urls),
            'associations_found': total_found,
            'success': total_found > 0
        }
        
        logging.info(f"   📊 Total: {total_found} associations via APIs")
        return result
    
    def method_5_network_interception(self):
        """MÉTHODE 5: Interception des requêtes réseau"""
        logging.info("🔍 MÉTHODE 5: Interception réseau")
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Récupérer les logs réseau
            logs = self.driver.get_log('performance')
            api_calls = []
            
            for log in logs:
                try:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.responseReceived':
                        response = message['message']['params']['response']
                        url = response['url']
                        
                        if any(keyword in url.lower() for keyword in ['api', 'search', 'association', 'data']):
                            api_calls.append(url)
                except:
                    continue
            
            # Tester les URLs découvertes
            total_found = 0
            for api_url in set(api_calls):
                try:
                    response = self.session.get(api_url, timeout=10)
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            associations = self.find_associations_in_json(data)
                            if associations:
                                logging.info(f"      🎯 {len(associations)} associations via {api_url}")
                                total_found += len(associations)
                        except:
                            pass
                except:
                    pass
            
            result = {
                'method': 'network_interception',
                'api_calls_found': len(set(api_calls)),
                'associations_found': total_found,
                'success': total_found > 0
            }
            
            logging.info(f"   📊 {len(set(api_calls))} appels API découverts, {total_found} associations")
            return result
            
        except Exception as e:
            logging.error(f"   ❌ Erreur interception: {e}")
            return {'method': 'network_interception', 'error': str(e), 'success': False}
    
    def method_6_form_manipulation(self):
        """MÉTHODE 6: Manipulation de tous les formulaires"""
        logging.info("🔍 MÉTHODE 6: Manipulation de formulaires")
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            total_found = 0
            
            # Trouver tous les formulaires
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            
            for i, form in enumerate(forms):
                try:
                    logging.info(f"   📄 Test formulaire {i+1}")
                    
                    # Trouver tous les inputs
                    inputs = form.find_elements(By.TAG_NAME, "input")
                    selects = form.find_elements(By.TAG_NAME, "select")
                    
                    # Remplir les inputs
                    for input_elem in inputs:
                        try:
                            input_type = input_elem.get_attribute('type')
                            if input_type in ['text', 'search']:
                                input_elem.clear()
                                input_elem.send_keys("association")
                        except:
                            pass
                    
                    # Manipuler les selects
                    for select_elem in selects:
                        try:
                            select = Select(select_elem)
                            if len(select.options) > 1:
                                select.select_by_index(1)
                        except:
                            pass
                    
                    # Soumettre le formulaire
                    try:
                        submit_button = form.find_element(By.XPATH, ".//button[@type='submit'] | .//input[@type='submit']")
                        submit_button.click()
                        time.sleep(3)
                        
                        associations = self.extract_associations_from_page()
                        if associations:
                            logging.info(f"      🎯 {len(associations)} associations via formulaire {i+1}")
                            total_found += len(associations)
                        
                    except:
                        pass
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur formulaire {i+1}: {e}")
            
            result = {
                'method': 'form_manipulation',
                'forms_tested': len(forms),
                'associations_found': total_found,
                'success': total_found > 0
            }
            
            logging.info(f"   📊 {len(forms)} formulaires testés, {total_found} associations")
            return result
            
        except Exception as e:
            logging.error(f"   ❌ Erreur formulaires: {e}")
            return {'method': 'form_manipulation', 'error': str(e), 'success': False}
    
    def find_associations_in_json(self, data):
        """Trouve des associations dans des données JSON"""
        associations = []
        
        def search_recursive(obj):
            if isinstance(obj, dict):
                # Vérifier si c'est une association
                if self.looks_like_association(obj):
                    rna = obj.get('rna') or obj.get('id_rna') or obj.get('RNA')
                    if rna:
                        associations.append({
                            'rna': rna,
                            'nom': obj.get('nom') or obj.get('name') or obj.get('denomination') or 'Association API',
                            'url': f'https://www.data-asso.fr/association/{rna}',
                            'method': 'api_json'
                        })
                
                for value in obj.values():
                    search_recursive(value)
                    
            elif isinstance(obj, list):
                for item in obj:
                    search_recursive(item)
        
        search_recursive(data)
        return associations
    
    def looks_like_association(self, obj):
        """Détermine si un objet ressemble à une association"""
        if not isinstance(obj, dict):
            return False
        
        indicators = ['rna', 'siren', 'nom', 'name', 'denomination', 'association']
        found = sum(1 for key in obj.keys() if any(ind in key.lower() for ind in indicators))
        return found >= 2
    
    def run_all_methods(self):
        """Lance TOUTES les méthodes exhaustivement"""
        logging.info("🚀 LANCEMENT DE TOUTES LES MÉTHODES EXHAUSTIVES")
        
        methods = [
            self.method_1_basic_scraping,
            self.method_2_all_url_variations,
            self.method_3_all_search_terms,
            self.method_4_api_discovery,
            self.method_5_network_interception,
            self.method_6_form_manipulation,
        ]
        
        results = []
        
        for i, method in enumerate(methods):
            logging.info(f"\n{'='*60}")
            logging.info(f"MÉTHODE {i+1}/{len(methods)}")
            
            try:
                result = method()
                results.append(result)
                
                self.stats['methods_tested'] += 1
                if result.get('success'):
                    self.stats['successful_methods'] += 1
                
                self.stats['method_results'][result.get('method', f'method_{i+1}')] = result
                
                logging.info(f"✅ Méthode terminée: {result.get('method', f'method_{i+1}')}")
                
            except Exception as e:
                logging.error(f"❌ Erreur méthode {i+1}: {e}")
                results.append({'method': f'method_{i+1}', 'error': str(e), 'success': False})
        
        self.stats['total_associations_found'] = len(self.all_associations)
        
        return results
    
    def save_exhaustive_results(self):
        """Sauvegarde tous les résultats"""
        # Associations trouvées
        associations_list = list(self.all_associations.values())
        
        # JSON complet
        final_results = {
            'metadata': {
                'total_associations': len(associations_list),
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'methods_tested': self.stats['methods_tested'],
                'successful_methods': self.stats['successful_methods'],
                'method_results': self.stats['method_results']
            },
            'associations': associations_list
        }
        
        with open('exhaustive_results.json', 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        # CSV
        if associations_list:
            with open('exhaustive_results.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'url', 'method']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for assoc in associations_list:
                    writer.writerow(assoc)
        
        logging.info("💾 Résultats exhaustifs sauvegardés:")
        logging.info("   📄 exhaustive_results.json")
        logging.info("   📊 exhaustive_results.csv")

def main():
    """
    Scraper exhaustif principal
    """
    print("🚀 SCRAPER EXHAUSTIF - TOUTES LES MÉTHODES")
    print("=" * 70)
    print("Ce scraper va tester ABSOLUMENT TOUTES les méthodes possibles:")
    print("1. Scraping de base")
    print("2. Toutes les variations d'URL")
    print("3. Recherche exhaustive (alphabet, nombres, mots-clés)")
    print("4. Découverte d'APIs cachées")
    print("5. Interception des requêtes réseau")
    print("6. Manipulation de tous les formulaires")
    print()
    print("⚠️ ATTENTION: Ce processus peut prendre 1-2 heures!")
    print()
    
    response = input("Voulez-vous lancer le scraping exhaustif? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = ExhaustiveAssociationScraper()
    
    try:
        scraper.start_driver()
        
        # Lancer toutes les méthodes
        results = scraper.run_all_methods()
        
        # Sauvegarder
        scraper.save_exhaustive_results()
        
        # Afficher le résumé final
        print(f"\n🎉 SCRAPING EXHAUSTIF TERMINÉ!")
        print(f"   📊 Méthodes testées: {scraper.stats['methods_tested']}")
        print(f"   ✅ Méthodes réussies: {scraper.stats['successful_methods']}")
        print(f"   🏢 Total associations: {scraper.stats['total_associations_found']}")
        
        # Détail par méthode
        print(f"\n📋 RÉSULTATS PAR MÉTHODE:")
        for method_name, result in scraper.stats['method_results'].items():
            status = "✅" if result.get('success') else "❌"
            associations = result.get('associations_found', 0)
            print(f"   {status} {method_name}: {associations} associations")
        
        # Exemples d'associations
        if scraper.all_associations:
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS TROUVÉES:")
            for i, assoc in enumerate(list(scraper.all_associations.values())[:10]):
                print(f"   {i+1}. {assoc['rna']}: {assoc['nom'][:50]}...")
                print(f"      Méthode: {assoc['method']}")
        
        print(f"\n💾 Résultats complets sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()
