import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('annuaire_complete_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class CompleteAnnuaireAnalyzer:
    def __init__(self):
        """
        Analyseur COMPLET de la page d'annuaire data-asso.fr
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Activer les logs réseau
        self.options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        self.driver = None
        self.wait = None
        self.analysis_results = {}
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def analyze_page_elements_detailed(self):
        """
        Analyse TOUS les éléments de la page d'annuaire
        """
        logging.info("🔍 ANALYSE DÉTAILLÉE DE TOUS LES ÉLÉMENTS")
        
        url = "https://www.data-asso.fr/annuaire"
        self.driver.get(url)
        time.sleep(10)  # Attendre le chargement complet
        
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        
        elements_analysis = {
            'page_info': {
                'title': self.driver.title,
                'url': self.driver.current_url,
                'page_height': self.driver.execute_script("return document.body.scrollHeight"),
                'viewport_height': self.driver.execute_script("return window.innerHeight")
            },
            'all_inputs': [],
            'all_selects': [],
            'all_buttons': [],
            'all_forms': [],
            'all_links': [],
            'filter_elements': [],
            'search_elements': [],
            'pagination_elements': [],
            'association_cards': []
        }
        
        # 1. TOUS LES INPUTS
        logging.info("📝 Analyse des inputs...")
        inputs = self.driver.find_elements(By.TAG_NAME, "input")
        for i, input_elem in enumerate(inputs):
            try:
                input_info = {
                    'index': i,
                    'type': input_elem.get_attribute('type'),
                    'name': input_elem.get_attribute('name'),
                    'id': input_elem.get_attribute('id'),
                    'class': input_elem.get_attribute('class'),
                    'placeholder': input_elem.get_attribute('placeholder'),
                    'value': input_elem.get_attribute('value'),
                    'visible': input_elem.is_displayed(),
                    'enabled': input_elem.is_enabled()
                }
                elements_analysis['all_inputs'].append(input_info)
                
                if input_info['visible'] and input_info['enabled']:
                    logging.info(f"   📝 Input {i}: {input_info['type']} - {input_info['placeholder']} - {input_info['name']}")
                    
            except Exception as e:
                logging.debug(f"Erreur input {i}: {e}")
        
        # 2. TOUS LES SELECTS (dropdowns)
        logging.info("📋 Analyse des selects...")
        selects = self.driver.find_elements(By.TAG_NAME, "select")
        for i, select_elem in enumerate(selects):
            try:
                select_obj = Select(select_elem)
                options = [opt.text for opt in select_obj.options]
                
                select_info = {
                    'index': i,
                    'name': select_elem.get_attribute('name'),
                    'id': select_elem.get_attribute('id'),
                    'class': select_elem.get_attribute('class'),
                    'visible': select_elem.is_displayed(),
                    'enabled': select_elem.is_enabled(),
                    'options': options,
                    'options_count': len(options),
                    'selected_value': select_obj.first_selected_option.text if select_obj.first_selected_option else None
                }
                elements_analysis['all_selects'].append(select_info)
                
                if select_info['visible'] and select_info['enabled']:
                    logging.info(f"   📋 Select {i}: {select_info['name']} - {select_info['options_count']} options")
                    logging.info(f"      Options: {options[:5]}...")  # Afficher les 5 premières
                    
            except Exception as e:
                logging.debug(f"Erreur select {i}: {e}")
        
        # 3. TOUS LES BOUTONS
        logging.info("🔘 Analyse des boutons...")
        buttons = self.driver.find_elements(By.TAG_NAME, "button")
        for i, button_elem in enumerate(buttons):
            try:
                button_info = {
                    'index': i,
                    'text': button_elem.text.strip(),
                    'type': button_elem.get_attribute('type'),
                    'class': button_elem.get_attribute('class'),
                    'id': button_elem.get_attribute('id'),
                    'onclick': button_elem.get_attribute('onclick'),
                    'visible': button_elem.is_displayed(),
                    'enabled': button_elem.is_enabled()
                }
                elements_analysis['all_buttons'].append(button_info)
                
                if button_info['visible'] and button_info['enabled']:
                    logging.info(f"   🔘 Bouton {i}: '{button_info['text']}' - {button_info['type']}")
                    
            except Exception as e:
                logging.debug(f"Erreur bouton {i}: {e}")
        
        # 4. TOUS LES FORMULAIRES
        logging.info("📄 Analyse des formulaires...")
        forms = soup.find_all('form')
        for i, form in enumerate(forms):
            form_inputs = form.find_all(['input', 'select', 'textarea', 'button'])
            form_info = {
                'index': i,
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'class': form.get('class', []),
                'id': form.get('id', ''),
                'inputs_count': len(form_inputs),
                'inputs': [{'tag': inp.name, 'type': inp.get('type'), 'name': inp.get('name')} for inp in form_inputs]
            }
            elements_analysis['all_forms'].append(form_info)
            logging.info(f"   📄 Form {i}: {form_info['method']} -> {form_info['action']} ({form_info['inputs_count']} champs)")
        
        # 5. CARTES D'ASSOCIATIONS
        logging.info("🏢 Analyse des cartes d'associations...")
        association_cards = soup.find_all('a', class_=lambda x: x and 'association' in ' '.join(x).lower())
        if not association_cards:
            # Essayer d'autres sélecteurs
            association_cards = soup.find_all('a', href=lambda x: x and '/association/' in x)
        
        for i, card in enumerate(association_cards):
            card_info = {
                'index': i,
                'href': card.get('href', ''),
                'text': card.get_text(strip=True)[:200],  # Premiers 200 caractères
                'class': card.get('class', []),
                'rna': None,
                'siren': None
            }
            
            # Extraire RNA et SIREN
            rna_match = re.search(r'RNA\s*([A-Z0-9]+)', card_info['text'], re.IGNORECASE)
            if rna_match:
                card_info['rna'] = rna_match.group(1)
            
            siren_match = re.search(r'SIREN\s*([0-9]+)', card_info['text'], re.IGNORECASE)
            if siren_match:
                card_info['siren'] = siren_match.group(1)
            
            elements_analysis['association_cards'].append(card_info)
            
            if i < 5:  # Afficher les 5 premières
                logging.info(f"   🏢 Association {i}: RNA={card_info['rna']} - {card_info['text'][:50]}...")
        
        self.analysis_results['elements'] = elements_analysis
        
        logging.info(f"📊 Éléments trouvés:")
        logging.info(f"   📝 Inputs: {len(elements_analysis['all_inputs'])}")
        logging.info(f"   📋 Selects: {len(elements_analysis['all_selects'])}")
        logging.info(f"   🔘 Boutons: {len(elements_analysis['all_buttons'])}")
        logging.info(f"   📄 Formulaires: {len(elements_analysis['all_forms'])}")
        logging.info(f"   🏢 Associations: {len(elements_analysis['association_cards'])}")
        
        return elements_analysis
    
    def test_all_filters_systematically(self):
        """
        Teste TOUS les filtres disponibles de manière systématique
        """
        logging.info("🎛️ TEST SYSTÉMATIQUE DE TOUS LES FILTRES")
        
        filter_results = {}
        
        # Récupérer l'état initial
        initial_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        initial_associations = [link for link in initial_soup.find_all('a', href=True) 
                              if '/association/' in link['href']]
        initial_count = len(initial_associations)
        
        logging.info(f"📊 État initial: {initial_count} associations")
        
        # 1. Tester tous les selects
        selects = self.driver.find_elements(By.TAG_NAME, "select")
        for i, select_elem in enumerate(selects):
            if not select_elem.is_displayed() or not select_elem.is_enabled():
                continue
                
            try:
                select_obj = Select(select_elem)
                select_name = select_elem.get_attribute('name') or f"select_{i}"
                
                logging.info(f"📋 Test select: {select_name}")
                
                # Tester chaque option
                for j, option in enumerate(select_obj.options[1:5]):  # Tester les 4 premières options (skip la première qui est souvent vide)
                    try:
                        option_text = option.text.strip()
                        if not option_text:
                            continue
                            
                        logging.info(f"   🔄 Test option: {option_text}")
                        
                        # Sélectionner l'option
                        select_obj.select_by_visible_text(option_text)
                        time.sleep(3)  # Attendre le chargement
                        
                        # Compter les associations après changement
                        new_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                        new_associations = [link for link in new_soup.find_all('a', href=True) 
                                          if '/association/' in link['href']]
                        new_count = len(new_associations)
                        
                        filter_results[f"{select_name}_{option_text}"] = {
                            'type': 'select',
                            'initial_count': initial_count,
                            'new_count': new_count,
                            'changed': new_count != initial_count,
                            'associations_sample': [link['href'] for link in new_associations[:3]]
                        }
                        
                        logging.info(f"      📊 {initial_count} -> {new_count} associations")
                        
                        if new_count != initial_count:
                            logging.info(f"      🎯 FILTRE FONCTIONNEL! {select_name} = {option_text}")
                        
                        # Remettre à l'état initial
                        select_obj.select_by_index(0)
                        time.sleep(2)
                        
                    except Exception as e:
                        logging.debug(f"Erreur option {option_text}: {e}")
                        
            except Exception as e:
                logging.debug(f"Erreur select {i}: {e}")
        
        # 2. Tester les champs de recherche
        search_inputs = [inp for inp in self.driver.find_elements(By.TAG_NAME, "input") 
                        if inp.get_attribute('type') in ['text', 'search'] and inp.is_displayed()]
        
        for i, input_elem in enumerate(search_inputs):
            try:
                input_name = input_elem.get_attribute('name') or input_elem.get_attribute('placeholder') or f"input_{i}"
                
                logging.info(f"🔍 Test recherche: {input_name}")
                
                # Tester différents termes de recherche
                test_terms = ['sport', 'culture', 'social', 'education', 'sante']
                
                for term in test_terms:
                    try:
                        input_elem.clear()
                        input_elem.send_keys(term)
                        
                        # Chercher un bouton de recherche ou appuyer sur Entrée
                        search_button = None
                        try:
                            search_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Recherch') or contains(text(), 'Search')]")
                        except:
                            pass
                        
                        if search_button and search_button.is_displayed():
                            search_button.click()
                        else:
                            input_elem.send_keys('\n')  # Appuyer sur Entrée
                        
                        time.sleep(3)
                        
                        # Compter les résultats
                        search_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                        search_associations = [link for link in search_soup.find_all('a', href=True) 
                                             if '/association/' in link['href']]
                        search_count = len(search_associations)
                        
                        filter_results[f"{input_name}_{term}"] = {
                            'type': 'search',
                            'initial_count': initial_count,
                            'new_count': search_count,
                            'changed': search_count != initial_count,
                            'search_term': term
                        }
                        
                        logging.info(f"      📊 Recherche '{term}': {search_count} associations")
                        
                        if search_count != initial_count:
                            logging.info(f"      🎯 RECHERCHE FONCTIONNELLE! '{term}' -> {search_count} résultats")
                        
                        # Vider le champ
                        input_elem.clear()
                        time.sleep(2)
                        
                    except Exception as e:
                        logging.debug(f"Erreur recherche {term}: {e}")
                        
            except Exception as e:
                logging.debug(f"Erreur input recherche {i}: {e}")
        
        self.analysis_results['filters'] = filter_results
        
        # Résumé des filtres fonctionnels
        functional_filters = [name for name, result in filter_results.items() if result['changed']]
        
        logging.info(f"\n🎛️ RÉSUMÉ DES FILTRES:")
        logging.info(f"   📊 Total testés: {len(filter_results)}")
        logging.info(f"   ✅ Fonctionnels: {len(functional_filters)}")
        
        if functional_filters:
            logging.info(f"   🎯 FILTRES FONCTIONNELS TROUVÉS:")
            for filter_name in functional_filters:
                result = filter_results[filter_name]
                logging.info(f"      ✅ {filter_name}: {result['initial_count']} -> {result['new_count']}")
        
        return filter_results
    
    def test_scroll_and_loading_behavior(self):
        """
        Teste le comportement du scroll et du chargement
        """
        logging.info("📜 TEST DU COMPORTEMENT DE SCROLL")
        
        scroll_results = {
            'initial_height': self.driver.execute_script("return document.body.scrollHeight"),
            'initial_associations': 0,
            'scroll_tests': []
        }
        
        # Compter les associations initiales
        initial_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        initial_associations = [link for link in initial_soup.find_all('a', href=True) 
                              if '/association/' in link['href']]
        scroll_results['initial_associations'] = len(initial_associations)
        
        logging.info(f"📊 État initial: {scroll_results['initial_associations']} associations, hauteur: {scroll_results['initial_height']}px")
        
        # Test de scroll progressif
        for i in range(10):
            scroll_position = (i + 1) * 500
            
            logging.info(f"📜 Scroll {i+1}: position {scroll_position}px")
            
            self.driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(2)
            
            # Mesurer les changements
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            new_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            new_associations = [link for link in new_soup.find_all('a', href=True) 
                              if '/association/' in link['href']]
            
            scroll_test = {
                'scroll_position': scroll_position,
                'page_height': new_height,
                'associations_count': len(new_associations),
                'height_changed': new_height > scroll_results['initial_height'],
                'associations_changed': len(new_associations) > scroll_results['initial_associations']
            }
            
            scroll_results['scroll_tests'].append(scroll_test)
            
            logging.info(f"      📊 Hauteur: {new_height}px, Associations: {len(new_associations)}")
            
            if scroll_test['associations_changed']:
                logging.info(f"      🎯 NOUVELLES ASSOCIATIONS DÉTECTÉES!")
                break
            
            if scroll_position >= new_height:
                logging.info(f"      📜 Fin de page atteinte")
                break
        
        self.analysis_results['scroll'] = scroll_results
        return scroll_results
    
    def comprehensive_annuaire_analysis(self):
        """
        Analyse complète de la page d'annuaire
        """
        logging.info("🚀 ANALYSE COMPLÈTE DE LA PAGE D'ANNUAIRE")
        
        try:
            # 1. Analyser tous les éléments
            elements = self.analyze_page_elements_detailed()
            
            # 2. Tester tous les filtres
            filters = self.test_all_filters_systematically()
            
            # 3. Tester le scroll
            scroll = self.test_scroll_and_loading_behavior()
            
            # 4. Résumé et recommandations
            summary = {
                'total_associations_found': len(elements['association_cards']),
                'functional_filters': [name for name, result in filters.items() if result['changed']],
                'scroll_loading': any(test['associations_changed'] for test in scroll['scroll_tests']),
                'available_inputs': len([inp for inp in elements['all_inputs'] if inp['visible'] and inp['enabled']]),
                'available_selects': len([sel for sel in elements['all_selects'] if sel['visible'] and sel['enabled']]),
                'available_buttons': len([btn for btn in elements['all_buttons'] if btn['visible'] and btn['enabled']])
            }
            
            # Recommandations
            recommendations = []
            
            if summary['functional_filters']:
                recommendations.append(f"✅ {len(summary['functional_filters'])} filtres fonctionnels trouvés")
                recommendations.append("💡 Utiliser les filtres pour obtenir différentes associations")
            
            if summary['scroll_loading']:
                recommendations.append("✅ Scroll infini détecté")
                recommendations.append("💡 Utiliser le scroll pour charger plus d'associations")
            
            if not summary['functional_filters'] and not summary['scroll_loading']:
                recommendations.append("❌ Aucun moyen de charger plus d'associations détecté")
                recommendations.append("💡 Le site semble limité aux associations actuellement affichées")
            
            summary['recommendations'] = recommendations
            self.analysis_results['summary'] = summary
            
            # Sauvegarder l'analyse complète
            with open('annuaire_complete_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
            
            logging.info("✅ ANALYSE COMPLÈTE TERMINÉE")
            
            return self.analysis_results
            
        except Exception as e:
            logging.error(f"❌ Erreur analyse complète: {e}")
            return None

def main():
    """
    Analyse principale complète
    """
    print("🔍 ANALYSE COMPLÈTE DE LA PAGE D'ANNUAIRE DATA-ASSO")
    print("=" * 70)
    print("Cette analyse va examiner TOUS les éléments de la page:")
    print("- Tous les filtres et champs de recherche")
    print("- Tous les boutons et formulaires")
    print("- Le comportement du scroll")
    print("- Les cartes d'associations")
    print()
    
    analyzer = CompleteAnnuaireAnalyzer()
    
    try:
        analyzer.start_driver()
        
        # Analyse complète
        results = analyzer.comprehensive_annuaire_analysis()
        
        if results:
            summary = results.get('summary', {})
            
            print(f"\n📊 RÉSULTATS DE L'ANALYSE COMPLÈTE:")
            print(f"   🏢 Associations trouvées: {summary.get('total_associations_found', 0)}")
            print(f"   🎛️ Filtres fonctionnels: {len(summary.get('functional_filters', []))}")
            print(f"   📜 Scroll infini: {'✅' if summary.get('scroll_loading') else '❌'}")
            print(f"   📝 Champs disponibles: {summary.get('available_inputs', 0)}")
            print(f"   📋 Sélecteurs disponibles: {summary.get('available_selects', 0)}")
            print(f"   🔘 Boutons disponibles: {summary.get('available_buttons', 0)}")
            
            if summary.get('functional_filters'):
                print(f"\n🎯 FILTRES FONCTIONNELS TROUVÉS:")
                for filter_name in summary['functional_filters']:
                    print(f"   ✅ {filter_name}")
            
            if summary.get('recommendations'):
                print(f"\n💡 RECOMMANDATIONS:")
                for rec in summary['recommendations']:
                    print(f"   {rec}")
            
            print(f"\n💾 Analyse complète sauvegardée dans: annuaire_complete_analysis.json")
            print(f"📄 Log détaillé dans: annuaire_complete_analysis.log")
            
        else:
            print("\n❌ Échec de l'analyse")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        analyzer.close_driver()

if __name__ == "__main__":
    main()
