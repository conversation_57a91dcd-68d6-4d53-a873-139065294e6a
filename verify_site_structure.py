import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re

def verify_site_structure():
    """
    Vérifie la structure du site data-asso.fr pour comprendre comment scraper
    """
    print("🔍 VÉRIFICATION DE LA STRUCTURE DU SITE DATA-ASSO.FR")
    print("=" * 60)
    
    # Configuration Chrome
    options = Options()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

    # Installation automatique de ChromeDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    wait = WebDriverWait(driver, 15)
    
    verification_results = {
        "site_accessible": False,
        "page_title": "",
        "associations_found": 0,
        "association_links": [],
        "sample_html": "",
        "rna_siren_patterns": [],
        "scroll_test": False,
        "association_page_test": {},
        "coordonnees_tab_found": False,
        "extraction_fields": {}
    }
    
    try:
        # 1. Accès au site
        print("\n1️⃣ Accès au site...")
        url = "https://www.data-asso.fr/annuaire"
        driver.get(url)
        time.sleep(5)
        
        verification_results["site_accessible"] = True
        verification_results["page_title"] = driver.title
        print(f"   ✅ Site accessible: {driver.title}")
        
        # 2. Analyse de la structure HTML
        print("\n2️⃣ Analyse de la structure HTML...")
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Sauvegarder un échantillon du HTML
        verification_results["sample_html"] = str(soup)[:2000] + "..."
        
        # 3. Recherche des associations
        print("\n3️⃣ Recherche des associations...")
        
        # Différents patterns possibles pour les liens d'associations
        association_patterns = [
            'a[href*="/association/"]',
            'a[href*="/asso/"]',
            '.association-card',
            '.asso-card',
            '[data-association]'
        ]
        
        association_links = []
        for pattern in association_patterns:
            elements = soup.select(pattern)
            if elements:
                print(f"   ✅ Trouvé {len(elements)} éléments avec le pattern: {pattern}")
                for elem in elements[:5]:  # Prendre les 5 premiers
                    if elem.get('href'):
                        link = elem['href']
                        if link.startswith('/'):
                            link = 'https://www.data-asso.fr' + link
                        association_links.append(link)
                break
        
        verification_results["associations_found"] = len(association_links)
        verification_results["association_links"] = association_links[:10]
        
        # 4. Recherche des numéros RNA/SIREN
        print("\n4️⃣ Recherche des patterns RNA/SIREN...")
        text_content = soup.get_text()
        
        rna_patterns = re.findall(r'RNA[:\s]*([A-Z0-9]+)', text_content, re.IGNORECASE)
        siren_patterns = re.findall(r'SIREN[:\s]*([0-9]+)', text_content, re.IGNORECASE)
        
        verification_results["rna_siren_patterns"] = {
            "rna_found": len(rna_patterns),
            "siren_found": len(siren_patterns),
            "rna_examples": rna_patterns[:3],
            "siren_examples": siren_patterns[:3]
        }
        
        print(f"   📋 RNA trouvés: {len(rna_patterns)} (ex: {rna_patterns[:3]})")
        print(f"   📋 SIREN trouvés: {len(siren_patterns)} (ex: {siren_patterns[:3]})")
        
        # 5. Test du scroll
        print("\n5️⃣ Test du scroll pour chargement dynamique...")
        initial_height = driver.execute_script("return document.body.scrollHeight")
        
        # Scroll vers le bas
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        new_height = driver.execute_script("return document.body.scrollHeight")
        verification_results["scroll_test"] = new_height > initial_height
        
        print(f"   📏 Hauteur initiale: {initial_height}px")
        print(f"   📏 Nouvelle hauteur: {new_height}px")
        print(f"   {'✅' if verification_results['scroll_test'] else '❌'} Contenu dynamique détecté")
        
        # 6. Test d'une page d'association
        print("\n6️⃣ Test d'une page d'association...")
        if association_links:
            test_link = association_links[0]
            print(f"   🔗 Test de: {test_link}")
            
            try:
                driver.get(test_link)
                time.sleep(3)
                
                association_soup = BeautifulSoup(driver.page_source, 'html.parser')
                
                # Chercher l'onglet coordonnées
                coordonnees_selectors = [
                    "//a[contains(text(), 'Coordonnées')]",
                    "//a[contains(text(), 'coordonnées')]",
                    "//button[contains(text(), 'Coordonnées')]",
                    "//tab[contains(text(), 'Coordonnées')]"
                ]
                
                coordonnees_found = False
                for selector in coordonnees_selectors:
                    try:
                        element = driver.find_element(By.XPATH, selector)
                        coordonnees_found = True
                        print(f"   ✅ Onglet coordonnées trouvé avec: {selector}")
                        
                        # Cliquer sur l'onglet
                        element.click()
                        time.sleep(2)
                        break
                    except:
                        continue
                
                verification_results["coordonnees_tab_found"] = coordonnees_found
                
                # Extraire les champs disponibles
                final_soup = BeautifulSoup(driver.page_source, 'html.parser')
                
                # Chercher les champs typiques
                fields_to_check = [
                    'Adresse', 'Téléphone', 'Email', 'Courriel', 'Site internet',
                    'Adresse de gestion', 'Code postal', 'Ville'
                ]
                
                found_fields = {}
                for field in fields_to_check:
                    field_element = final_soup.find(text=re.compile(field, re.IGNORECASE))
                    if field_element:
                        found_fields[field] = True
                        print(f"   📋 Champ trouvé: {field}")
                
                verification_results["extraction_fields"] = found_fields
                
                # Extraire le titre de l'association
                title = final_soup.find('h1')
                if title:
                    verification_results["association_page_test"]["title"] = title.get_text(strip=True)
                    print(f"   🏢 Titre: {title.get_text(strip=True)}")
                
            except Exception as e:
                print(f"   ❌ Erreur lors du test de la page: {e}")
        
        print("\n" + "=" * 60)
        print("✅ VÉRIFICATION TERMINÉE")
        
    except Exception as e:
        print(f"\n❌ Erreur générale: {e}")
        verification_results["error"] = str(e)
        
    finally:
        driver.quit()
    
    # Sauvegarder les résultats
    with open('site_verification_results.json', 'w', encoding='utf-8') as f:
        json.dump(verification_results, f, ensure_ascii=False, indent=2)
    
    print(f"📄 Résultats sauvegardés dans: site_verification_results.json")
    
    # Afficher le résumé
    print("\n📊 RÉSUMÉ DE LA VÉRIFICATION:")
    print(f"   🌐 Site accessible: {'✅' if verification_results['site_accessible'] else '❌'}")
    print(f"   🏢 Associations trouvées: {verification_results['associations_found']}")
    print(f"   📜 Scroll dynamique: {'✅' if verification_results['scroll_test'] else '❌'}")
    print(f"   📋 Onglet coordonnées: {'✅' if verification_results['coordonnees_tab_found'] else '❌'}")
    print(f"   📍 Champs extractibles: {len(verification_results['extraction_fields'])}")
    
    return verification_results

if __name__ == "__main__":
    verify_site_structure()
