import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realistic_scroll.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class RealisticScrollTester:
    def __init__(self):
        """
        Test réaliste du scroll infini - simulation exacte du comportement manuel
        """
        self.options = Options()
        # Mode visible pour voir exactement ce qui se passe
        # self.options.add_argument('--headless')  # Commenté pour voir
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = None
        self.wait = None
        self.all_associations = set()  # Pour éviter les doublons
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré en mode visible")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def extract_associations_from_page(self):
        """
        Extrait toutes les associations visibles sur la page actuelle
        """
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            current_associations = []
            
            # Chercher tous les liens d'associations
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    # Extraire RNA depuis l'URL
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        
                        association_info = {
                            'rna': rna,
                            'url': 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href'],
                            'text': link.get_text(strip=True)[:200],  # Texte de la carte
                            'found_at_scroll': len(self.all_associations)  # Position de découverte
                        }
                        
                        current_associations.append(association_info)
            
            return current_associations
            
        except Exception as e:
            logging.error(f"Erreur extraction: {e}")
            return []
    
    def realistic_infinite_scroll_test(self, max_scrolls=100, target_associations=100):
        """
        Test réaliste du scroll infini - exactement comme un utilisateur manuel
        """
        logging.info("🖱️ TEST RÉALISTE DU SCROLL INFINI")
        logging.info(f"📊 Objectif: {target_associations} associations, max {max_scrolls} scrolls")
        
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🌐 Accès à {url}")
        
        self.driver.get(url)
        time.sleep(10)  # Attendre le chargement complet comme un utilisateur
        
        # État initial
        initial_associations = self.extract_associations_from_page()
        for assoc in initial_associations:
            self.all_associations.add(assoc['rna'])
        
        logging.info(f"📊 État initial: {len(initial_associations)} associations")
        
        scroll_results = {
            'initial_count': len(initial_associations),
            'scroll_history': [],
            'final_associations': [],
            'success': False
        }
        
        previous_count = len(self.all_associations)
        no_change_count = 0
        
        for scroll_num in range(max_scrolls):
            logging.info(f"\n📜 SCROLL {scroll_num + 1}/{max_scrolls}")
            
            # Simuler un scroll manuel réaliste
            # 1. Scroll progressif vers le bas (comme avec la molette)
            current_position = self.driver.execute_script("return window.pageYOffset")
            page_height = self.driver.execute_script("return document.body.scrollHeight")
            
            # Scroll de 800px (équivalent à quelques tours de molette)
            new_position = min(current_position + 800, page_height)
            
            logging.info(f"   📍 Position: {current_position}px -> {new_position}px")
            
            # Scroll fluide comme un utilisateur
            self.driver.execute_script(f"window.scrollTo({{top: {new_position}, behavior: 'smooth'}});")
            
            # Attendre comme un utilisateur qui regarde le contenu
            time.sleep(3)
            
            # Vérifier si on est proche du bas
            viewport_height = self.driver.execute_script("return window.innerHeight")
            distance_from_bottom = page_height - (new_position + viewport_height)
            
            logging.info(f"   📏 Distance du bas: {distance_from_bottom}px")
            
            # Si on est proche du bas, attendre plus longtemps pour le chargement
            if distance_from_bottom < 200:
                logging.info("   ⏳ Proche du bas - Attente de chargement...")
                time.sleep(8)  # Attendre le chargement du contenu
                
                # Vérifier si la page a grandi
                new_page_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_page_height > page_height:
                    logging.info(f"   📈 Page agrandie: {page_height}px -> {new_page_height}px")
                    page_height = new_page_height
            
            # Extraire les nouvelles associations
            current_associations = self.extract_associations_from_page()
            
            # Ajouter les nouvelles associations
            new_associations = []
            for assoc in current_associations:
                if assoc['rna'] not in self.all_associations:
                    self.all_associations.add(assoc['rna'])
                    new_associations.append(assoc)
            
            current_total = len(self.all_associations)
            
            scroll_info = {
                'scroll_number': scroll_num + 1,
                'position': new_position,
                'page_height': page_height,
                'distance_from_bottom': distance_from_bottom,
                'total_associations': current_total,
                'new_associations_found': len(new_associations),
                'new_associations': [assoc['rna'] for assoc in new_associations]
            }
            
            scroll_results['scroll_history'].append(scroll_info)
            
            logging.info(f"   📊 Total associations: {previous_count} -> {current_total}")
            
            if new_associations:
                logging.info(f"   🎯 {len(new_associations)} NOUVELLES ASSOCIATIONS TROUVÉES!")
                for assoc in new_associations[:3]:  # Afficher les 3 premières
                    logging.info(f"      🏢 {assoc['rna']}: {assoc['text'][:50]}...")
                
                no_change_count = 0  # Reset du compteur
                previous_count = current_total
                
                # Vérifier si on a atteint l'objectif
                if current_total >= target_associations:
                    logging.info(f"   🎉 OBJECTIF ATTEINT! {current_total} associations")
                    scroll_results['success'] = True
                    break
            else:
                no_change_count += 1
                logging.info(f"   ⚠️ Pas de nouvelles associations ({no_change_count}/5)")
            
            # Si pas de nouvelles associations depuis 5 scrolls
            if no_change_count >= 5:
                logging.info("   ⏹️ Arrêt: Pas de nouvelles associations depuis 5 scrolls")
                
                # Dernière tentative: scroll jusqu'au bout et attendre
                logging.info("   🔄 Dernière tentative: scroll jusqu'au bout")
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(10)
                
                final_associations = self.extract_associations_from_page()
                final_new = []
                for assoc in final_associations:
                    if assoc['rna'] not in self.all_associations:
                        self.all_associations.add(assoc['rna'])
                        final_new.append(assoc)
                
                if final_new:
                    logging.info(f"   🎯 {len(final_new)} associations supplémentaires trouvées!")
                    current_total = len(self.all_associations)
                
                break
            
            # Pause réaliste entre les scrolls
            time.sleep(2)
        
        # Résultats finaux
        final_associations_list = self.extract_associations_from_page()
        
        scroll_results.update({
            'final_count': len(self.all_associations),
            'total_scrolls_performed': scroll_num + 1,
            'associations_found': len(self.all_associations) > scroll_results['initial_count'],
            'final_associations': final_associations_list,
            'unique_rnas': list(self.all_associations)
        })
        
        logging.info(f"\n🎉 RÉSULTATS FINAUX:")
        logging.info(f"   📊 Associations initiales: {scroll_results['initial_count']}")
        logging.info(f"   📊 Associations finales: {scroll_results['final_count']}")
        logging.info(f"   📊 Nouvelles associations: {scroll_results['final_count'] - scroll_results['initial_count']}")
        logging.info(f"   📜 Scrolls effectués: {scroll_results['total_scrolls_performed']}")
        logging.info(f"   ✅ Scroll infini détecté: {'OUI' if scroll_results['associations_found'] else 'NON'}")
        
        return scroll_results
    
    def save_results(self, results):
        """
        Sauvegarde les résultats
        """
        with open('realistic_scroll_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # Sauvegarder aussi la liste des RNAs
        if results.get('unique_rnas'):
            with open('all_associations_rnas.txt', 'w', encoding='utf-8') as f:
                for rna in results['unique_rnas']:
                    f.write(f"{rna}\n")
        
        logging.info("💾 Résultats sauvegardés:")
        logging.info("   📄 realistic_scroll_results.json")
        logging.info("   📋 all_associations_rnas.txt")

def main():
    """
    Test principal réaliste
    """
    print("🖱️ TEST RÉALISTE DU SCROLL INFINI")
    print("=" * 60)
    print("Ce test simule exactement le comportement d'un utilisateur")
    print("qui scrolle manuellement vers le bas pour voir plus d'associations.")
    print()
    print("Le test va:")
    print("- Scroller progressivement comme avec une molette")
    print("- Attendre le chargement à chaque étape")
    print("- Détecter les nouvelles associations")
    print("- Continuer jusqu'à trouver 100 associations ou 100 scrolls")
    print()
    
    response = input("Voulez-vous continuer? (o/n): ").lower().strip()
    if response != 'o':
        print("Test annulé.")
        return
    
    tester = RealisticScrollTester()
    
    try:
        tester.start_driver()
        
        # Test réaliste
        results = tester.realistic_infinite_scroll_test(
            max_scrolls=100, 
            target_associations=100
        )
        
        # Sauvegarder
        tester.save_results(results)
        
        # Afficher le résumé
        print(f"\n🎉 RÉSULTATS DU TEST RÉALISTE:")
        print(f"   📊 Associations initiales: {results['initial_count']}")
        print(f"   📊 Associations finales: {results['final_count']}")
        print(f"   📈 Nouvelles associations: {results['final_count'] - results['initial_count']}")
        print(f"   📜 Scrolls effectués: {results['total_scrolls_performed']}")
        
        if results['associations_found']:
            print(f"   🎉 SCROLL INFINI CONFIRMÉ!")
            print(f"   🏢 Total associations uniques: {len(results.get('unique_rnas', []))}")
            
            # Afficher quelques exemples
            if results.get('unique_rnas'):
                print(f"\n📋 EXEMPLES DE RNAs TROUVÉS:")
                for i, rna in enumerate(results['unique_rnas'][:10]):
                    print(f"   {i+1}. {rna}")
        else:
            print(f"   ⚠️ Pas de scroll infini détecté")
        
        print(f"\n💾 Résultats détaillés sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
