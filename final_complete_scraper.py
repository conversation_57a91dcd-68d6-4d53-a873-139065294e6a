import time
import json
import csv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class CompleteFrenchAssociationScraper:
    def __init__(self, max_workers=3):
        """
        Scraper complet pour TOUTES les associations françaises
        Stratégie: Scraper par département (101 départements × 20 associations = ~2,020 associations)
        """
        self.max_workers = max_workers
        self.thread_local = threading.local()
        
        # Liste complète des départements français
        self.departments = [
            # Métropole (01-95)
            *[f"{i:02d}" for i in range(1, 96)],
            # DOM-TOM
            '971', '972', '973', '974', '975', '976', '977', '978', '984', '986', '987', '988'
        ]
        
        # Statistiques globales
        self.stats = {
            'total_departments': len(self.departments),
            'processed_departments': 0,
            'total_associations': 0,
            'unique_associations': 0,
            'emails_found': 0,
            'phones_found': 0,
            'start_time': None,
            'end_time': None,
            'failed_departments': []
        }
        
        self.all_associations = []
        self.seen_rnas = set()  # Pour éviter les doublons
        
    def get_driver(self):
        """
        Obtient un driver pour le thread actuel
        """
        if not hasattr(self.thread_local, 'driver'):
            options = Options()
            options.add_argument('--headless')  # Mode invisible pour la vitesse
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            service = Service(ChromeDriverManager().install())
            self.thread_local.driver = webdriver.Chrome(service=service, options=options)
            self.thread_local.wait = WebDriverWait(self.thread_local.driver, 15)
            
        return self.thread_local.driver, self.thread_local.wait
    
    def close_driver(self):
        """
        Ferme le driver du thread actuel
        """
        if hasattr(self.thread_local, 'driver'):
            self.thread_local.driver.quit()
            delattr(self.thread_local, 'driver')
            delattr(self.thread_local, 'wait')
    
    def scrape_department(self, department_code):
        """
        Scrape toutes les associations d'un département
        """
        driver, wait = self.get_driver()
        department_associations = []
        
        try:
            url = f"https://www.data-asso.fr/annuaire?departement={department_code}"
            logging.info(f"🏛️ Scraping département {department_code}: {url}")
            
            driver.get(url)
            time.sleep(3)
            
            # Attendre que la page se charge
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Récupérer les liens d'associations
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            association_links = []
            
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    full_url = 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                    association_links.append(full_url)
            
            # Supprimer les doublons
            association_links = list(set(association_links))
            
            logging.info(f"   📋 {len(association_links)} associations trouvées pour le département {department_code}")
            
            # Scraper chaque association
            for i, assoc_url in enumerate(association_links):
                try:
                    assoc_data = self.scrape_single_association(driver, assoc_url, department_code)
                    if assoc_data:
                        department_associations.append(assoc_data)
                    
                    # Pause respectueuse
                    time.sleep(0.5)
                    
                except Exception as e:
                    logging.error(f"   ❌ Erreur association {assoc_url}: {e}")
                    continue
            
            logging.info(f"   ✅ Département {department_code}: {len(department_associations)} associations extraites")
            
        except Exception as e:
            logging.error(f"❌ Erreur département {department_code}: {e}")
            self.stats['failed_departments'].append(department_code)
        
        finally:
            self.stats['processed_departments'] += 1
        
        return department_associations
    
    def scrape_single_association(self, driver, association_url, department_code):
        """
        Scrape une association individuelle
        """
        try:
            driver.get(association_url)
            time.sleep(2)
            
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Structure de données
            data = {
                'url': association_url,
                'department': department_code,
                'nom': '',
                'rna': '',
                'siren': '',
                'adresse': '',
                'adresse_gestion': '',
                'telephone': '',
                'email': '',
                'site_web': '',
                'ville': '',
                'code_postal': '',
                'activite': '',
                'statut': 'Données partielles'
            }
            
            # Extraire RNA depuis l'URL
            rna_match = re.search(r'/association/([A-Z0-9]+)', association_url)
            if rna_match:
                data['rna'] = rna_match.group(1)
                
                # Vérifier si déjà traité (éviter doublons)
                if data['rna'] in self.seen_rnas:
                    return None
                self.seen_rnas.add(data['rna'])
            
            # Extraire le nom
            title = soup.find('h1')
            if title and title.get_text(strip=True):
                data['nom'] = title.get_text(strip=True)
            
            # Extraire SIREN
            text_content = soup.get_text()
            siren_match = re.search(r'SIREN[:\s]*([0-9]{9})', text_content, re.IGNORECASE)
            if siren_match:
                data['siren'] = siren_match.group(1)
            
            # Extraire les coordonnées
            fields_mapping = {
                'Adresse': 'adresse',
                'Adresse de gestion': 'adresse_gestion',
                'Téléphone': 'telephone',
                'Courriel': 'email',
                'Email': 'email',
                'Site internet': 'site_web'
            }
            
            for field_name, data_key in fields_mapping.items():
                value = self.extract_field_value(soup, field_name)
                if value and value != '-':
                    data[data_key] = value
            
            # Extraire ville et code postal
            if data['adresse']:
                postal_match = re.search(r'(\d{5})\s*([A-Z\s\-À-ÿ]+)', data['adresse'], re.IGNORECASE)
                if postal_match:
                    data['code_postal'] = postal_match.group(1)
                    data['ville'] = postal_match.group(2).strip()
            
            # Extraire l'activité depuis le texte de la carte
            activity_match = re.search(r'association([A-Za-z\s]+)', text_content, re.IGNORECASE)
            if activity_match:
                data['activite'] = activity_match.group(1).strip()
            
            # Mettre à jour les statistiques
            if data['email'] and data['email'] != '-':
                self.stats['emails_found'] += 1
            if data['telephone'] and data['telephone'] != '-':
                self.stats['phones_found'] += 1
            
            self.stats['total_associations'] += 1
            
            return data
            
        except Exception as e:
            logging.debug(f"Erreur extraction {association_url}: {e}")
            return None
    
    def extract_field_value(self, soup, field_name):
        """
        Extrait la valeur d'un champ spécifique
        """
        try:
            # Méthode 1: Recherche directe dans le texte
            pattern = rf'{re.escape(field_name)}\s*:?\s*([^\n\r]+)'
            text_content = soup.get_text()
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if value and value != '-':
                    return value
            
            # Méthode 2: Recherche dans les éléments
            for element in soup.find_all(string=re.compile(field_name, re.IGNORECASE)):
                parent = element.parent
                if parent:
                    next_elem = parent.find_next()
                    if next_elem:
                        value = next_elem.get_text(strip=True)
                        if value and value != '-':
                            return value
            
        except Exception as e:
            logging.debug(f"Erreur extraction champ {field_name}: {e}")
        
        return None
    
    def scrape_all_departments_parallel(self):
        """
        Scrape tous les départements en parallèle
        """
        logging.info(f"🚀 DÉBUT DU SCRAPING COMPLET")
        logging.info(f"📊 {len(self.departments)} départements à traiter")
        logging.info(f"⚡ {self.max_workers} threads simultanés")
        
        self.stats['start_time'] = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Soumettre toutes les tâches
            future_to_dept = {
                executor.submit(self.scrape_department, dept): dept 
                for dept in self.departments
            }
            
            # Récupérer les résultats au fur et à mesure
            for future in as_completed(future_to_dept):
                dept = future_to_dept[future]
                try:
                    department_associations = future.result()
                    if department_associations:
                        self.all_associations.extend(department_associations)
                    
                    # Afficher le progrès
                    progress = (self.stats['processed_departments'] / len(self.departments)) * 100
                    logging.info(f"📊 Progrès: {self.stats['processed_departments']}/{len(self.departments)} départements ({progress:.1f}%)")
                    logging.info(f"   🏢 Total associations: {len(self.all_associations)}")
                    logging.info(f"   📧 Emails: {self.stats['emails_found']}")
                    logging.info(f"   📞 Téléphones: {self.stats['phones_found']}")
                
                except Exception as e:
                    logging.error(f"❌ Erreur département {dept}: {e}")
        
        self.stats['end_time'] = time.time()
        self.stats['unique_associations'] = len(self.all_associations)
        
        # Fermer tous les drivers
        for _ in range(self.max_workers):
            try:
                self.close_driver()
            except:
                pass
        
        return self.all_associations
    
    def save_results(self, filename_base='associations_france_complete'):
        """
        Sauvegarde les résultats complets
        """
        if not self.all_associations:
            logging.warning("⚠️ Aucune donnée à sauvegarder")
            return
        
        # JSON complet
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_associations': len(self.all_associations),
                    'scraping_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'departments_processed': self.stats['processed_departments'],
                    'emails_found': self.stats['emails_found'],
                    'phones_found': self.stats['phones_found']
                },
                'associations': self.all_associations
            }, f, ensure_ascii=False, indent=2)
        
        # CSV pour Excel
        csv_file = f"{filename_base}.csv"
        fieldnames = ['nom', 'rna', 'siren', 'adresse', 'adresse_gestion', 
                     'telephone', 'email', 'site_web', 'ville', 'code_postal', 
                     'activite', 'department', 'statut', 'url']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in self.all_associations:
                writer.writerow(item)
        
        # CSV des emails uniquement
        emails_file = f"{filename_base}_emails.csv"
        associations_with_emails = [a for a in self.all_associations if a.get('email') and a['email'] != '-']
        
        with open(emails_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in associations_with_emails:
                writer.writerow(item)
        
        # CSV des téléphones uniquement
        phones_file = f"{filename_base}_telephones.csv"
        associations_with_phones = [a for a in self.all_associations if a.get('telephone') and a['telephone'] != '-']
        
        with open(phones_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in associations_with_phones:
                writer.writerow(item)
        
        logging.info(f"💾 Sauvegardé:")
        logging.info(f"   📄 {json_file} - Données complètes JSON")
        logging.info(f"   📊 {csv_file} - Toutes les associations CSV")
        logging.info(f"   📧 {emails_file} - {len(associations_with_emails)} associations avec email")
        logging.info(f"   📞 {phones_file} - {len(associations_with_phones)} associations avec téléphone")
    
    def print_final_stats(self):
        """
        Affiche les statistiques finales
        """
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            
            print(f"\n🎉 SCRAPING COMPLET TERMINÉ!")
            print(f"=" * 60)
            print(f"⏱️ Durée totale: {duration/60:.1f} minutes ({duration:.0f} secondes)")
            print(f"🏛️ Départements traités: {self.stats['processed_departments']}/{self.stats['total_departments']}")
            print(f"🏢 Associations trouvées: {self.stats['unique_associations']}")
            print(f"📧 Emails récupérés: {self.stats['emails_found']}")
            print(f"📞 Téléphones récupérés: {self.stats['phones_found']}")
            
            if self.stats['unique_associations'] > 0:
                email_rate = (self.stats['emails_found'] / self.stats['unique_associations']) * 100
                phone_rate = (self.stats['phones_found'] / self.stats['unique_associations']) * 100
                print(f"📈 Taux d'emails: {email_rate:.1f}%")
                print(f"📈 Taux de téléphones: {phone_rate:.1f}%")
            
            if self.stats['failed_departments']:
                print(f"⚠️ Départements échoués: {', '.join(self.stats['failed_departments'])}")
            
            rate = self.stats['unique_associations'] / duration if duration > 0 else 0
            print(f"🚀 Vitesse moyenne: {rate:.1f} associations/seconde")

def main():
    """
    Scraper principal - TOUTES LES ASSOCIATIONS FRANÇAISES
    """
    print("🇫🇷 SCRAPER COMPLET - TOUTES LES ASSOCIATIONS FRANÇAISES")
    print("=" * 70)
    print("Ce scraper va récupérer TOUTES les associations disponibles")
    print("en scrapant les 101 départements français.")
    print("Estimation: ~2,000 associations avec emails et téléphones")
    print()
    
    # Demander confirmation
    response = input("Voulez-vous continuer? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = CompleteFrenchAssociationScraper(max_workers=4)  # 4 threads pour optimiser
    
    try:
        # Scraping complet
        results = scraper.scrape_all_departments_parallel()
        
        if results:
            # Sauvegarder
            scraper.save_results()
            
            # Afficher quelques exemples
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS RÉCUPÉRÉES:")
            for i, assoc in enumerate(results[:10]):
                print(f"\n{i+1}. {assoc.get('nom', 'N/A')} (Dept. {assoc.get('department', 'N/A')})")
                print(f"   🏢 RNA: {assoc.get('rna', 'N/A')}")
                print(f"   📞 Téléphone: {assoc.get('telephone', 'N/A')}")
                print(f"   📧 Email: {assoc.get('email', 'N/A')}")
                print(f"   🏙️ Ville: {assoc.get('ville', 'N/A')}")
        
        # Statistiques finales
        scraper.print_final_stats()
        
    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrompu par l'utilisateur")
        scraper.save_results()  # Sauvegarder ce qui a été récupéré
        scraper.print_final_stats()
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
