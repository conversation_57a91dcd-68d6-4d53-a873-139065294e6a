import pandas as pd
import json
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)

def create_simple_excel():
    """
    Crée un fichier Excel simple à partir des associations
    """
    print("📊 CRÉATION DU FICHIER EXCEL SIMPLE")
    
    try:
        # Charger les données JSON
        with open('mega_scraper_all_associations.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        associations = data.get('associations', [])
        print(f"📊 {len(associations)} associations trouvées")
        
        # Préparer les données pour Excel
        excel_data = []
        
        for i, assoc in enumerate(associations):
            try:
                # Extraire les données principales
                nom_complet = str(assoc.get('nom', ''))
                
                # Séparer les informations du nom (qui contient tout)
                parts = nom_complet.split(';') if ';' in nom_complet else [nom_complet]
                
                row = {
                    'RNA': assoc.get('rna', ''),
                    'Nom_Association': parts[0] if parts else nom_complet[:100],
                    'Description_Complete': nom_complet,
                    'Source': assoc.get('source', 'data.gouv.fr'),
                    'SIREN': assoc.get('siren', ''),
                    'URL': assoc.get('url', ''),
                    'Email': '',  # Vide pour l'instant
                    'Telephone': '',  # Vide pour l'instant
                    'Site_Web': '',  # Vide pour l'instant
                    'Notes': ''  # Vide pour l'instant
                }
                
                excel_data.append(row)
                
                if i % 10000 == 0:
                    print(f"   📊 {i}/{len(associations)} associations traitées")
            
            except Exception as e:
                print(f"Erreur ligne {i}: {e}")
                continue
        
        # Créer le DataFrame
        df = pd.DataFrame(excel_data)
        
        # Créer le fichier Excel
        excel_filename = 'Associations_Francaises.xlsx'
        
        print(f"💾 Création du fichier {excel_filename}...")
        
        # Sauvegarder en Excel
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        
        print(f"\n✅ FICHIER EXCEL CRÉÉ AVEC SUCCÈS!")
        print(f"📁 Nom: {excel_filename}")
        print(f"📊 Lignes: {len(df)}")
        print(f"📊 Colonnes: {len(df.columns)}")
        print(f"\n📋 Colonnes disponibles:")
        for col in df.columns:
            print(f"   - {col}")
        
        return excel_filename
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def main():
    """
    Fonction principale
    """
    print("📊 CONVERTISSEUR EXCEL SIMPLE")
    print("=" * 50)
    print("Conversion de vos associations en fichier Excel")
    print()
    
    excel_file = create_simple_excel()
    
    if excel_file:
        print(f"\n🎉 SUCCÈS!")
        print(f"Votre fichier Excel '{excel_file}' est prêt!")
        print(f"\n📊 Contenu:")
        print(f"   - RNA officiel de chaque association")
        print(f"   - Nom de l'association")
        print(f"   - Description complète")
        print(f"   - Source des données")
        print(f"   - Colonnes vides pour Email/Téléphone/Site web")
        print(f"\n💡 Vous pouvez maintenant ouvrir ce fichier dans Excel!")
    else:
        print(f"\n❌ Échec de la création du fichier Excel")

if __name__ == "__main__":
    main()
