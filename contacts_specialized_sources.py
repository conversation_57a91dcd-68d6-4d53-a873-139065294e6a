import requests
import json
import csv
import re
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import urllib.parse

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SpecializedContactsScraper:
    def __init__(self):
        """
        Scraper spécialisé pour sources où les associations publient leurs contacts
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.associations_with_contacts = []
        
        # Patterns pour extraire emails et téléphones
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_patterns = [
            re.compile(r'0[1-9](?:[0-9]{8})'),
            re.compile(r'\+33[1-9](?:[0-9]{8})'),
            re.compile(r'(?:0[1-9])(?:[-.\s]?[0-9]{2}){4}'),
        ]
        
    def extract_contacts_from_text(self, text):
        """Extrait emails et téléphones d'un texte"""
        emails = self.email_pattern.findall(text)
        phones = []
        
        for pattern in self.phone_patterns:
            phones.extend(pattern.findall(text))
        
        # Nettoyer
        emails = list(set(emails))
        phones = list(set([re.sub(r'[-.\s]', '', phone) for phone in phones if len(re.sub(r'[-.\s]', '', phone)) >= 10]))
        
        return emails, phones
    
    def scrape_helloasso_detailed(self):
        """Scrape HelloAsso avec Selenium pour les contacts"""
        logging.info("💰 HELLOASSO - Recherche détaillée")
        
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            
            try:
                # Aller sur HelloAsso
                driver.get("https://www.helloasso.com/associations")
                time.sleep(5)
                
                # Chercher des associations avec des liens
                association_links = []
                links = driver.find_elements(By.TAG_NAME, "a")
                
                for link in links:
                    href = link.get_attribute('href')
                    if href and '/associations/' in href:
                        association_links.append(href)
                
                logging.info(f"   🔗 {len(association_links)} liens d'associations trouvés")
                
                # Visiter les premières pages d'associations
                for i, url in enumerate(association_links[:20]):  # Limiter à 20
                    try:
                        driver.get(url)
                        time.sleep(2)
                        
                        # Extraire le nom
                        try:
                            title = driver.find_element(By.TAG_NAME, "h1").text
                        except:
                            title = "Association HelloAsso"
                        
                        # Chercher des contacts dans la page
                        page_text = driver.page_source
                        emails, phones = self.extract_contacts_from_text(page_text)
                        
                        if emails or phones:
                            self.associations_with_contacts.append({
                                'nom': title,
                                'email': emails[0] if emails else '',
                                'telephone': phones[0] if phones else '',
                                'source': 'HelloAsso',
                                'url': url
                            })
                            
                            logging.info(f"   🎯 {title[:40]}...")
                            if emails:
                                logging.info(f"      📧 {emails[0]}")
                            if phones:
                                logging.info(f"      📞 {phones[0]}")
                    
                    except Exception as e:
                        logging.debug(f"Erreur page {url}: {e}")
            
            finally:
                driver.quit()
                
        except Exception as e:
            logging.error(f"Erreur HelloAsso: {e}")
    
    def scrape_lemouvementassociatif(self):
        """Scrape Le Mouvement Associatif"""
        logging.info("🤝 LE MOUVEMENT ASSOCIATIF")
        
        try:
            url = "https://lemouvementassociatif.org/annuaire/"
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher des liens vers des associations
                for link in soup.find_all('a', href=True):
                    if 'association' in link['href'].lower() or 'membre' in link['href'].lower():
                        try:
                            detail_url = link['href']
                            if detail_url.startswith('/'):
                                detail_url = 'https://lemouvementassociatif.org' + detail_url
                            
                            # Visiter la page de détail
                            detail_response = self.session.get(detail_url, timeout=10)
                            if detail_response.status_code == 200:
                                emails, phones = self.extract_contacts_from_text(detail_response.text)
                                
                                if emails or phones:
                                    nom = link.get_text(strip=True) or "Association Mouvement Associatif"
                                    
                                    self.associations_with_contacts.append({
                                        'nom': nom,
                                        'email': emails[0] if emails else '',
                                        'telephone': phones[0] if phones else '',
                                        'source': 'Mouvement Associatif',
                                        'url': detail_url
                                    })
                                    
                                    logging.info(f"   🎯 {nom[:40]}...")
                                    if emails:
                                        logging.info(f"      📧 {emails[0]}")
                                    if phones:
                                        logging.info(f"      📞 {phones[0]}")
                            
                            time.sleep(1)
                            
                        except Exception as e:
                            logging.debug(f"Erreur détail: {e}")
                            
        except Exception as e:
            logging.error(f"Erreur Mouvement Associatif: {e}")
    
    def scrape_francebenevolat(self):
        """Scrape France Bénévolat"""
        logging.info("🙋 FRANCE BÉNÉVOLAT")
        
        try:
            url = "https://www.francebenevolat.org/associations"
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher des associations
                for element in soup.find_all(class_=lambda x: x and 'association' in str(x).lower()):
                    try:
                        text = element.get_text()
                        emails, phones = self.extract_contacts_from_text(text)
                        
                        if emails or phones:
                            # Chercher le nom dans l'élément ou ses parents
                            nom = "Association France Bénévolat"
                            for parent in [element, element.parent]:
                                if parent:
                                    title_elem = parent.find(['h1', 'h2', 'h3', 'h4'])
                                    if title_elem:
                                        nom = title_elem.get_text(strip=True)
                                        break
                            
                            self.associations_with_contacts.append({
                                'nom': nom,
                                'email': emails[0] if emails else '',
                                'telephone': phones[0] if phones else '',
                                'source': 'France Bénévolat',
                                'url': url
                            })
                            
                            logging.info(f"   🎯 {nom[:40]}...")
                            if emails:
                                logging.info(f"      📧 {emails[0]}")
                            if phones:
                                logging.info(f"      📞 {phones[0]}")
                    
                    except Exception as e:
                        logging.debug(f"Erreur élément: {e}")
                        
        except Exception as e:
            logging.error(f"Erreur France Bénévolat: {e}")
    
    def scrape_associations_gouv_selenium(self):
        """Scrape associations.gouv.fr avec Selenium"""
        logging.info("🏛️ ASSOCIATIONS.GOUV.FR - Selenium")
        
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            
            try:
                driver.get("https://www.associations.gouv.fr")
                time.sleep(5)
                
                # Chercher un formulaire de recherche
                try:
                    search_input = driver.find_element(By.XPATH, "//input[@type='text' or @type='search']")
                    search_input.send_keys("association")
                    search_input.submit()
                    time.sleep(5)
                    
                    # Analyser les résultats
                    page_text = driver.page_source
                    emails, phones = self.extract_contacts_from_text(page_text)
                    
                    if emails or phones:
                        self.associations_with_contacts.append({
                            'nom': "Association gouvernementale",
                            'email': emails[0] if emails else '',
                            'telephone': phones[0] if phones else '',
                            'source': 'associations.gouv.fr',
                            'url': driver.current_url
                        })
                        
                        logging.info(f"   🎯 Contacts gouvernementaux trouvés")
                        if emails:
                            logging.info(f"      📧 {emails[0]}")
                        if phones:
                            logging.info(f"      📞 {phones[0]}")
                
                except Exception as e:
                    logging.debug(f"Pas de formulaire de recherche: {e}")
            
            finally:
                driver.quit()
                
        except Exception as e:
            logging.error(f"Erreur associations.gouv.fr: {e}")
    
    def scrape_local_directories(self):
        """Scrape des annuaires locaux"""
        logging.info("📍 ANNUAIRES LOCAUX")
        
        # Annuaires locaux connus
        directories = [
            "https://www.gralon.net/annuaire/associations/",
            "https://www.net1901.org/",
            "https://www.associations-france.org/",
        ]
        
        for directory_url in directories:
            try:
                logging.info(f"   🔍 {directory_url}")
                response = self.session.get(directory_url, timeout=15)
                
                if response.status_code == 200:
                    emails, phones = self.extract_contacts_from_text(response.text)
                    
                    if emails or phones:
                        self.associations_with_contacts.append({
                            'nom': f"Association {directory_url.split('//')[1].split('/')[0]}",
                            'email': emails[0] if emails else '',
                            'telephone': phones[0] if phones else '',
                            'source': 'Annuaire local',
                            'url': directory_url
                        })
                        
                        logging.info(f"   🎯 Contacts trouvés sur {directory_url}")
                        if emails:
                            logging.info(f"      📧 {emails[0]}")
                        if phones:
                            logging.info(f"      📞 {phones[0]}")
                
                time.sleep(2)
                
            except Exception as e:
                logging.debug(f"Erreur {directory_url}: {e}")
    
    def run_specialized_scraping(self):
        """Lance le scraping spécialisé"""
        logging.info("🎯 SCRAPING SOURCES SPÉCIALISÉES POUR CONTACTS")
        
        # Lancer toutes les méthodes
        self.scrape_helloasso_detailed()
        self.scrape_lemouvementassociatif()
        self.scrape_francebenevolat()
        self.scrape_associations_gouv_selenium()
        self.scrape_local_directories()
        
        return len(self.associations_with_contacts)
    
    def save_specialized_results(self):
        """Sauvegarde les résultats spécialisés"""
        if not self.associations_with_contacts:
            logging.warning("⚠️ Aucun contact trouvé dans les sources spécialisées")
            return
        
        # CSV des contacts spécialisés
        with open('contacts_sources_specialisees.csv', 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['nom', 'email', 'telephone', 'source', 'url']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for assoc in self.associations_with_contacts:
                writer.writerow(assoc)
        
        # JSON
        with open('contacts_sources_specialisees.json', 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_contacts': len(self.associations_with_contacts),
                    'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'method': 'specialized_sources'
                },
                'associations': self.associations_with_contacts
            }, f, ensure_ascii=False, indent=2)
        
        logging.info("💾 Contacts spécialisés sauvegardés:")
        logging.info("   📊 contacts_sources_specialisees.csv")
        logging.info("   📄 contacts_sources_specialisees.json")

def main():
    """
    Scraper principal sources spécialisées
    """
    print("🎯 SCRAPER SOURCES SPÉCIALISÉES - CONTACTS ASSOCIATIONS")
    print("=" * 60)
    print("Ce scraper va chercher sur des sources où les associations")
    print("publient VOLONTAIREMENT leurs contacts:")
    print()
    print("1. HelloAsso - Plateforme de financement")
    print("2. Le Mouvement Associatif - Réseau")
    print("3. France Bénévolat - Bénévolat")
    print("4. associations.gouv.fr - Officiel")
    print("5. Annuaires locaux spécialisés")
    print()
    
    scraper = SpecializedContactsScraper()
    
    try:
        start_time = time.time()
        
        # Scraping spécialisé
        contacts_found = scraper.run_specialized_scraping()
        
        # Sauvegarder
        scraper.save_specialized_results()
        
        # Résumé
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎯 SCRAPING SPÉCIALISÉ TERMINÉ!")
        print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
        print(f"   📧 Contacts trouvés: {contacts_found}")
        
        if scraper.associations_with_contacts:
            print(f"\n📧 CONTACTS TROUVÉS:")
            for i, assoc in enumerate(scraper.associations_with_contacts):
                print(f"   {i+1}. {assoc['nom'][:40]}...")
                if assoc.get('email'):
                    print(f"      📧 {assoc['email']}")
                if assoc.get('telephone'):
                    print(f"      📞 {assoc['telephone']}")
                print(f"      🔗 Source: {assoc['source']}")
        
        print(f"\n💾 Fichiers contacts spécialisés créés!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
