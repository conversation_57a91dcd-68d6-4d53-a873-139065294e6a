import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deep_scroll_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DeepScrollTester:
    def __init__(self):
        """
        Testeur approfondi du scroll infini
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Activer les logs réseau pour voir les requêtes AJAX
        self.options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def get_associations_count(self):
        """
        Compte le nombre d'associations actuellement visibles
        """
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Différentes méthodes pour compter les associations
            methods = {
                'links_with_association': len([link for link in soup.find_all('a', href=True) if '/association/' in link['href']]),
                'cards_with_association_class': len(soup.find_all(class_=lambda x: x and 'association' in ' '.join(x).lower())),
                'divs_with_association_class': len(soup.find_all('div', class_=lambda x: x and 'association' in ' '.join(x).lower())),
                'elements_with_rna': len(re.findall(r'RNA[:\s]*([A-Z0-9]+)', soup.get_text(), re.IGNORECASE))
            }
            
            # Prendre la méthode qui donne le plus de résultats
            best_method = max(methods, key=methods.get)
            count = methods[best_method]
            
            logging.debug(f"Méthodes de comptage: {methods}")
            logging.debug(f"Meilleure méthode: {best_method} = {count}")
            
            return count, methods
            
        except Exception as e:
            logging.error(f"Erreur comptage: {e}")
            return 0, {}
    
    def get_network_requests(self):
        """
        Récupère les requêtes réseau récentes
        """
        try:
            logs = self.driver.get_log('performance')
            requests = []
            
            for log in logs:
                try:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.responseReceived':
                        response = message['message']['params']['response']
                        url = response['url']
                        
                        # Filtrer les requêtes intéressantes
                        if any(keyword in url.lower() for keyword in ['data-asso', 'api', 'ajax', 'search', 'load']):
                            requests.append({
                                'url': url,
                                'status': response['status'],
                                'mimeType': response['mimeType'],
                                'timestamp': message['message']['params']['timestamp']
                            })
                except:
                    continue
            
            return requests
            
        except Exception as e:
            logging.debug(f"Erreur requêtes réseau: {e}")
            return []
    
    def test_scroll_infinite_comprehensive(self):
        """
        Test complet du scroll infini avec différentes techniques
        """
        logging.info("📜 TEST COMPLET DU SCROLL INFINI")
        
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🌐 Accès à {url}")
        
        self.driver.get(url)
        time.sleep(10)  # Attendre le chargement complet
        
        # État initial
        initial_count, initial_methods = self.get_associations_count()
        initial_height = self.driver.execute_script("return document.body.scrollHeight")
        initial_requests = self.get_network_requests()
        
        logging.info(f"📊 État initial:")
        logging.info(f"   Associations: {initial_count}")
        logging.info(f"   Hauteur page: {initial_height}px")
        logging.info(f"   Requêtes réseau: {len(initial_requests)}")
        
        scroll_results = {
            'initial_state': {
                'associations_count': initial_count,
                'page_height': initial_height,
                'counting_methods': initial_methods,
                'network_requests': len(initial_requests)
            },
            'scroll_tests': []
        }
        
        # Test 1: Scroll progressif lent
        logging.info("\n🔄 TEST 1: Scroll progressif lent")
        max_associations = initial_count
        
        for i in range(20):  # 20 tentatives de scroll
            scroll_position = (i + 1) * 500
            
            logging.info(f"📜 Scroll {i+1}: position {scroll_position}px")
            
            # Scroll lent et progressif
            self.driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(3)  # Attendre plus longtemps
            
            # Mesurer les changements
            new_count, new_methods = self.get_associations_count()
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            new_requests = self.get_network_requests()
            
            scroll_test = {
                'scroll_number': i + 1,
                'scroll_position': scroll_position,
                'associations_count': new_count,
                'page_height': new_height,
                'network_requests': len(new_requests),
                'associations_increased': new_count > max_associations,
                'height_increased': new_height > initial_height,
                'new_requests': len(new_requests) > len(initial_requests)
            }
            
            scroll_results['scroll_tests'].append(scroll_test)
            
            logging.info(f"   📊 Associations: {initial_count} -> {new_count}")
            logging.info(f"   📏 Hauteur: {initial_height} -> {new_height}px")
            logging.info(f"   📡 Requêtes: {len(initial_requests)} -> {len(new_requests)}")
            
            if new_count > max_associations:
                max_associations = new_count
                logging.info(f"   🎯 NOUVELLES ASSOCIATIONS DÉTECTÉES! Total: {new_count}")
            
            if scroll_test['new_requests']:
                logging.info(f"   📡 NOUVELLES REQUÊTES RÉSEAU DÉTECTÉES!")
                # Afficher les nouvelles requêtes
                for req in new_requests[-3:]:  # 3 dernières requêtes
                    logging.info(f"      🌐 {req['url']}")
            
            # Arrêter si on atteint le bas de la page
            current_scroll = self.driver.execute_script("return window.pageYOffset + window.innerHeight")
            if current_scroll >= new_height:
                logging.info(f"   📜 Bas de page atteint")
                break
            
            # Arrêter si pas de changement depuis 5 scrolls
            if i >= 5:
                recent_counts = [test['associations_count'] for test in scroll_results['scroll_tests'][-5:]]
                if len(set(recent_counts)) == 1:  # Tous identiques
                    logging.info(f"   ⏹️ Pas de changement depuis 5 scrolls")
                    break
        
        # Test 2: Scroll jusqu'au bout d'un coup
        logging.info("\n🔄 TEST 2: Scroll jusqu'au bout")
        
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        final_count, final_methods = self.get_associations_count()
        final_height = self.driver.execute_script("return document.body.scrollHeight")
        final_requests = self.get_network_requests()
        
        logging.info(f"   📊 Après scroll complet:")
        logging.info(f"      Associations: {initial_count} -> {final_count}")
        logging.info(f"      Hauteur: {initial_height} -> {final_height}px")
        logging.info(f"      Requêtes: {len(initial_requests)} -> {len(final_requests)}")
        
        # Test 3: Scroll avec touches clavier
        logging.info("\n🔄 TEST 3: Scroll avec touches clavier")
        
        # Remonter en haut
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)
        
        # Utiliser les touches Page Down
        body = self.driver.find_element(By.TAG_NAME, "body")
        
        for i in range(10):
            logging.info(f"⌨️ Page Down {i+1}")
            body.send_keys(Keys.PAGE_DOWN)
            time.sleep(2)
            
            keyboard_count, _ = self.get_associations_count()
            keyboard_requests = self.get_network_requests()
            
            logging.info(f"   📊 Associations: {keyboard_count}")
            
            if keyboard_count > max_associations:
                max_associations = keyboard_count
                logging.info(f"   🎯 NOUVELLES ASSOCIATIONS VIA CLAVIER! Total: {keyboard_count}")
        
        # Test 4: Attendre plus longtemps après scroll
        logging.info("\n🔄 TEST 4: Scroll avec attente prolongée")
        
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)
        
        # Scroll avec attente de 10 secondes
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        logging.info("   ⏳ Attente de 10 secondes...")
        time.sleep(10)
        
        long_wait_count, _ = self.get_associations_count()
        long_wait_requests = self.get_network_requests()
        
        logging.info(f"   📊 Après attente prolongée:")
        logging.info(f"      Associations: {initial_count} -> {long_wait_count}")
        logging.info(f"      Requêtes: {len(initial_requests)} -> {len(long_wait_requests)}")
        
        if long_wait_count > max_associations:
            max_associations = long_wait_count
            logging.info(f"   🎯 NOUVELLES ASSOCIATIONS APRÈS ATTENTE! Total: {long_wait_count}")
        
        # Résultats finaux
        scroll_results['final_results'] = {
            'max_associations_found': max_associations,
            'scroll_infinite_detected': max_associations > initial_count,
            'final_associations_count': long_wait_count,
            'final_page_height': final_height,
            'total_network_requests': len(long_wait_requests)
        }
        
        # Extraire les associations uniques si on en a trouvé plus
        if max_associations > initial_count:
            logging.info("\n🎯 EXTRACTION DES ASSOCIATIONS SUPPLÉMENTAIRES")
            
            final_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            all_association_links = []
            
            for link in final_soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    full_url = 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                    
                    # Extraire RNA depuis l'URL
                    rna_match = re.search(r'/association/([A-Z0-9]+)', link['href'])
                    if rna_match:
                        rna = rna_match.group(1)
                        all_association_links.append({
                            'url': full_url,
                            'rna': rna,
                            'text': link.get_text(strip=True)[:100]
                        })
            
            # Supprimer les doublons par RNA
            unique_associations = {}
            for assoc in all_association_links:
                unique_associations[assoc['rna']] = assoc
            
            scroll_results['unique_associations'] = list(unique_associations.values())
            
            logging.info(f"   🎉 TOTAL ASSOCIATIONS UNIQUES: {len(unique_associations)}")
            
            # Afficher quelques exemples
            for i, assoc in enumerate(list(unique_associations.values())[:10]):
                logging.info(f"   {i+1}. RNA: {assoc['rna']} - {assoc['text'][:50]}...")
        
        return scroll_results
    
    def save_scroll_results(self, results):
        """
        Sauvegarde les résultats du test de scroll
        """
        with open('deep_scroll_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logging.info("💾 Résultats sauvegardés dans: deep_scroll_results.json")

def main():
    """
    Test principal du scroll infini approfondi
    """
    print("📜 TEST APPROFONDI DU SCROLL INFINI")
    print("=" * 60)
    print("Ce test va vérifier de manière exhaustive si le scroll")
    print("infini fonctionne sur data-asso.fr avec différentes techniques:")
    print("- Scroll progressif lent")
    print("- Scroll direct jusqu'au bout")
    print("- Scroll avec touches clavier")
    print("- Scroll avec attente prolongée")
    print()
    
    tester = DeepScrollTester()
    
    try:
        tester.start_driver()
        
        # Test complet du scroll
        results = tester.test_scroll_infinite_comprehensive()
        
        # Sauvegarder les résultats
        tester.save_scroll_results(results)
        
        # Afficher le résumé
        final_results = results.get('final_results', {})
        
        print(f"\n📊 RÉSULTATS FINAUX:")
        print(f"   📊 Associations initiales: {results['initial_state']['associations_count']}")
        print(f"   📊 Associations maximales trouvées: {final_results.get('max_associations_found', 0)}")
        print(f"   📜 Scroll infini détecté: {'✅' if final_results.get('scroll_infinite_detected') else '❌'}")
        
        if final_results.get('scroll_infinite_detected'):
            print(f"   🎉 SCROLL INFINI CONFIRMÉ!")
            print(f"   🏢 Total associations uniques: {len(results.get('unique_associations', []))}")
            
            if results.get('unique_associations'):
                print(f"\n📋 EXEMPLES D'ASSOCIATIONS SUPPLÉMENTAIRES:")
                for i, assoc in enumerate(results['unique_associations'][:5]):
                    print(f"   {i+1}. RNA: {assoc['rna']} - {assoc['text'][:50]}...")
        else:
            print(f"   ⚠️ Pas de scroll infini détecté")
            print(f"   💡 Le site semble limité à {results['initial_state']['associations_count']} associations")
        
        print(f"\n💾 Résultats détaillés dans: deep_scroll_results.json")
        print(f"📄 Log complet dans: deep_scroll_test.log")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
