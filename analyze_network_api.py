import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

from webdriver_manager.chrome import ChromeDriverManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class NetworkAnalyzer:
    def __init__(self):
        """
        Analyseur de requêtes réseau pour trouver l'API de data-asso.fr
        """
        # Configuration pour capturer les requêtes réseau
        
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--enable-network-service-logging')
        
        self.driver = None
        self.network_requests = []
        
    def start_driver(self):
        """Démarre le driver avec capture réseau"""
        try:
            service = Service(ChromeDriverManager().install())

            # Activer les logs de performance dans les options
            self.options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})

            self.driver = webdriver.Chrome(
                service=service,
                options=self.options
            )
            logging.info("✅ Driver démarré avec capture réseau")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def capture_network_requests(self, url, duration=30):
        """
        Capture toutes les requêtes réseau pendant la navigation
        """
        logging.info(f"🌐 Analyse réseau de {url}")
        
        try:
            # Aller sur la page
            self.driver.get(url)
            time.sleep(5)
            
            # Capturer les requêtes initiales
            logs = self.driver.get_log('performance')
            self.process_logs(logs)
            
            # Faire quelques actions pour déclencher plus de requêtes
            logging.info("🔄 Actions pour déclencher des requêtes...")
            
            # Scroll pour voir s'il y a des requêtes AJAX
            for i in range(5):
                self.driver.execute_script(f"window.scrollTo(0, {500 * (i + 1)});")
                time.sleep(2)
                
                # Capturer les nouvelles requêtes
                new_logs = self.driver.get_log('performance')
                self.process_logs(new_logs)
            
            # Essayer de cliquer sur des éléments
            try:
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons[:3]:  # Tester les 3 premiers boutons
                    if button.is_displayed() and button.is_enabled():
                        logging.info(f"🔘 Clic sur bouton: {button.text}")
                        button.click()
                        time.sleep(3)
                        
                        # Capturer les requêtes après clic
                        click_logs = self.driver.get_log('performance')
                        self.process_logs(click_logs)
            except Exception as e:
                logging.debug(f"Erreur clic boutons: {e}")
            
            # Analyser les requêtes trouvées
            self.analyze_requests()
            
        except Exception as e:
            logging.error(f"❌ Erreur capture: {e}")
    
    def process_logs(self, logs):
        """
        Traite les logs de performance pour extraire les requêtes réseau
        """
        for log in logs:
            try:
                message = json.loads(log['message'])
                
                if message['message']['method'] == 'Network.responseReceived':
                    response = message['message']['params']['response']
                    request_url = response['url']
                    
                    # Filtrer les requêtes intéressantes
                    if self.is_interesting_request(request_url):
                        request_info = {
                            'url': request_url,
                            'method': response.get('requestHeaders', {}).get(':method', 'GET'),
                            'status': response['status'],
                            'mimeType': response['mimeType'],
                            'timestamp': message['message']['params']['timestamp']
                        }
                        
                        # Éviter les doublons
                        if not any(req['url'] == request_url for req in self.network_requests):
                            self.network_requests.append(request_info)
                            logging.info(f"📡 Requête: {request_url}")
                
            except Exception as e:
                logging.debug(f"Erreur traitement log: {e}")
    
    def is_interesting_request(self, url):
        """
        Détermine si une requête est intéressante pour l'analyse
        """
        # Filtrer les requêtes qui pourraient contenir des données d'associations
        interesting_keywords = [
            'api', 'ajax', 'json', 'data', 'association', 'search', 'query',
            'annuaire', 'list', 'fetch', 'load', 'get', 'post'
        ]
        
        # Exclure les ressources statiques
        excluded_extensions = [
            '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg',
            '.woff', '.woff2', '.ttf', '.eot'
        ]
        
        url_lower = url.lower()
        
        # Exclure les ressources statiques
        if any(ext in url_lower for ext in excluded_extensions):
            return False
        
        # Inclure les requêtes avec mots-clés intéressants
        if any(keyword in url_lower for keyword in interesting_keywords):
            return True
        
        # Inclure les requêtes vers le même domaine
        if 'data-asso.fr' in url_lower:
            return True
        
        return False
    
    def analyze_requests(self):
        """
        Analyse les requêtes capturées pour identifier les APIs
        """
        logging.info(f"\n📊 ANALYSE DE {len(self.network_requests)} REQUÊTES")
        
        api_candidates = []
        data_requests = []
        
        for req in self.network_requests:
            url = req['url']
            
            # Identifier les candidats API
            if any(keyword in url.lower() for keyword in ['api', 'ajax', 'json']):
                api_candidates.append(req)
            
            # Identifier les requêtes de données
            if req['mimeType'] in ['application/json', 'text/json']:
                data_requests.append(req)
        
        logging.info(f"🎯 Candidats API: {len(api_candidates)}")
        logging.info(f"📄 Requêtes JSON: {len(data_requests)}")
        
        # Afficher les candidats les plus prometteurs
        if api_candidates:
            logging.info("\n🚀 CANDIDATS API PROMETTEURS:")
            for i, req in enumerate(api_candidates[:5]):
                logging.info(f"   {i+1}. {req['url']}")
                logging.info(f"      Status: {req['status']}, Type: {req['mimeType']}")
        
        if data_requests:
            logging.info("\n📊 REQUÊTES DE DONNÉES:")
            for i, req in enumerate(data_requests[:5]):
                logging.info(f"   {i+1}. {req['url']}")
        
        return {
            'total_requests': len(self.network_requests),
            'api_candidates': api_candidates,
            'data_requests': data_requests,
            'all_requests': self.network_requests
        }

def main():
    """
    Analyse principale des requêtes réseau
    """
    print("🔍 ANALYSE DES REQUÊTES RÉSEAU - DATA-ASSO")
    print("=" * 60)
    
    analyzer = NetworkAnalyzer()
    
    try:
        analyzer.start_driver()
        
        # Analyser la page d'annuaire
        results = analyzer.capture_network_requests("https://www.data-asso.fr/annuaire")
        
        # Sauvegarder les résultats
        with open('network_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 RÉSULTATS:")
        print(f"   📡 Total requêtes: {results['total_requests']}")
        print(f"   🎯 Candidats API: {len(results['api_candidates'])}")
        print(f"   📄 Requêtes JSON: {len(results['data_requests'])}")
        
        if results['api_candidates']:
            print(f"\n🚀 APIS POTENTIELLES TROUVÉES:")
            for i, req in enumerate(results['api_candidates'][:3]):
                print(f"   {i+1}. {req['url']}")
        
        print(f"\n💾 Analyse complète sauvegardée dans: network_analysis.json")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        analyzer.close_driver()

if __name__ == "__main__":
    main()
