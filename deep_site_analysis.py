import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deep_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DeepSiteAnalyzer:
    def __init__(self):
        """
        Analyseur approfondi du site data-asso.fr
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Activer les logs réseau
        self.options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        self.driver = None
        self.wait = None
        self.analysis_results = {}
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def analyze_page_structure(self):
        """
        Analyse complète de la structure de la page
        """
        logging.info("🔍 ANALYSE DE LA STRUCTURE DE LA PAGE")
        
        url = "https://www.data-asso.fr/annuaire"
        self.driver.get(url)
        time.sleep(10)  # Attendre le chargement complet
        
        # 1. Analyser le HTML complet
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        
        structure_analysis = {
            'title': self.driver.title,
            'url': self.driver.current_url,
            'total_elements': len(soup.find_all()),
            'scripts': len(soup.find_all('script')),
            'forms': len(soup.find_all('form')),
            'buttons': len(soup.find_all('button')),
            'inputs': len(soup.find_all('input')),
            'links': len(soup.find_all('a')),
            'divs': len(soup.find_all('div')),
        }
        
        # 2. Chercher des éléments spécifiques aux associations
        association_elements = {
            'association_links': [],
            'association_cards': [],
            'pagination_elements': [],
            'filter_elements': [],
            'search_elements': []
        }
        
        # Liens d'associations
        for link in soup.find_all('a', href=True):
            if '/association/' in link['href']:
                association_elements['association_links'].append({
                    'href': link['href'],
                    'text': link.get_text(strip=True),
                    'class': link.get('class', [])
                })
        
        # Éléments de pagination
        pagination_keywords = ['page', 'next', 'prev', 'suivant', 'précédent', 'plus', 'charger', 'load']
        for keyword in pagination_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for elem in elements:
                if elem.parent:
                    association_elements['pagination_elements'].append({
                        'text': elem.strip(),
                        'tag': elem.parent.name,
                        'class': elem.parent.get('class', []),
                        'id': elem.parent.get('id', '')
                    })
        
        # Éléments de recherche/filtres
        search_elements = soup.find_all(['input', 'select', 'button'], 
                                       attrs={'type': ['search', 'text', 'submit']})
        for elem in search_elements:
            association_elements['search_elements'].append({
                'tag': elem.name,
                'type': elem.get('type'),
                'name': elem.get('name'),
                'id': elem.get('id'),
                'class': elem.get('class', []),
                'placeholder': elem.get('placeholder', '')
            })
        
        self.analysis_results['structure'] = structure_analysis
        self.analysis_results['association_elements'] = association_elements
        
        logging.info(f"📊 Structure analysée: {structure_analysis['total_elements']} éléments")
        logging.info(f"🏢 {len(association_elements['association_links'])} liens d'associations trouvés")
        
        return structure_analysis, association_elements
    
    def analyze_network_requests(self):
        """
        Analyse approfondie des requêtes réseau
        """
        logging.info("🌐 ANALYSE DES REQUÊTES RÉSEAU")
        
        # Capturer les requêtes initiales
        initial_logs = self.driver.get_log('performance')
        
        # Effectuer différentes actions pour déclencher des requêtes
        actions_performed = []
        
        try:
            # 1. Scroll progressif
            logging.info("📜 Test du scroll...")
            for i in range(10):
                self.driver.execute_script(f"window.scrollTo(0, {300 * (i + 1)});")
                time.sleep(1)
            actions_performed.append("scroll_progressive")
            
            # 2. Cliquer sur tous les boutons visibles
            logging.info("🔘 Test des boutons...")
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            for i, button in enumerate(buttons):
                try:
                    if button.is_displayed() and button.is_enabled():
                        button_text = button.text.strip()
                        logging.info(f"   Clic sur: '{button_text}'")
                        button.click()
                        time.sleep(2)
                        actions_performed.append(f"button_click_{i}_{button_text}")
                except Exception as e:
                    logging.debug(f"Erreur clic bouton {i}: {e}")
            
            # 3. Tester les champs de recherche
            logging.info("🔍 Test des champs de recherche...")
            search_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='search'], input[type='text']")
            for i, input_elem in enumerate(search_inputs):
                try:
                    if input_elem.is_displayed() and input_elem.is_enabled():
                        input_elem.clear()
                        input_elem.send_keys("test")
                        time.sleep(1)
                        input_elem.clear()
                        actions_performed.append(f"search_input_{i}")
                except Exception as e:
                    logging.debug(f"Erreur input {i}: {e}")
            
            # 4. Tester les sélecteurs/dropdowns
            logging.info("📋 Test des sélecteurs...")
            selects = self.driver.find_elements(By.TAG_NAME, "select")
            for i, select in enumerate(selects):
                try:
                    if select.is_displayed() and select.is_enabled():
                        options = select.find_elements(By.TAG_NAME, "option")
                        if len(options) > 1:
                            options[1].click()  # Sélectionner la 2ème option
                            time.sleep(1)
                        actions_performed.append(f"select_{i}")
                except Exception as e:
                    logging.debug(f"Erreur select {i}: {e}")
            
        except Exception as e:
            logging.error(f"Erreur actions: {e}")
        
        # Capturer toutes les requêtes après actions
        all_logs = self.driver.get_log('performance')
        
        # Analyser les requêtes
        network_requests = []
        for log in all_logs:
            try:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.responseReceived':
                    response = message['message']['params']['response']
                    url = response['url']
                    
                    if 'data-asso.fr' in url:
                        network_requests.append({
                            'url': url,
                            'status': response['status'],
                            'mimeType': response['mimeType'],
                            'method': response.get('requestHeaders', {}).get(':method', 'GET'),
                            'timestamp': message['message']['params']['timestamp']
                        })
            except Exception as e:
                logging.debug(f"Erreur log: {e}")
        
        self.analysis_results['network'] = {
            'actions_performed': actions_performed,
            'total_requests': len(network_requests),
            'requests': network_requests
        }
        
        logging.info(f"📡 {len(network_requests)} requêtes réseau capturées")
        
        return network_requests
    
    def test_different_urls(self):
        """
        Teste différents patterns d'URL pour trouver plus d'associations
        """
        logging.info("🌐 TEST DE DIFFÉRENTS PATTERNS D'URL")
        
        base_urls = [
            "https://www.data-asso.fr/annuaire",
            "https://www.data-asso.fr/annuaire/",
            "https://www.data-asso.fr/search",
            "https://www.data-asso.fr/associations",
            "https://www.data-asso.fr/liste",
        ]
        
        # Paramètres à tester
        params_to_test = [
            {},
            {'page': 1}, {'page': 2}, {'page': 3},
            {'limit': 50}, {'limit': 100}, {'limit': 500},
            {'size': 50}, {'size': 100}, {'size': 500},
            {'per_page': 50}, {'per_page': 100},
            {'offset': 0}, {'offset': 20}, {'offset': 50},
            {'start': 0}, {'start': 20}, {'start': 50},
            {'q': ''}, {'query': ''}, {'search': ''},
            {'type': 'association'}, {'format': 'json'},
        ]
        
        # Départements français
        departments = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', 
                      '75', '69', '13', '59', '62', '33', '31', '44', '67', '76']
        
        url_test_results = []
        
        # Test des URLs de base
        for base_url in base_urls:
            for params in params_to_test[:10]:  # Limiter pour ne pas être trop long
                try:
                    # Construire l'URL avec paramètres
                    if params:
                        param_str = '&'.join([f"{k}={v}" for k, v in params.items()])
                        test_url = f"{base_url}?{param_str}"
                    else:
                        test_url = base_url
                    
                    logging.info(f"🌐 Test: {test_url}")
                    self.driver.get(test_url)
                    time.sleep(3)
                    
                    # Compter les associations
                    soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                    association_links = [link for link in soup.find_all('a', href=True) 
                                       if '/association/' in link['href']]
                    
                    result = {
                        'url': test_url,
                        'status': 'success',
                        'associations_count': len(association_links),
                        'page_title': self.driver.title,
                        'current_url': self.driver.current_url
                    }
                    
                    url_test_results.append(result)
                    logging.info(f"   📊 {len(association_links)} associations trouvées")
                    
                    if len(association_links) > 20:  # Si on trouve plus que la normale
                        logging.info(f"   🎯 URL PROMETTEUSE: {test_url}")
                    
                except Exception as e:
                    url_test_results.append({
                        'url': test_url,
                        'status': 'error',
                        'error': str(e)
                    })
                    logging.debug(f"   ❌ Erreur: {e}")
                
                time.sleep(1)  # Pause respectueuse
        
        # Test avec départements
        for dept in departments[:5]:  # Tester 5 départements
            try:
                dept_url = f"https://www.data-asso.fr/annuaire?departement={dept}"
                logging.info(f"🏛️ Test département {dept}: {dept_url}")
                
                self.driver.get(dept_url)
                time.sleep(3)
                
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                association_links = [link for link in soup.find_all('a', href=True) 
                                   if '/association/' in link['href']]
                
                url_test_results.append({
                    'url': dept_url,
                    'status': 'success',
                    'associations_count': len(association_links),
                    'type': 'department'
                })
                
                logging.info(f"   📊 Département {dept}: {len(association_links)} associations")
                
            except Exception as e:
                logging.debug(f"Erreur département {dept}: {e}")
        
        self.analysis_results['url_tests'] = url_test_results
        
        return url_test_results
    
    def analyze_javascript_and_apis(self):
        """
        Analyse le JavaScript et les APIs utilisées
        """
        logging.info("⚙️ ANALYSE JAVASCRIPT ET APIs")
        
        # Récupérer tous les scripts
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        scripts = soup.find_all('script')
        
        js_analysis = {
            'total_scripts': len(scripts),
            'inline_scripts': 0,
            'external_scripts': [],
            'api_endpoints': [],
            'interesting_variables': [],
            'ajax_calls': []
        }
        
        for script in scripts:
            if script.get('src'):
                js_analysis['external_scripts'].append(script['src'])
            else:
                js_analysis['inline_scripts'] += 1
                
                # Analyser le contenu du script
                if script.string:
                    content = script.string.lower()
                    
                    # Chercher des endpoints API
                    api_patterns = [
                        r'["\']https?://[^"\']*api[^"\']*["\']',
                        r'["\']https?://[^"\']*gw[^"\']*["\']',
                        r'["\']https?://[^"\']*search[^"\']*["\']',
                        r'["\']https?://[^"\']*data[^"\']*["\']'
                    ]
                    
                    for pattern in api_patterns:
                        matches = re.findall(pattern, content)
                        js_analysis['api_endpoints'].extend(matches)
                    
                    # Chercher des appels AJAX
                    ajax_patterns = [
                        r'fetch\s*\([^)]+\)',
                        r'axios\.[^(]+\([^)]+\)',
                        r'\.get\s*\([^)]+\)',
                        r'\.post\s*\([^)]+\)',
                        r'XMLHttpRequest'
                    ]
                    
                    for pattern in ajax_patterns:
                        matches = re.findall(pattern, content)
                        js_analysis['ajax_calls'].extend(matches)
        
        self.analysis_results['javascript'] = js_analysis
        
        logging.info(f"📜 {js_analysis['total_scripts']} scripts analysés")
        logging.info(f"🔗 {len(js_analysis['api_endpoints'])} endpoints API potentiels")
        
        return js_analysis
    
    def comprehensive_analysis(self):
        """
        Analyse complète du site
        """
        logging.info("🚀 DÉBUT DE L'ANALYSE COMPLÈTE")
        
        try:
            # 1. Structure de la page
            structure, elements = self.analyze_page_structure()
            
            # 2. Requêtes réseau
            network_requests = self.analyze_network_requests()
            
            # 3. Test d'URLs
            url_results = self.test_different_urls()
            
            # 4. JavaScript et APIs
            js_analysis = self.analyze_javascript_and_apis()
            
            # 5. Résumé final
            summary = {
                'total_association_links_found': len(elements['association_links']),
                'best_urls': [r for r in url_results if r.get('associations_count', 0) > 20],
                'api_endpoints': list(set(js_analysis['api_endpoints'])),
                'network_requests_count': len(network_requests),
                'promising_methods': []
            }
            
            # Identifier les méthodes prometteuses
            if summary['best_urls']:
                summary['promising_methods'].append('URL_PARAMETERS')
            
            if any('api' in req['url'].lower() for req in network_requests):
                summary['promising_methods'].append('API_DIRECT')
            
            if any('search' in req['url'].lower() for req in network_requests):
                summary['promising_methods'].append('SEARCH_API')
            
            self.analysis_results['summary'] = summary
            
            # Sauvegarder l'analyse complète
            with open('complete_site_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
            
            logging.info("✅ ANALYSE COMPLÈTE TERMINÉE")
            
            return self.analysis_results
            
        except Exception as e:
            logging.error(f"❌ Erreur analyse complète: {e}")
            return None

def main():
    """
    Analyse principale
    """
    print("🔍 ANALYSE COMPLÈTE ET APPROFONDIE - DATA-ASSO.FR")
    print("=" * 70)
    print("Cette analyse va prendre plusieurs minutes...")
    print()
    
    analyzer = DeepSiteAnalyzer()
    
    try:
        analyzer.start_driver()
        
        # Analyse complète
        results = analyzer.comprehensive_analysis()
        
        if results:
            print(f"\n📊 RÉSULTATS DE L'ANALYSE:")
            print(f"   🏢 Associations trouvées: {results['summary']['total_association_links_found']}")
            print(f"   🌐 URLs prometteuses: {len(results['summary']['best_urls'])}")
            print(f"   📡 Requêtes réseau: {results['summary']['network_requests_count']}")
            print(f"   🎯 Méthodes prometteuses: {', '.join(results['summary']['promising_methods'])}")
            
            if results['summary']['best_urls']:
                print(f"\n🚀 URLS PROMETTEUSES:")
                for url_info in results['summary']['best_urls']:
                    print(f"   ✅ {url_info['url']} -> {url_info['associations_count']} associations")
            
            if results['summary']['api_endpoints']:
                print(f"\n🔗 ENDPOINTS API TROUVÉS:")
                for endpoint in results['summary']['api_endpoints'][:5]:
                    print(f"   📡 {endpoint}")
            
            print(f"\n💾 Analyse complète sauvegardée dans: complete_site_analysis.json")
            print(f"📄 Log détaillé dans: deep_analysis.log")
            
        else:
            print("\n❌ Échec de l'analyse")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        analyzer.close_driver()

if __name__ == "__main__":
    main()
