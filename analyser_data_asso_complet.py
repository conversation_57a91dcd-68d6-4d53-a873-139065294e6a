#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR COMPLET DE DATA-ASSO.FR
Analyse en profondeur du site pour trouver la meilleure méthode
"""

import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import urllib.parse

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyse_data_asso.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AnalyseurDataAsso:
    def __init__(self):
        """Analyseur complet de data-asso.fr"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.driver = None
        self.wait = None
        self.associations_trouvees = {}
        
    def start_driver(self):
        """Démarre le driver Selenium"""
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        self.wait = WebDriverWait(self.driver, 30)
        logging.info("✅ Driver démarré")
    
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def analyser_structure_site(self):
        """ÉTAPE 1: Analyser la structure complète du site"""
        logging.info("🔍 ÉTAPE 1: Analyse structure du site")
        
        try:
            self.driver.get("https://www.data-asso.fr")
            time.sleep(5)
            
            # Analyser la page d'accueil
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Trouver tous les liens
            liens = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                text = link.get_text(strip=True)
                
                if href.startswith('/') or 'data-asso.fr' in href:
                    liens.append({
                        'url': href,
                        'text': text,
                        'type': self.categoriser_lien(href, text)
                    })
            
            logging.info(f"   📊 {len(liens)} liens trouvés")
            
            # Catégoriser les liens
            categories = {}
            for lien in liens:
                cat = lien['type']
                if cat not in categories:
                    categories[cat] = []
                categories[cat].append(lien)
            
            logging.info(f"   📋 Catégories trouvées:")
            for cat, liens_cat in categories.items():
                logging.info(f"      📊 {cat}: {len(liens_cat)} liens")
                
                # Afficher quelques exemples
                for lien in liens_cat[:3]:
                    logging.info(f"         - {lien['url']} ({lien['text'][:30]}...)")
            
            return categories
            
        except Exception as e:
            logging.error(f"   ❌ Erreur analyse structure: {e}")
            return {}
    
    def categoriser_lien(self, href, text):
        """Catégorise un lien selon son contenu"""
        href_lower = href.lower()
        text_lower = text.lower()
        
        if '/association/' in href_lower:
            return 'association_individuelle'
        elif '/annuaire' in href_lower or '/recherche' in href_lower:
            return 'annuaire_recherche'
        elif '/api' in href_lower or '/export' in href_lower:
            return 'api_export'
        elif 'contact' in href_lower or 'contact' in text_lower:
            return 'contact'
        elif any(word in href_lower for word in ['admin', 'login', 'auth']):
            return 'administration'
        elif any(word in text_lower for word in ['télécharger', 'download', 'csv', 'json']):
            return 'telechargement'
        else:
            return 'autre'
    
    def analyser_page_annuaire_detaillee(self):
        """ÉTAPE 2: Analyse détaillée de la page annuaire"""
        logging.info("🔍 ÉTAPE 2: Analyse détaillée page annuaire")
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Analyser le HTML complet
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Chercher tous les formulaires
            formulaires = soup.find_all('form')
            logging.info(f"   📋 {len(formulaires)} formulaires trouvés")
            
            for i, form in enumerate(formulaires):
                logging.info(f"      📋 Formulaire {i+1}:")
                
                # Analyser les inputs
                inputs = form.find_all('input')
                for inp in inputs:
                    name = inp.get('name', 'sans_nom')
                    type_inp = inp.get('type', 'text')
                    placeholder = inp.get('placeholder', '')
                    logging.info(f"         📝 Input: {name} (type: {type_inp}, placeholder: {placeholder})")
                
                # Analyser les selects
                selects = form.find_all('select')
                for sel in selects:
                    name = sel.get('name', 'sans_nom')
                    options = [opt.get_text(strip=True) for opt in sel.find_all('option')]
                    logging.info(f"         📋 Select: {name} ({len(options)} options)")
                    if len(options) <= 10:
                        logging.info(f"            Options: {', '.join(options)}")
            
            # Chercher des éléments de pagination
            pagination = soup.find_all(class_=lambda x: x and any(word in str(x).lower() for word in ['page', 'next', 'prev', 'pagination']))
            logging.info(f"   📄 {len(pagination)} éléments de pagination trouvés")
            
            # Chercher des boutons
            boutons = soup.find_all('button')
            logging.info(f"   🔘 {len(boutons)} boutons trouvés")
            for bouton in boutons:
                text = bouton.get_text(strip=True)
                onclick = bouton.get('onclick', '')
                logging.info(f"      🔘 Bouton: '{text}' (onclick: {onclick[:50]}...)")
            
            # Chercher des scripts JavaScript
            scripts = soup.find_all('script')
            logging.info(f"   🔧 {len(scripts)} scripts JavaScript trouvés")
            
            for script in scripts:
                if script.string:
                    script_content = script.string
                    
                    # Chercher des URLs d'API
                    api_urls = re.findall(r'["\']([^"\']*(?:api|search|association)[^"\']*)["\']', script_content)
                    if api_urls:
                        logging.info(f"      🔗 URLs API trouvées: {api_urls}")
                    
                    # Chercher des fonctions intéressantes
                    functions = re.findall(r'function\s+(\w+)', script_content)
                    if functions:
                        logging.info(f"      ⚙️ Fonctions: {functions}")
            
            return True
            
        except Exception as e:
            logging.error(f"   ❌ Erreur analyse annuaire: {e}")
            return False
    
    def tester_methodes_recherche_avancees(self):
        """ÉTAPE 3: Tester des méthodes de recherche avancées"""
        logging.info("🔍 ÉTAPE 3: Test méthodes recherche avancées")
        
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(5)
            
            # Méthode 1: Recherche par caractères spéciaux
            caracteres_speciaux = ['*', '%', '_', '?', '+', '.', '^', '$']
            
            for char in caracteres_speciaux:
                try:
                    # Chercher un champ de recherche
                    search_input = self.driver.find_element(By.XPATH, "//input[@type='text' or @type='search']")
                    search_input.clear()
                    search_input.send_keys(char)
                    search_input.send_keys(Keys.RETURN)
                    time.sleep(3)
                    
                    # Compter les résultats
                    soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                    associations = soup.find_all('a', href=lambda x: x and '/association/' in x)
                    
                    if len(associations) > 0:
                        logging.info(f"      🎯 Caractère '{char}': {len(associations)} associations trouvées")
                        
                        # Extraire quelques associations
                        for assoc in associations[:5]:
                            href = assoc['href']
                            rna_match = re.search(r'/association/([A-Z0-9]+)', href)
                            if rna_match:
                                rna = rna_match.group(1)
                                nom = assoc.get_text(strip=True)
                                self.associations_trouvees[rna] = {
                                    'rna': rna,
                                    'nom': nom,
                                    'methode': f'caractere_special_{char}'
                                }
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur caractère {char}: {e}")
            
            # Méthode 2: Recherche par codes postaux
            codes_postaux = ['75001', '69001', '13001', '31000', '06000']
            
            for cp in codes_postaux:
                try:
                    search_input = self.driver.find_element(By.XPATH, "//input[@type='text' or @type='search']")
                    search_input.clear()
                    search_input.send_keys(cp)
                    search_input.send_keys(Keys.RETURN)
                    time.sleep(3)
                    
                    soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                    associations = soup.find_all('a', href=lambda x: x and '/association/' in x)
                    
                    if len(associations) > 0:
                        logging.info(f"      🎯 Code postal '{cp}': {len(associations)} associations")
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur CP {cp}: {e}")
            
            # Méthode 3: Recherche par années
            annees = ['2020', '2021', '2022', '2023', '2024']
            
            for annee in annees:
                try:
                    search_input = self.driver.find_element(By.XPATH, "//input[@type='text' or @type='search']")
                    search_input.clear()
                    search_input.send_keys(annee)
                    search_input.send_keys(Keys.RETURN)
                    time.sleep(3)
                    
                    soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                    associations = soup.find_all('a', href=lambda x: x and '/association/' in x)
                    
                    if len(associations) > 0:
                        logging.info(f"      🎯 Année '{annee}': {len(associations)} associations")
                    
                except Exception as e:
                    logging.debug(f"      ❌ Erreur année {annee}: {e}")
            
            return len(self.associations_trouvees)
            
        except Exception as e:
            logging.error(f"   ❌ Erreur méthodes avancées: {e}")
            return 0
    
    def analyser_pages_individuelles(self):
        """ÉTAPE 4: Analyser des pages individuelles d'associations"""
        logging.info("🔍 ÉTAPE 4: Analyse pages individuelles")
        
        if not self.associations_trouvees:
            logging.warning("   ⚠️ Aucune association trouvée pour analyser")
            return {}
        
        contacts_trouves = {}
        
        # Analyser les 10 premières associations
        for i, (rna, assoc) in enumerate(list(self.associations_trouvees.items())[:10]):
            try:
                url = f"https://www.data-asso.fr/association/{rna}"
                logging.info(f"   🔍 Analyse {i+1}/10: {rna}")
                
                self.driver.get(url)
                time.sleep(3)
                
                # Analyser le contenu de la page
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # Chercher des contacts
                page_text = soup.get_text()
                
                # Patterns pour emails et téléphones
                emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', page_text)
                phones = re.findall(r'0[1-9](?:[0-9]{8})', page_text)
                
                if emails or phones:
                    contacts_trouves[rna] = {
                        'rna': rna,
                        'nom': assoc['nom'],
                        'emails': emails,
                        'phones': phones,
                        'url': url
                    }
                    
                    logging.info(f"      🎯 Contacts trouvés:")
                    if emails:
                        logging.info(f"         📧 Emails: {emails}")
                    if phones:
                        logging.info(f"         📞 Téléphones: {phones}")
                
                # Analyser la structure de la page
                sections = soup.find_all(['div', 'section', 'article'])
                for section in sections:
                    if section.get('class'):
                        classes = ' '.join(section.get('class'))
                        if any(word in classes.lower() for word in ['contact', 'info', 'detail']):
                            logging.info(f"      📋 Section intéressante: {classes}")
                
            except Exception as e:
                logging.debug(f"      ❌ Erreur analyse {rna}: {e}")
        
        return contacts_trouves
    
    def tester_apis_cachees(self):
        """ÉTAPE 5: Tester des APIs cachées"""
        logging.info("🔍 ÉTAPE 5: Test APIs cachées")
        
        # URLs d'API potentielles
        api_urls = [
            "https://www.data-asso.fr/api/associations",
            "https://www.data-asso.fr/api/search",
            "https://www.data-asso.fr/api/annuaire",
            "https://www.data-asso.fr/api/v1/associations",
            "https://www.data-asso.fr/api/v2/associations",
            "https://www.data-asso.fr/search/api",
            "https://www.data-asso.fr/annuaire/api",
            "https://www.data-asso.fr/export/csv",
            "https://www.data-asso.fr/export/json",
            "https://www.data-asso.fr/download/associations",
        ]
        
        apis_fonctionnelles = []
        
        for api_url in api_urls:
            try:
                logging.info(f"   🔗 Test: {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                logging.info(f"      📊 Status: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    logging.info(f"      📄 Content-Type: {content_type}")
                    
                    # Analyser le contenu
                    if 'json' in content_type:
                        try:
                            data = response.json()
                            logging.info(f"      📊 JSON: {len(data) if isinstance(data, list) else 'Object'}")
                            apis_fonctionnelles.append({
                                'url': api_url,
                                'type': 'json',
                                'data': data
                            })
                        except:
                            logging.info(f"      ❌ JSON invalide")
                    
                    elif 'csv' in content_type or api_url.endswith('.csv'):
                        lines = response.text.split('\n')
                        logging.info(f"      📊 CSV: {len(lines)} lignes")
                        apis_fonctionnelles.append({
                            'url': api_url,
                            'type': 'csv',
                            'lines': len(lines)
                        })
                    
                    else:
                        # Chercher des associations dans le HTML
                        associations = re.findall(r'W[0-9]{9}[A-Z]?', response.text)
                        if associations:
                            logging.info(f"      🎯 {len(set(associations))} RNAs trouvés")
                            apis_fonctionnelles.append({
                                'url': api_url,
                                'type': 'html',
                                'rnas': list(set(associations))
                            })
                
            except Exception as e:
                logging.debug(f"      ❌ Erreur {api_url}: {e}")
        
        return apis_fonctionnelles
    
    def run_analyse_complete(self):
        """Lance l'analyse complète"""
        logging.info("🚀 ANALYSE COMPLÈTE DE DATA-ASSO.FR")
        logging.info("=" * 60)
        
        try:
            self.start_driver()
            
            # ÉTAPE 1: Structure du site
            structure = self.analyser_structure_site()
            
            # ÉTAPE 2: Page annuaire détaillée
            annuaire_ok = self.analyser_page_annuaire_detaillee()
            
            # ÉTAPE 3: Méthodes de recherche avancées
            nb_associations = self.tester_methodes_recherche_avancees()
            
            # ÉTAPE 4: Pages individuelles
            contacts = self.analyser_pages_individuelles()
            
            # ÉTAPE 5: APIs cachées
            apis = self.tester_apis_cachees()
            
            # Résultats finaux
            resultats = {
                'structure_site': structure,
                'annuaire_analyse': annuaire_ok,
                'associations_trouvees': len(self.associations_trouvees),
                'contacts_trouves': len(contacts),
                'apis_fonctionnelles': len(apis),
                'details': {
                    'associations': list(self.associations_trouvees.values()),
                    'contacts': list(contacts.values()),
                    'apis': apis
                }
            }
            
            # Sauvegarder les résultats
            with open('analyse_complete_data_asso.json', 'w', encoding='utf-8') as f:
                json.dump(resultats, f, ensure_ascii=False, indent=2)
            
            # Afficher le résumé
            print(f"\n🎉 ANALYSE COMPLÈTE TERMINÉE!")
            print(f"   📊 Associations trouvées: {len(self.associations_trouvees)}")
            print(f"   📧 Contacts trouvés: {len(contacts)}")
            print(f"   🔗 APIs fonctionnelles: {len(apis)}")
            
            if contacts:
                print(f"\n📧 CONTACTS TROUVÉS:")
                for rna, contact in contacts.items():
                    print(f"   📊 {rna}: {contact['nom'][:40]}...")
                    if contact['emails']:
                        print(f"      📧 {', '.join(contact['emails'])}")
                    if contact['phones']:
                        print(f"      📞 {', '.join(contact['phones'])}")
            
            if apis:
                print(f"\n🔗 APIS FONCTIONNELLES:")
                for api in apis:
                    print(f"   🔗 {api['url']} ({api['type']})")
            
            print(f"\n💾 Résultats sauvegardés: analyse_complete_data_asso.json")
            
            return resultats
            
        except Exception as e:
            logging.error(f"❌ Erreur analyse complète: {e}")
            return None
            
        finally:
            self.close_driver()

def main():
    """Fonction principale"""
    print("🔍 ANALYSEUR COMPLET DE DATA-ASSO.FR")
    print("=" * 60)
    print("Analyse en profondeur du site pour trouver la meilleure méthode")
    print("de récupération d'associations avec contacts.")
    print()
    
    analyseur = AnalyseurDataAsso()
    resultats = analyseur.run_analyse_complete()
    
    if resultats:
        print(f"\n🎯 ANALYSE RÉUSSIE!")
        print(f"Consultez le fichier 'analyse_complete_data_asso.json' pour les détails complets.")
    else:
        print(f"\n❌ ANALYSE ÉCHOUÉE!")

if __name__ == "__main__":
    main()
