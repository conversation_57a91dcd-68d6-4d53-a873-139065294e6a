import time
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_button_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ExtractButtonTester:
    def __init__(self):
        """
        Testeur spécialisé pour le bouton "Extraire la liste"
        """
        self.options = Options()
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Configurer le dossier de téléchargement
        download_dir = os.path.join(os.getcwd(), "downloads")
        os.makedirs(download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        self.options.add_experimental_option("prefs", prefs)
        
        self.driver = None
        self.wait = None
        self.download_dir = download_dir
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def test_extract_button_comprehensive(self):
        """
        Test complet du bouton "Extraire la liste"
        """
        logging.info("🔘 TEST COMPLET DU BOUTON 'EXTRAIRE LA LISTE'")
        
        try:
            # Aller sur la page d'annuaire
            url = "https://www.data-asso.fr/annuaire"
            logging.info(f"🌐 Accès à {url}")
            self.driver.get(url)
            time.sleep(10)  # Attendre le chargement complet
            
            # État initial
            initial_state = {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'page_source_length': len(self.driver.page_source),
                'downloads_before': self.list_downloads()
            }
            
            logging.info(f"📊 État initial:")
            logging.info(f"   URL: {initial_state['url']}")
            logging.info(f"   Titre: {initial_state['title']}")
            logging.info(f"   Taille page: {initial_state['page_source_length']} caractères")
            logging.info(f"   Fichiers téléchargés: {len(initial_state['downloads_before'])}")
            
            # Chercher le bouton "Extraire la liste"
            extract_button = None
            button_selectors = [
                "//button[contains(text(), 'Extraire la liste')]",
                "//button[contains(text(), 'Extraire')]",
                "//a[contains(text(), 'Extraire la liste')]",
                "//a[contains(text(), 'Extraire')]",
                "//*[contains(text(), 'Extraire la liste')]"
            ]
            
            for selector in button_selectors:
                try:
                    extract_button = self.driver.find_element(By.XPATH, selector)
                    if extract_button.is_displayed():
                        logging.info(f"✅ Bouton trouvé avec sélecteur: {selector}")
                        break
                except:
                    continue
            
            if not extract_button:
                logging.error("❌ Bouton 'Extraire la liste' non trouvé")
                return None
            
            # Analyser le bouton
            button_info = {
                'text': extract_button.text,
                'tag': extract_button.tag_name,
                'class': extract_button.get_attribute('class'),
                'id': extract_button.get_attribute('id'),
                'onclick': extract_button.get_attribute('onclick'),
                'href': extract_button.get_attribute('href'),
                'type': extract_button.get_attribute('type'),
                'visible': extract_button.is_displayed(),
                'enabled': extract_button.is_enabled()
            }
            
            logging.info(f"🔘 Informations du bouton:")
            for key, value in button_info.items():
                if value:
                    logging.info(f"   {key}: {value}")
            
            # Cliquer sur le bouton
            logging.info("🔘 Clic sur le bouton 'Extraire la liste'...")
            
            try:
                # Essayer le clic normal
                extract_button.click()
                logging.info("   ✅ Clic normal réussi")
            except Exception as e:
                logging.info(f"   ⚠️ Clic normal échoué: {e}")
                try:
                    # Essayer le clic JavaScript
                    self.driver.execute_script("arguments[0].click();", extract_button)
                    logging.info("   ✅ Clic JavaScript réussi")
                except Exception as e2:
                    logging.error(f"   ❌ Clic JavaScript échoué: {e2}")
                    return None
            
            # Attendre et observer les changements
            logging.info("⏳ Attente des changements...")
            time.sleep(10)
            
            # État après le clic
            after_state = {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'page_source_length': len(self.driver.page_source),
                'downloads_after': self.list_downloads()
            }
            
            # Analyser les changements
            changes = {
                'url_changed': initial_state['url'] != after_state['url'],
                'title_changed': initial_state['title'] != after_state['title'],
                'page_changed': initial_state['page_source_length'] != after_state['page_source_length'],
                'new_downloads': len(after_state['downloads_after']) > len(initial_state['downloads_before'])
            }
            
            logging.info(f"📊 Changements détectés:")
            logging.info(f"   URL changée: {changes['url_changed']}")
            logging.info(f"   Titre changé: {changes['title_changed']}")
            logging.info(f"   Page changée: {changes['page_changed']}")
            logging.info(f"   Nouveaux téléchargements: {changes['new_downloads']}")
            
            if after_state['url'] != initial_state['url']:
                logging.info(f"   Nouvelle URL: {after_state['url']}")
            
            if after_state['title'] != initial_state['title']:
                logging.info(f"   Nouveau titre: {after_state['title']}")
            
            # Vérifier les téléchargements
            new_files = [f for f in after_state['downloads_after'] 
                        if f not in initial_state['downloads_before']]
            
            if new_files:
                logging.info(f"🎯 NOUVEAUX FICHIERS TÉLÉCHARGÉS:")
                for file in new_files:
                    file_path = os.path.join(self.download_dir, file)
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    logging.info(f"   📄 {file} ({file_size} bytes)")
                    
                    # Analyser le contenu si c'est un fichier texte
                    if file.endswith(('.csv', '.txt', '.json')):
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                logging.info(f"      Contenu: {len(content)} caractères")
                                logging.info(f"      Aperçu: {content[:200]}...")
                        except Exception as e:
                            logging.debug(f"Erreur lecture {file}: {e}")
            
            # Analyser la nouvelle page si l'URL a changé
            if changes['url_changed'] or changes['page_changed']:
                logging.info("🔍 Analyse de la nouvelle page...")
                
                new_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # Chercher des associations
                new_associations = [link for link in new_soup.find_all('a', href=True) 
                                  if '/association/' in link['href']]
                
                # Chercher des données tabulaires
                tables = new_soup.find_all('table')
                
                # Chercher du contenu CSV/JSON
                pre_elements = new_soup.find_all('pre')
                
                logging.info(f"   🏢 Associations trouvées: {len(new_associations)}")
                logging.info(f"   📊 Tables trouvées: {len(tables)}")
                logging.info(f"   📄 Éléments <pre>: {len(pre_elements)}")
                
                if len(new_associations) > 20:
                    logging.info(f"   🎯 PLUS D'ASSOCIATIONS TROUVÉES! ({len(new_associations)})")
                
                if tables:
                    for i, table in enumerate(tables):
                        rows = table.find_all('tr')
                        logging.info(f"   📊 Table {i+1}: {len(rows)} lignes")
                
                if pre_elements:
                    for i, pre in enumerate(pre_elements):
                        content = pre.get_text()[:500]
                        logging.info(f"   📄 Pre {i+1}: {content}...")
            
            # Résultats du test
            test_results = {
                'button_found': True,
                'button_info': button_info,
                'initial_state': initial_state,
                'after_state': after_state,
                'changes': changes,
                'new_files': new_files,
                'success': any(changes.values()) or bool(new_files)
            }
            
            if test_results['success']:
                logging.info("🎉 BOUTON FONCTIONNEL! Des changements ont été détectés.")
            else:
                logging.info("⚠️ Bouton cliqué mais aucun changement visible détecté.")
            
            return test_results
            
        except Exception as e:
            logging.error(f"❌ Erreur test bouton: {e}")
            return {'error': str(e)}
    
    def list_downloads(self):
        """
        Liste les fichiers dans le dossier de téléchargement
        """
        try:
            if os.path.exists(self.download_dir):
                return os.listdir(self.download_dir)
            return []
        except Exception as e:
            logging.debug(f"Erreur listage téléchargements: {e}")
            return []
    
    def wait_for_download(self, timeout=30):
        """
        Attend qu'un téléchargement se termine
        """
        logging.info(f"⏳ Attente de téléchargement (max {timeout}s)...")
        
        start_time = time.time()
        initial_files = self.list_downloads()
        
        while time.time() - start_time < timeout:
            current_files = self.list_downloads()
            
            # Vérifier s'il y a de nouveaux fichiers
            new_files = [f for f in current_files if f not in initial_files]
            if new_files:
                # Vérifier que le téléchargement est terminé (pas de .crdownload)
                complete_files = [f for f in new_files if not f.endswith('.crdownload')]
                if complete_files:
                    logging.info(f"✅ Téléchargement terminé: {complete_files}")
                    return complete_files
            
            time.sleep(1)
        
        logging.info("⏰ Timeout - Aucun téléchargement détecté")
        return []

def main():
    """
    Test principal du bouton "Extraire la liste"
    """
    print("🔘 TEST DU BOUTON 'EXTRAIRE LA LISTE'")
    print("=" * 50)
    print("Ce test va:")
    print("- Chercher le bouton 'Extraire la liste'")
    print("- Cliquer dessus")
    print("- Analyser tous les changements")
    print("- Détecter les téléchargements")
    print()
    
    tester = ExtractButtonTester()
    
    try:
        tester.start_driver()
        
        # Test du bouton
        results = tester.test_extract_button_comprehensive()
        
        if results:
            print(f"\n📊 RÉSULTATS DU TEST:")
            
            if results.get('success'):
                print(f"   🎉 SUCCÈS! Le bouton est fonctionnel")
                
                if results.get('new_files'):
                    print(f"   📄 Fichiers téléchargés: {len(results['new_files'])}")
                    for file in results['new_files']:
                        print(f"      📄 {file}")
                
                changes = results.get('changes', {})
                if changes.get('url_changed'):
                    print(f"   🌐 Redirection vers: {results['after_state']['url']}")
                
                if changes.get('page_changed'):
                    print(f"   📄 Contenu de page modifié")
                    
            else:
                print(f"   ⚠️ Bouton trouvé mais pas de changement détecté")
            
            # Sauvegarder les résultats
            with open('extract_button_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 Résultats détaillés sauvegardés dans: extract_button_results.json")
            print(f"📄 Log détaillé dans: extract_button_test.log")
            
        else:
            print("\n❌ Échec du test")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
