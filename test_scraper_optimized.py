import time
import json
import csv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DataAssoTestScraper:
    def __init__(self, headless=False):
        """
        Scraper de test optimisé pour data-asso.fr
        """
        self.options = Options()
        if headless:
            self.options.add_argument('--headless')
        
        # Options optimisées
        self.options.add_argument('--no-sandbox')
        self.options.add_argument('--disable-dev-shm-usage')
        self.options.add_argument('--disable-gpu')
        self.options.add_argument('--window-size=1920,1080')
        self.options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        self.options.add_argument('--disable-blink-features=AutomationControlled')
        self.options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = None
        self.wait = None
        
    def start_driver(self):
        """Démarre le driver Chrome avec webdriver-manager"""
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            logging.info("✅ Driver Chrome démarré avec succès")
        except Exception as e:
            logging.error(f"❌ Erreur lors du démarrage du driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
            
    def get_association_links(self, max_associations=3):
        """
        Récupère les liens des associations depuis l'annuaire
        """
        url = "https://www.data-asso.fr/annuaire"
        logging.info(f"🌐 Accès à {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            # Attendre que la page se charge
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Extraire les liens avec BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            association_links = []
            for link in soup.find_all('a', href=True):
                if '/association/' in link['href']:
                    full_url = 'https://www.data-asso.fr' + link['href'] if link['href'].startswith('/') else link['href']
                    association_links.append(full_url)
            
            # Supprimer les doublons et limiter
            association_links = list(set(association_links))[:max_associations]
            
            logging.info(f"📋 {len(association_links)} associations trouvées")
            return association_links
            
        except Exception as e:
            logging.error(f"❌ Erreur lors de la récupération des liens: {e}")
            return []
    
    def extract_association_data(self, association_url):
        """
        Extrait les données d'une association depuis sa page
        """
        logging.info(f"🔍 Extraction: {association_url}")
        
        try:
            self.driver.get(association_url)
            time.sleep(3)
            
            # Attendre que la page se charge
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Structure de données
            data = {
                'url': association_url,
                'nom': '',
                'rna': '',
                'siren': '',
                'adresse': '',
                'adresse_gestion': '',
                'telephone': '',
                'email': '',
                'site_web': '',
                'ville': '',
                'code_postal': '',
                'statut': 'Données partielles'
            }
            
            # Extraire le nom (plusieurs méthodes)
            # Méthode 1: titre h1
            title = soup.find('h1')
            if title and title.get_text(strip=True):
                data['nom'] = title.get_text(strip=True)
                logging.info(f"   📝 Nom (H1): {data['nom']}")
            else:
                # Méthode 2: titre dans meta ou title
                title_tag = soup.find('title')
                if title_tag and title_tag.get_text(strip=True) != 'Data-Asso':
                    data['nom'] = title_tag.get_text(strip=True)
                    logging.info(f"   📝 Nom (Title): {data['nom']}")
                else:
                    # Méthode 3: chercher dans le contenu
                    for elem in soup.find_all(['h2', 'h3', 'div'], class_=re.compile(r'title|name|nom', re.I)):
                        if elem.get_text(strip=True):
                            data['nom'] = elem.get_text(strip=True)
                            logging.info(f"   📝 Nom (Class): {data['nom']}")
                            break
            
            # Extraire RNA et SIREN depuis l'URL ou le contenu
            rna_match = re.search(r'/association/([A-Z0-9]+)', association_url)
            if rna_match:
                data['rna'] = rna_match.group(1)
                logging.info(f"   🏢 RNA: {data['rna']}")
            
            # Chercher SIREN dans le contenu
            text_content = soup.get_text()
            siren_match = re.search(r'SIREN[:\s]*([0-9]{9})', text_content, re.IGNORECASE)
            if siren_match:
                data['siren'] = siren_match.group(1)
                logging.info(f"   🏢 SIREN: {data['siren']}")
            
            # Extraire les champs de coordonnées
            fields_mapping = {
                'Adresse': 'adresse',
                'Adresse de gestion': 'adresse_gestion',
                'Téléphone': 'telephone',
                'Courriel': 'email',
                'Site internet': 'site_web'
            }
            
            for field_name, data_key in fields_mapping.items():
                value = self.extract_field_value(soup, field_name)
                if value:
                    data[data_key] = value
                    logging.info(f"   📍 {field_name}: {value}")
            
            # Extraire ville et code postal depuis l'adresse
            if data['adresse']:
                # Pattern amélioré pour les codes postaux et villes
                postal_patterns = [
                    r'(\d{5})\s+([A-Z\s\-À-ÿ]+?)(?:FRANCE|$)',
                    r'(\d{5})\s+([A-Z\s\-À-ÿ]+)',
                    r'(\d{5})([A-Z\s\-À-ÿ]+)'
                ]

                for pattern in postal_patterns:
                    postal_match = re.search(pattern, data['adresse'], re.IGNORECASE)
                    if postal_match:
                        data['code_postal'] = postal_match.group(1)
                        ville_raw = postal_match.group(2).strip()
                        # Nettoyer la ville
                        ville_clean = re.sub(r'(FRANCE|france)$', '', ville_raw).strip()
                        data['ville'] = ville_clean
                        break
            
            # Déterminer le statut
            has_contact = any([
                data['telephone'] and data['telephone'] != '-',
                data['email'] and data['email'] != '-',
                data['site_web']
            ])

            has_address = data['adresse'] and data['adresse'] != '-'
            has_name = data['nom'] and data['nom'] != ''
            has_rna = data['rna'] and data['rna'] != ''

            if has_name and has_address and has_contact:
                data['statut'] = 'Données complètes'
            elif has_rna and (has_address or has_contact):
                data['statut'] = 'Données partielles'
            elif has_rna:
                data['statut'] = 'RNA seulement'
            else:
                data['statut'] = 'Échec extraction'
            
            return data
            
        except Exception as e:
            logging.error(f"   ❌ Erreur extraction {association_url}: {e}")
            return None
    
    def extract_field_value(self, soup, field_name):
        """
        Extrait la valeur d'un champ spécifique
        """
        try:
            # Méthode 1: Chercher le texte du champ
            field_element = soup.find(string=re.compile(field_name, re.IGNORECASE))
            
            if field_element:
                # Chercher dans les éléments suivants
                parent = field_element.parent
                
                # Chercher dans les siblings
                for sibling in parent.find_next_siblings():
                    if sibling.name and sibling.get_text(strip=True):
                        return sibling.get_text(strip=True)
                
                # Chercher dans l'élément suivant
                next_elem = parent.find_next()
                if next_elem and next_elem.get_text(strip=True):
                    return next_elem.get_text(strip=True)
            
            # Méthode 2: Chercher dans les divs/spans avec classes
            for element in soup.find_all(['div', 'span', 'p']):
                if element.get_text() and field_name.lower() in element.get_text().lower():
                    # Chercher la valeur dans le même élément ou le suivant
                    text = element.get_text(strip=True)
                    if ':' in text:
                        parts = text.split(':', 1)
                        if len(parts) > 1:
                            return parts[1].strip()
            
        except Exception as e:
            logging.warning(f"   ⚠️ Erreur extraction champ {field_name}: {e}")
        
        return None
    
    def test_scraping(self, max_associations=3):
        """
        Test du scraping sur un nombre limité d'associations
        """
        logging.info("🚀 DÉBUT DU TEST DE SCRAPING")
        logging.info(f"📊 Limite: {max_associations} associations")
        
        if not self.driver:
            self.start_driver()
        
        try:
            # 1. Récupérer les liens
            association_links = self.get_association_links(max_associations)
            
            if not association_links:
                logging.error("❌ Aucun lien d'association trouvé")
                return []
            
            # 2. Extraire les données
            results = []
            for i, link in enumerate(association_links):
                logging.info(f"\n📋 Association {i+1}/{len(association_links)}")
                
                data = self.extract_association_data(link)
                if data:
                    results.append(data)
                    logging.info(f"   ✅ Succès: {data['statut']}")
                else:
                    logging.warning(f"   ⚠️ Échec extraction")
                
                # Pause respectueuse
                time.sleep(2)
            
            logging.info(f"\n🎉 TEST TERMINÉ: {len(results)}/{len(association_links)} associations extraites")
            return results
            
        except Exception as e:
            logging.error(f"❌ Erreur générale: {e}")
            return []
    
    def save_results(self, data, filename_base='test_associations'):
        """
        Sauvegarde les résultats en JSON et CSV
        """
        if not data:
            logging.warning("⚠️ Aucune donnée à sauvegarder")
            return
        
        # Sauvegarde JSON
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logging.info(f"💾 Sauvegardé: {json_file}")
        
        # Sauvegarde CSV
        csv_file = f"{filename_base}.csv"
        fieldnames = ['nom', 'rna', 'siren', 'adresse', 'adresse_gestion', 
                     'telephone', 'email', 'site_web', 'ville', 'code_postal', 
                     'statut', 'url']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for item in data:
                writer.writerow(item)
        logging.info(f"💾 Sauvegardé: {csv_file}")

def main():
    """
    Test principal
    """
    print("🧪 TEST DU SCRAPER DATA-ASSO")
    print("=" * 50)
    
    scraper = DataAssoTestScraper(headless=False)  # Mode visible pour debug
    
    try:
        # Test sur 3 associations
        results = scraper.test_scraping(max_associations=3)
        
        if results:
            print(f"\n📊 RÉSULTATS DU TEST:")
            print(f"   ✅ {len(results)} associations extraites")
            
            # Afficher les résultats
            for i, assoc in enumerate(results):
                print(f"\n{i+1}. {assoc.get('nom', 'N/A')}")
                print(f"   🏢 RNA: {assoc.get('rna', 'N/A')}")
                print(f"   📍 Adresse: {assoc.get('adresse', 'N/A')}")
                print(f"   📞 Téléphone: {assoc.get('telephone', 'N/A')}")
                print(f"   📧 Email: {assoc.get('email', 'N/A')}")
                print(f"   📊 Statut: {assoc.get('statut', 'N/A')}")
            
            # Sauvegarder
            scraper.save_results(results)
            print(f"\n💾 Résultats sauvegardés!")
            
        else:
            print("\n❌ Aucun résultat obtenu")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()
