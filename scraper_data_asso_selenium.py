#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRAPER DATA-ASSO.FR AVEC SELENIUM
Version simplifiée et efficace qui contourne TOUS les blocages
Utilise Selenium pour simuler un utilisateur réel
"""

import time
import json
import csv
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
import random
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_selenium.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ScraperDataAssoSelenium:
    def __init__(self):
        """Scraper Selenium pour data-asso.fr"""
        self.driver = None
        self.wait = None
        self.associations = {}
        
        # Statistiques
        self.stats = {
            'total_associations': 0,
            'pages_visitees': 0,
            'erreurs': 0,
            'csv_exportes': 0
        }
        
        # Stratégies de recherche
        self.strategies = [
            # Recherches par lettres
            *[chr(i) for i in range(ord('a'), ord('z') + 1)],
            # Recherches par chiffres
            *[str(i) for i in range(10)],
            # Recherches par mots courants
            'association', 'club', 'amicale', 'union', 'federation', 'comite',
            'sport', 'culture', 'social', 'education', 'environnement',
            # Recherches par villes
            'paris', 'lyon', 'marseille', 'toulouse', 'nice', 'nantes',
            'strasbourg', 'montpellier', 'bordeaux', 'lille', 'rennes',
            # Recherches par codes postaux
            '75', '69', '13', '31', '06', '44', '67', '34', '33', '59'
        ]
    
    def start_driver(self):
        """Démarre le driver Selenium avec configuration optimisée"""
        options = Options()
        
        # Configuration pour éviter la détection
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # User agent réaliste
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Dossier de téléchargement
        download_dir = os.path.abspath("downloads_data_asso")
        os.makedirs(download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        self.wait = WebDriverWait(self.driver, 30)
        
        # Masquer l'automation
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logging.info("✅ Driver Selenium démarré avec configuration anti-détection")
    
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def aller_sur_annuaire(self):
        """Va sur la page annuaire"""
        try:
            self.driver.get("https://www.data-asso.fr/annuaire")
            time.sleep(random.uniform(3, 6))
            
            # Accepter les cookies si nécessaire
            try:
                cookie_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'compris') or contains(text(), 'accepter')]")
                cookie_button.click()
                time.sleep(2)
            except:
                pass
            
            logging.info("✅ Page annuaire chargée")
            return True
            
        except Exception as e:
            logging.error(f"❌ Erreur chargement annuaire: {e}")
            return False
    
    def extraire_associations_page_actuelle(self):
        """Extrait toutes les associations de la page actuelle"""
        try:
            # Attendre que les associations se chargent
            time.sleep(3)
            
            # Extraire le HTML
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            associations_trouvees = 0
            
            # Chercher tous les liens vers des associations
            for link in soup.find_all('a', href=True):
                href = link['href']
                
                if '/annuaire/association/' in href:
                    # Extraire le RNA
                    rna_match = re.search(r'/annuaire/association/([A-Z0-9]+)', href)
                    if rna_match:
                        rna = rna_match.group(1)
                        
                        if rna not in self.associations:
                            # Extraire les informations visibles
                            nom = link.get_text(strip=True)
                            
                            # Chercher les informations dans le parent
                            parent = link.parent
                            if parent:
                                parent_text = parent.get_text()
                                
                                # Extraire SIREN
                                siren_match = re.search(r'N° SIREN (\d+)', parent_text)
                                siren = siren_match.group(1) if siren_match else ''
                                
                                # Extraire adresse
                                adresse_match = re.search(r'(\d+.*?\d{5}\s+[A-Za-zÀ-ÿ\-\s]+)', parent_text)
                                adresse = adresse_match.group(1) if adresse_match else ''
                                
                                # Extraire thème
                                themes = ['Sport', 'Culture', 'Santé et action sociale', 'Loisirs et vie sociale', 
                                         'Economie et développement local', 'Education et formation',
                                         'Environnement et patrimoine', 'Autres et divers']
                                theme = ''
                                for t in themes:
                                    if t in parent_text:
                                        theme = t
                                        break
                            
                            association = {
                                'rna': rna,
                                'nom': nom,
                                'siren': siren,
                                'adresse': adresse,
                                'theme': theme,
                                'url': f"https://www.data-asso.fr{href}" if href.startswith('/') else href,
                                'extraction_date': datetime.now().isoformat()
                            }
                            
                            self.associations[rna] = association
                            associations_trouvees += 1
                            self.stats['total_associations'] += 1
            
            logging.info(f"   📊 {associations_trouvees} associations extraites de cette page")
            return associations_trouvees
            
        except Exception as e:
            logging.error(f"   ❌ Erreur extraction page: {e}")
            return 0
    
    def exporter_csv_page_actuelle(self):
        """Exporte le CSV de la page actuelle"""
        try:
            # Chercher le bouton d'export
            export_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Extraire la liste')]"))
            )
            
            # Cliquer sur le bouton
            self.driver.execute_script("arguments[0].click();", export_button)
            time.sleep(3)
            
            self.stats['csv_exportes'] += 1
            logging.info("   📄 CSV exporté")
            return True
            
        except Exception as e:
            logging.debug(f"   ❌ Erreur export CSV: {e}")
            return False
    
    def rechercher_avec_terme(self, terme):
        """Effectue une recherche avec un terme donné"""
        try:
            # Chercher le champ de recherche
            search_input = self.driver.find_element(By.XPATH, "//input[@placeholder='Rechercher une association' or contains(@placeholder, 'association')]")
            
            # Vider et saisir le terme
            search_input.clear()
            time.sleep(0.5)
            
            # Saisir caractère par caractère pour simuler un humain
            for char in terme:
                search_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            time.sleep(1)
            
            # Appuyer sur Entrée
            search_input.send_keys(Keys.RETURN)
            
            # Attendre le chargement
            time.sleep(random.uniform(3, 6))
            
            logging.info(f"   🔍 Recherche effectuée: '{terme}'")
            return True
            
        except Exception as e:
            logging.debug(f"   ❌ Erreur recherche '{terme}': {e}")
            return False
    
    def scraper_avec_strategies_multiples(self):
        """Scrape en utilisant toutes les stratégies de recherche"""
        logging.info("🎯 SCRAPING AVEC STRATÉGIES MULTIPLES")
        
        for i, strategie in enumerate(self.strategies):
            try:
                logging.info(f"📍 Stratégie {i+1}/{len(self.strategies)}: '{strategie}'")
                
                # Aller sur l'annuaire
                if not self.aller_sur_annuaire():
                    continue
                
                # Effectuer la recherche
                if self.rechercher_avec_terme(strategie):
                    # Extraire les associations
                    nb_extraites = self.extraire_associations_page_actuelle()
                    
                    # Exporter le CSV
                    self.exporter_csv_page_actuelle()
                    
                    self.stats['pages_visitees'] += 1
                    
                    # Afficher le progrès
                    if i % 10 == 0:
                        logging.info(f"   📊 Progrès: {i}/{len(self.strategies)} stratégies")
                        logging.info(f"      📋 {self.stats['total_associations']} associations uniques")
                        logging.info(f"      📄 {self.stats['csv_exportes']} CSV exportés")
                
                # Pause entre les recherches
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logging.error(f"   ❌ Erreur stratégie '{strategie}': {e}")
                self.stats['erreurs'] += 1
                continue
    
    def scraper_par_filtres_interface(self):
        """Utilise les filtres de l'interface pour récupérer plus d'associations"""
        logging.info("🎛️ SCRAPING PAR FILTRES INTERFACE")
        
        try:
            if not self.aller_sur_annuaire():
                return
            
            # Tester différents filtres
            filtres_themes = [
                'Culture', 'Sport', 'Loisirs et vie sociale', 'Santé et action sociale',
                'Economie et développement local', 'Education et formation',
                'Environnement et patrimoine', 'Autres et divers'
            ]
            
            for theme in filtres_themes:
                try:
                    logging.info(f"   🎯 Filtre thème: {theme}")
                    
                    # Aller sur l'annuaire
                    self.aller_sur_annuaire()
                    
                    # Cliquer sur le filtre du thème
                    theme_checkbox = self.driver.find_element(By.XPATH, f"//label[contains(text(), '{theme}')]/preceding-sibling::input[@type='checkbox']")
                    
                    # Décocher tous les autres thèmes d'abord
                    all_checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox' and contains(@id, 'theme')]")
                    for checkbox in all_checkboxes:
                        if checkbox.is_selected():
                            checkbox.click()
                    
                    # Cocher seulement le thème voulu
                    if not theme_checkbox.is_selected():
                        theme_checkbox.click()
                    
                    time.sleep(3)
                    
                    # Extraire les associations
                    nb_extraites = self.extraire_associations_page_actuelle()
                    
                    # Exporter le CSV
                    self.exporter_csv_page_actuelle()
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur filtre {theme}: {e}")
                    continue
                    
        except Exception as e:
            logging.error(f"❌ Erreur filtres interface: {e}")
    
    def sauvegarder_resultats(self):
        """Sauvegarde tous les résultats"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # JSON complet
        resultats = {
            'metadata': {
                'total_associations': len(self.associations),
                'extraction_date': datetime.now().isoformat(),
                'methode': 'selenium_strategies_multiples',
                'statistiques': self.stats
            },
            'associations': list(self.associations.values())
        }
        
        json_file = f'data_asso_selenium_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(resultats, f, ensure_ascii=False, indent=2)
        
        # CSV
        csv_file = f'data_asso_selenium_{timestamp}.csv'
        if self.associations:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'siren', 'adresse', 'theme', 'url', 'extraction_date']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for assoc in self.associations.values():
                    writer.writerow(assoc)
        
        logging.info(f"💾 RÉSULTATS SAUVEGARDÉS:")
        logging.info(f"   📄 {json_file}")
        logging.info(f"   📊 {csv_file}")
        logging.info(f"   📋 {len(self.associations)} associations au total")
        
        return json_file, csv_file
    
    def run_scraping_selenium(self):
        """Lance le scraping complet avec Selenium"""
        logging.info("🚀 SCRAPING DATA-ASSO.FR AVEC SELENIUM")
        logging.info("=" * 80)
        logging.info("Contourne TOUS les blocages en simulant un utilisateur réel")
        logging.info("=" * 80)
        
        start_time = time.time()
        
        try:
            # Démarrer le driver
            self.start_driver()
            
            # MÉTHODE 1: Stratégies multiples de recherche
            self.scraper_avec_strategies_multiples()
            
            # MÉTHODE 2: Filtres interface
            # self.scraper_par_filtres_interface()
            
            # Sauvegarder les résultats
            json_file, csv_file = self.sauvegarder_resultats()
            
            # Résumé final
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n🎉 SCRAPING SELENIUM TERMINÉ!")
            print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
            print(f"   📋 Total associations: {len(self.associations)}")
            print(f"   📄 Pages visitées: {self.stats['pages_visitees']}")
            print(f"   📊 CSV exportés: {self.stats['csv_exportes']}")
            print(f"   ❌ Erreurs: {self.stats['erreurs']}")
            print(f"   💾 Fichiers créés: {json_file}, {csv_file}")
            
            if self.associations:
                print(f"\n📊 EXEMPLES D'ASSOCIATIONS RÉCUPÉRÉES:")
                for i, (rna, assoc) in enumerate(list(self.associations.items())[:10]):
                    print(f"   {i+1}. {assoc['nom'][:50]}... (RNA: {rna})")
                    if assoc['adresse']:
                        print(f"      📍 {assoc['adresse'][:50]}...")
                    if assoc['theme']:
                        print(f"      🏷️ {assoc['theme']}")
            
            print(f"\n🎯 MISSION ACCOMPLIE! Associations récupérées avec succès!")
            
        except Exception as e:
            logging.error(f"❌ Erreur scraping selenium: {e}")
            
        finally:
            self.close_driver()

def main():
    """Fonction principale"""
    print("🚀 SCRAPER DATA-ASSO.FR AVEC SELENIUM")
    print("=" * 80)
    print("Contourne TOUS les blocages en simulant un utilisateur réel")
    print("Utilise des stratégies multiples de recherche et d'export")
    print("=" * 80)
    print()
    print("🎯 Méthodes utilisées:")
    print("   1. Recherches par lettres, chiffres, mots-clés")
    print("   2. Recherches par villes et codes postaux")
    print("   3. Export CSV automatique pour chaque recherche")
    print("   4. Simulation comportement humain (pauses, saisie progressive)")
    print("   5. Configuration anti-détection avancée")
    print()
    
    response = input("Voulez-vous lancer le scraping Selenium? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = ScraperDataAssoSelenium()
    scraper.run_scraping_selenium()

if __name__ == "__main__":
    main()
