#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR DE RÉSULTATS DATA-ASSO.FR
Analyse et consolide tous les fichiers générés par les scrapers
"""

import json
import csv
import os
import glob
from collections import Counter, defaultdict
import pandas as pd
import re
from datetime import datetime
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class AnalyseurResultatsDataAsso:
    def __init__(self):
        """Analyseur de résultats pour data-asso.fr"""
        self.associations = {}
        self.statistiques = {
            'total_associations': 0,
            'associations_uniques': 0,
            'doublons_supprimes': 0,
            'avec_siren': 0,
            'avec_adresse': 0,
            'par_theme': Counter(),
            'par_departement': Counter(),
            'fichiers_traites': 0
        }
    
    def charger_tous_les_fichiers(self):
        """Charge tous les fichiers JSON et CSV générés"""
        logging.info("📂 CHARGEMENT DE TOUS LES FICHIERS")
        
        # Fichiers JSON
        json_files = glob.glob("data_asso_*.json")
        for json_file in json_files:
            self.charger_fichier_json(json_file)
        
        # Fichiers CSV
        csv_files = glob.glob("data_asso_*.csv")
        for csv_file in csv_files:
            self.charger_fichier_csv(csv_file)
        
        # Fichiers CSV téléchargés
        download_dir = "downloads_data_asso"
        if os.path.exists(download_dir):
            csv_downloads = glob.glob(os.path.join(download_dir, "*.csv"))
            for csv_file in csv_downloads:
                self.charger_fichier_csv_telecharge(csv_file)
        
        logging.info(f"✅ {self.statistiques['fichiers_traites']} fichiers traités")
    
    def charger_fichier_json(self, fichier):
        """Charge un fichier JSON"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            associations = data.get('associations', [])
            for assoc in associations:
                rna = assoc.get('rna', '')
                if rna and rna not in self.associations:
                    self.associations[rna] = assoc
                    self.statistiques['total_associations'] += 1
                elif rna in self.associations:
                    # Fusionner les données si plus complètes
                    self.fusionner_association(rna, assoc)
                    self.statistiques['doublons_supprimes'] += 1
            
            self.statistiques['fichiers_traites'] += 1
            logging.info(f"   📄 {fichier}: {len(associations)} associations")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur {fichier}: {e}")
    
    def charger_fichier_csv(self, fichier):
        """Charge un fichier CSV"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                associations_count = 0
                
                for row in reader:
                    rna = row.get('rna', '')
                    if rna and rna not in self.associations:
                        self.associations[rna] = dict(row)
                        self.statistiques['total_associations'] += 1
                        associations_count += 1
                    elif rna in self.associations:
                        self.fusionner_association(rna, dict(row))
                        self.statistiques['doublons_supprimes'] += 1
            
            self.statistiques['fichiers_traites'] += 1
            logging.info(f"   📊 {fichier}: {associations_count} associations")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur {fichier}: {e}")
    
    def charger_fichier_csv_telecharge(self, fichier):
        """Charge un fichier CSV téléchargé (format data-asso.fr)"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                # Le format des CSV téléchargés est différent
                content = f.read()
                
                # Parser le format spécial de data-asso.fr
                lines = content.strip().split('\n')
                if len(lines) < 2:
                    return
                
                headers = lines[0].split(';')
                associations_count = 0
                
                for line in lines[1:]:
                    values = line.split(';')
                    if len(values) >= len(headers):
                        row = dict(zip(headers, values))
                        
                        rna = row.get('id_rna', '')
                        if rna and rna not in self.associations:
                            # Convertir au format standard
                            assoc = {
                                'rna': rna,
                                'nom': row.get('nom', ''),
                                'siren': row.get('id_siren', ''),
                                'siret': row.get('id_siret_siege', ''),
                                'adresse': row.get('adresse', ''),
                                'theme': row.get('lib_theme1', ''),
                                'active': row.get('active', 'true').lower() == 'true',
                                'latitude': row.get('latitude', ''),
                                'longitude': row.get('longitude', ''),
                                'id_interne': row.get('_id', ''),
                                'source': 'csv_telecharge'
                            }
                            
                            self.associations[rna] = assoc
                            self.statistiques['total_associations'] += 1
                            associations_count += 1
                        elif rna in self.associations:
                            self.statistiques['doublons_supprimes'] += 1
            
            self.statistiques['fichiers_traites'] += 1
            logging.info(f"   📥 {fichier}: {associations_count} associations")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur {fichier}: {e}")
    
    def fusionner_association(self, rna, nouvelle_assoc):
        """Fusionne les données d'une association avec les données existantes"""
        assoc_existante = self.associations[rna]
        
        # Fusionner en gardant les données les plus complètes
        for key, value in nouvelle_assoc.items():
            if value and (not assoc_existante.get(key) or len(str(value)) > len(str(assoc_existante.get(key, '')))):
                assoc_existante[key] = value
    
    def calculer_statistiques(self):
        """Calcule les statistiques détaillées"""
        logging.info("📊 CALCUL DES STATISTIQUES")
        
        self.statistiques['associations_uniques'] = len(self.associations)
        
        for rna, assoc in self.associations.items():
            # SIREN
            if assoc.get('siren'):
                self.statistiques['avec_siren'] += 1
            
            # Adresse
            if assoc.get('adresse'):
                self.statistiques['avec_adresse'] += 1
            
            # Thème
            theme = assoc.get('theme', 'Non renseigné')
            self.statistiques['par_theme'][theme] += 1
            
            # Département (extrait du code postal ou RNA)
            departement = self.extraire_departement(assoc)
            if departement:
                self.statistiques['par_departement'][departement] += 1
    
    def extraire_departement(self, assoc):
        """Extrait le département d'une association"""
        # Essayer d'extraire du code postal dans l'adresse
        adresse = assoc.get('adresse', '')
        cp_match = re.search(r'\b(\d{5})\b', adresse)
        if cp_match:
            cp = cp_match.group(1)
            if cp.startswith('97') or cp.startswith('98'):  # DOM-TOM
                return cp[:3]
            else:
                return cp[:2]
        
        # Essayer d'extraire du RNA
        rna = assoc.get('rna', '')
        if len(rna) >= 4:
            dept_code = rna[1:3]
            if dept_code.isdigit():
                return dept_code
        
        return None
    
    def generer_rapport_complet(self):
        """Génère un rapport complet des résultats"""
        logging.info("📋 GÉNÉRATION DU RAPPORT COMPLET")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        rapport_file = f'rapport_data_asso_{timestamp}.txt'
        
        with open(rapport_file, 'w', encoding='utf-8') as f:
            f.write("🎯 RAPPORT COMPLET - SCRAPING DATA-ASSO.FR\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"📊 STATISTIQUES GÉNÉRALES:\n")
            f.write(f"   Total associations trouvées: {self.statistiques['total_associations']}\n")
            f.write(f"   Associations uniques: {self.statistiques['associations_uniques']}\n")
            f.write(f"   Doublons supprimés: {self.statistiques['doublons_supprimes']}\n")
            f.write(f"   Avec SIREN: {self.statistiques['avec_siren']} ({self.statistiques['avec_siren']/self.statistiques['associations_uniques']*100:.1f}%)\n")
            f.write(f"   Avec adresse: {self.statistiques['avec_adresse']} ({self.statistiques['avec_adresse']/self.statistiques['associations_uniques']*100:.1f}%)\n")
            f.write(f"   Fichiers traités: {self.statistiques['fichiers_traites']}\n\n")
            
            f.write(f"🏷️ RÉPARTITION PAR THÈME:\n")
            for theme, count in self.statistiques['par_theme'].most_common():
                pourcentage = count / self.statistiques['associations_uniques'] * 100
                f.write(f"   {theme}: {count} ({pourcentage:.1f}%)\n")
            f.write("\n")
            
            f.write(f"📍 TOP 20 DÉPARTEMENTS:\n")
            for dept, count in self.statistiques['par_departement'].most_common(20):
                pourcentage = count / self.statistiques['associations_uniques'] * 100
                f.write(f"   Département {dept}: {count} ({pourcentage:.1f}%)\n")
            f.write("\n")
            
            f.write(f"📋 EXEMPLES D'ASSOCIATIONS:\n")
            for i, (rna, assoc) in enumerate(list(self.associations.items())[:20]):
                f.write(f"   {i+1}. {assoc.get('nom', 'N/A')[:60]}...\n")
                f.write(f"      RNA: {rna}\n")
                if assoc.get('siren'):
                    f.write(f"      SIREN: {assoc['siren']}\n")
                if assoc.get('adresse'):
                    f.write(f"      Adresse: {assoc['adresse'][:60]}...\n")
                if assoc.get('theme'):
                    f.write(f"      Thème: {assoc['theme']}\n")
                f.write("\n")
        
        logging.info(f"📄 Rapport généré: {rapport_file}")
        return rapport_file
    
    def exporter_excel_complet(self):
        """Exporte toutes les données vers Excel"""
        logging.info("📊 EXPORT EXCEL COMPLET")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = f'data_asso_complet_{timestamp}.xlsx'
        
        try:
            # Convertir en DataFrame
            df = pd.DataFrame(list(self.associations.values()))
            
            # Créer le fichier Excel avec plusieurs onglets
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # Onglet principal
                df.to_excel(writer, sheet_name='Associations', index=False)
                
                # Onglet statistiques par thème
                stats_theme = pd.DataFrame(list(self.statistiques['par_theme'].items()), 
                                         columns=['Thème', 'Nombre'])
                stats_theme = stats_theme.sort_values('Nombre', ascending=False)
                stats_theme.to_excel(writer, sheet_name='Stats_Themes', index=False)
                
                # Onglet statistiques par département
                stats_dept = pd.DataFrame(list(self.statistiques['par_departement'].items()), 
                                        columns=['Département', 'Nombre'])
                stats_dept = stats_dept.sort_values('Nombre', ascending=False)
                stats_dept.to_excel(writer, sheet_name='Stats_Departements', index=False)
            
            logging.info(f"📊 Excel généré: {excel_file}")
            return excel_file
            
        except Exception as e:
            logging.error(f"❌ Erreur export Excel: {e}")
            return None
    
    def run_analyse_complete(self):
        """Lance l'analyse complète"""
        logging.info("🚀 ANALYSE COMPLÈTE DES RÉSULTATS DATA-ASSO.FR")
        logging.info("=" * 80)
        
        try:
            # Charger tous les fichiers
            self.charger_tous_les_fichiers()
            
            # Calculer les statistiques
            self.calculer_statistiques()
            
            # Générer le rapport
            rapport_file = self.generer_rapport_complet()
            
            # Exporter vers Excel
            excel_file = self.exporter_excel_complet()
            
            # Résumé final
            print(f"\n🎉 ANALYSE COMPLÈTE TERMINÉE!")
            print(f"   📋 {self.statistiques['associations_uniques']} associations uniques")
            print(f"   📊 {self.statistiques['avec_siren']} avec SIREN ({self.statistiques['avec_siren']/self.statistiques['associations_uniques']*100:.1f}%)")
            print(f"   📍 {self.statistiques['avec_adresse']} avec adresse ({self.statistiques['avec_adresse']/self.statistiques['associations_uniques']*100:.1f}%)")
            print(f"   📄 {rapport_file}")
            if excel_file:
                print(f"   📊 {excel_file}")
            
            print(f"\n🏷️ TOP 5 THÈMES:")
            for theme, count in self.statistiques['par_theme'].most_common(5):
                print(f"   {theme}: {count}")
            
            print(f"\n📍 TOP 5 DÉPARTEMENTS:")
            for dept, count in self.statistiques['par_departement'].most_common(5):
                print(f"   Département {dept}: {count}")
            
        except Exception as e:
            logging.error(f"❌ Erreur analyse complète: {e}")

def main():
    """Fonction principale"""
    print("📊 ANALYSEUR DE RÉSULTATS DATA-ASSO.FR")
    print("=" * 80)
    print("Analyse et consolide tous les fichiers générés par les scrapers")
    print("=" * 80)
    print()
    
    analyseur = AnalyseurResultatsDataAsso()
    analyseur.run_analyse_complete()

if __name__ == "__main__":
    main()
