import requests
import json
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('other_methods_discovery.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OtherMethodsDiscoverer:
    def __init__(self):
        """
        Découvreur d'autres méthodes pour obtenir TOUTES les associations
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.driver = None
        self.discovered_methods = []
        
    def start_driver(self):
        """Démarre le driver"""
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            logging.info("✅ Driver démarré")
        except Exception as e:
            logging.error(f"❌ Erreur driver: {e}")
            raise
            
    def close_driver(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Driver fermé")
    
    def method_official_government_sources(self):
        """
        MÉTHODE: Sources gouvernementales officielles
        """
        logging.info("🏛️ MÉTHODE: Sources gouvernementales officielles")
        
        official_sources = [
            {
                'name': 'data.gouv.fr - RNA',
                'url': 'https://www.data.gouv.fr/fr/datasets/repertoire-national-des-associations/',
                'description': 'Répertoire National des Associations officiel'
            },
            {
                'name': 'Journal Officiel',
                'url': 'https://www.journal-officiel.gouv.fr/pages/associations-search/',
                'description': 'Déclarations officielles d\'associations'
            },
            {
                'name': 'API Sirene INSEE',
                'url': 'https://api.insee.fr/catalogue/site/themes/wso2/subthemes/insee/pages/item-info.jag?name=Sirene&version=V3&provider=insee',
                'description': 'API officielle des entreprises et associations'
            },
            {
                'name': 'Légifrance',
                'url': 'https://www.legifrance.gouv.fr/',
                'description': 'Textes légaux et déclarations'
            },
            {
                'name': 'Service-Public.fr',
                'url': 'https://www.service-public.fr/associations/',
                'description': 'Informations officielles sur les associations'
            }
        ]
        
        results = []
        
        for source in official_sources:
            try:
                logging.info(f"   🔍 Test: {source['name']}")
                
                response = self.session.get(source['url'], timeout=10)
                
                result = {
                    'name': source['name'],
                    'url': source['url'],
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'content_length': len(response.content),
                    'description': source['description']
                }
                
                if response.status_code == 200:
                    # Chercher des indices de données d'associations
                    content = response.text.lower()
                    indicators = ['télécharger', 'download', 'csv', 'json', 'api', 'dataset', 'données']
                    found_indicators = [ind for ind in indicators if ind in content]
                    result['data_indicators'] = found_indicators
                    
                    if found_indicators:
                        logging.info(f"      🎯 Indicateurs de données trouvés: {found_indicators}")
                
                results.append(result)
                
            except Exception as e:
                logging.error(f"      ❌ Erreur {source['name']}: {e}")
                results.append({
                    'name': source['name'],
                    'url': source['url'],
                    'error': str(e),
                    'accessible': False
                })
        
        return {
            'method': 'official_government_sources',
            'sources_tested': len(official_sources),
            'accessible_sources': len([r for r in results if r.get('accessible')]),
            'results': results
        }
    
    def method_alternative_association_sites(self):
        """
        MÉTHODE: Sites alternatifs d'associations
        """
        logging.info("🌐 MÉTHODE: Sites alternatifs d'associations")
        
        alternative_sites = [
            {
                'name': 'HelloAsso',
                'url': 'https://www.helloasso.com/',
                'description': 'Plateforme de financement associatif'
            },
            {
                'name': 'Associations.gouv.fr',
                'url': 'https://www.associations.gouv.fr/',
                'description': 'Portail officiel des associations'
            },
            {
                'name': 'Le Mouvement Associatif',
                'url': 'https://lemouvementassociatif.org/',
                'description': 'Réseau d\'associations'
            },
            {
                'name': 'France Bénévolat',
                'url': 'https://www.francebenevolat.org/',
                'description': 'Réseau du bénévolat'
            },
            {
                'name': 'Recherche-Associations.fr',
                'url': 'https://www.recherche-associations.fr/',
                'description': 'Annuaire d\'associations'
            },
            {
                'name': 'Associations-France.org',
                'url': 'https://www.associations-france.org/',
                'description': 'Annuaire associatif'
            }
        ]
        
        results = []
        
        for site in alternative_sites:
            try:
                logging.info(f"   🔍 Test: {site['name']}")
                
                response = self.session.get(site['url'], timeout=10)
                
                result = {
                    'name': site['name'],
                    'url': site['url'],
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'description': site['description']
                }
                
                if response.status_code == 200:
                    # Analyser le contenu pour des associations
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Chercher des liens d'associations
                    association_links = []
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        text = link.get_text(strip=True).lower()
                        if any(keyword in text for keyword in ['association', 'club', 'federation']):
                            association_links.append(href)
                    
                    result['potential_association_links'] = len(association_links)
                    
                    # Chercher des formulaires de recherche
                    search_forms = soup.find_all('form')
                    result['search_forms'] = len(search_forms)
                    
                    if association_links or search_forms:
                        logging.info(f"      🎯 {len(association_links)} liens d'associations, {len(search_forms)} formulaires")
                
                results.append(result)
                
            except Exception as e:
                logging.error(f"      ❌ Erreur {site['name']}: {e}")
                results.append({
                    'name': site['name'],
                    'url': site['url'],
                    'error': str(e),
                    'accessible': False
                })
        
        return {
            'method': 'alternative_association_sites',
            'sites_tested': len(alternative_sites),
            'accessible_sites': len([r for r in results if r.get('accessible')]),
            'results': results
        }
    
    def method_web_scraping_aggregation(self):
        """
        MÉTHODE: Agrégation de plusieurs sources par scraping
        """
        logging.info("🕷️ MÉTHODE: Agrégation par scraping")
        
        scraping_targets = [
            {
                'name': 'Pages Jaunes - Associations',
                'url': 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=association&ou=france',
                'description': 'Annuaire commercial'
            },
            {
                'name': 'Yelp - Associations',
                'url': 'https://www.yelp.fr/search?find_desc=association&find_loc=France',
                'description': 'Plateforme d\'avis'
            },
            {
                'name': 'Google Maps - Associations',
                'url': 'https://www.google.com/maps/search/association+france',
                'description': 'Cartographie'
            }
        ]
        
        results = []
        
        for target in scraping_targets:
            try:
                logging.info(f"   🔍 Test: {target['name']}")
                
                if self.driver:
                    self.driver.get(target['url'])
                    time.sleep(3)
                    
                    # Analyser la page
                    soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                    
                    # Chercher des éléments d'associations
                    potential_associations = 0
                    
                    # Méthodes de détection spécifiques par site
                    if 'pagesjaunes' in target['url']:
                        potential_associations = len(soup.find_all(class_=lambda x: x and 'bi-denomination' in str(x)))
                    elif 'yelp' in target['url']:
                        potential_associations = len(soup.find_all(class_=lambda x: x and 'businessName' in str(x)))
                    elif 'google' in target['url']:
                        potential_associations = len(soup.find_all(attrs={'data-value': True}))
                    
                    result = {
                        'name': target['name'],
                        'url': target['url'],
                        'accessible': True,
                        'potential_associations': potential_associations,
                        'description': target['description']
                    }
                    
                    if potential_associations > 0:
                        logging.info(f"      🎯 {potential_associations} associations potentielles détectées")
                
                results.append(result)
                
            except Exception as e:
                logging.error(f"      ❌ Erreur {target['name']}: {e}")
                results.append({
                    'name': target['name'],
                    'url': target['url'],
                    'error': str(e),
                    'accessible': False
                })
        
        return {
            'method': 'web_scraping_aggregation',
            'targets_tested': len(scraping_targets),
            'results': results
        }
    
    def method_api_discovery_advanced(self):
        """
        MÉTHODE: Découverte d'APIs avancée
        """
        logging.info("🔗 MÉTHODE: Découverte d'APIs avancée")
        
        # APIs potentielles découvertes
        potential_apis = [
            'https://entreprise.data.gouv.fr/api/sirene/v1/siret/',
            'https://entreprise.data.gouv.fr/api/sirene/v3/unites_legales/',
            'https://api.insee.fr/entreprises/sirene/V3/siret/',
            'https://api.insee.fr/entreprises/sirene/V3/unites_legales/',
            'https://www.data-asso.fr/api/',
            'https://www.data-asso.fr/gw/',
            'https://api.journal-officiel.gouv.fr/',
        ]
        
        results = []
        
        for api_url in potential_apis:
            try:
                logging.info(f"   🔗 Test API: {api_url}")
                
                # Test différentes méthodes
                methods_results = {}
                
                for method in ['GET', 'POST', 'OPTIONS']:
                    try:
                        if method == 'GET':
                            response = self.session.get(api_url, timeout=10)
                        elif method == 'POST':
                            response = self.session.post(api_url, json={}, timeout=10)
                        elif method == 'OPTIONS':
                            response = self.session.options(api_url, timeout=10)
                        
                        methods_results[method] = {
                            'status_code': response.status_code,
                            'content_type': response.headers.get('content-type', ''),
                            'content_length': len(response.content)
                        }
                        
                        if response.status_code == 200:
                            logging.info(f"      ✅ {method} réussi: {response.status_code}")
                        
                    except Exception as e:
                        methods_results[method] = {'error': str(e)}
                
                results.append({
                    'api_url': api_url,
                    'methods_results': methods_results
                })
                
            except Exception as e:
                logging.error(f"      ❌ Erreur API {api_url}: {e}")
        
        return {
            'method': 'api_discovery_advanced',
            'apis_tested': len(potential_apis),
            'results': results
        }
    
    def discover_all_other_methods(self):
        """
        Lance la découverte de TOUTES les autres méthodes
        """
        logging.info("🚀 DÉCOUVERTE DE TOUTES LES AUTRES MÉTHODES")
        
        methods = [
            self.method_official_government_sources,
            self.method_alternative_association_sites,
            self.method_web_scraping_aggregation,
            self.method_api_discovery_advanced,
        ]
        
        all_results = []
        
        for i, method in enumerate(methods):
            logging.info(f"\n{'='*60}")
            logging.info(f"MÉTHODE ALTERNATIVE {i+1}/{len(methods)}")
            
            try:
                result = method()
                all_results.append(result)
                
                logging.info(f"✅ Méthode terminée: {result.get('method')}")
                
            except Exception as e:
                logging.error(f"❌ Erreur méthode {i+1}: {e}")
                all_results.append({'method': f'method_{i+1}', 'error': str(e)})
        
        return all_results
    
    def save_discovery_results(self, results):
        """
        Sauvegarde les résultats de découverte
        """
        discovery_results = {
            'metadata': {
                'discovery_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'methods_discovered': len(results),
                'purpose': 'Découverte d\'autres méthodes pour obtenir TOUTES les associations françaises'
            },
            'methods': results
        }
        
        with open('other_methods_discovery.json', 'w', encoding='utf-8') as f:
            json.dump(discovery_results, f, ensure_ascii=False, indent=2)
        
        logging.info("💾 Résultats de découverte sauvegardés: other_methods_discovery.json")

def main():
    """
    Découverte principale d'autres méthodes
    """
    print("🔍 DÉCOUVERTE D'AUTRES MÉTHODES POUR TOUTES LES ASSOCIATIONS")
    print("=" * 70)
    print("Ce script va découvrir d'AUTRES méthodes possibles pour")
    print("obtenir TOUTES les associations françaises, au-delà de data-asso.fr:")
    print()
    print("1. Sources gouvernementales officielles")
    print("2. Sites alternatifs d'associations")
    print("3. Agrégation par scraping web")
    print("4. Découverte d'APIs avancée")
    print()
    
    discoverer = OtherMethodsDiscoverer()
    
    try:
        discoverer.start_driver()
        
        # Découverte de toutes les autres méthodes
        results = discoverer.discover_all_other_methods()
        
        # Sauvegarder
        discoverer.save_discovery_results(results)
        
        # Afficher le résumé
        print(f"\n🎉 DÉCOUVERTE TERMINÉE!")
        print(f"   📊 Méthodes alternatives découvertes: {len(results)}")
        
        # Résumé par méthode
        for result in results:
            method_name = result.get('method', 'Unknown')
            print(f"\n📋 {method_name.upper()}:")
            
            if 'sources_tested' in result:
                print(f"   📊 Sources testées: {result['sources_tested']}")
                print(f"   ✅ Sources accessibles: {result['accessible_sources']}")
            elif 'sites_tested' in result:
                print(f"   📊 Sites testés: {result['sites_tested']}")
                print(f"   ✅ Sites accessibles: {result['accessible_sites']}")
            elif 'apis_tested' in result:
                print(f"   📊 APIs testées: {result['apis_tested']}")
            
            if 'error' in result:
                print(f"   ❌ Erreur: {result['error']}")
        
        print(f"\n💾 Résultats détaillés sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        
    finally:
        discoverer.close_driver()

if __name__ == "__main__":
    main()
