import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import urllib.parse

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mega_scraper_all.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class MegaScraperAllAssociations:
    def __init__(self):
        """
        MEGA SCRAPER pour TOUTES les associations françaises
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.all_associations = {}
        self.stats = {
            'total_found': 0,
            'sources_success': 0,
            'sources_failed': 0,
            'by_source': {}
        }
        
        # Lock pour thread safety
        self.lock = threading.Lock()
        
    def add_association(self, rna, nom, source, **kwargs):
        """Ajoute une association de manière thread-safe"""
        with self.lock:
            if rna not in self.all_associations:
                self.all_associations[rna] = {
                    'rna': rna,
                    'nom': nom,
                    'source': source,
                    **kwargs
                }
                self.stats['total_found'] += 1
                self.stats['by_source'][source] = self.stats['by_source'].get(source, 0) + 1
                return True
        return False
    
    def source_1_data_gouv_official(self):
        """SOURCE 1: data.gouv.fr - Données officielles"""
        logging.info("🏛️ SOURCE 1: data.gouv.fr - Données officielles")
        
        try:
            # API de recherche data.gouv.fr
            search_url = "https://www.data.gouv.fr/api/1/datasets/"
            params = {
                'q': 'associations répertoire national RNA',
                'page_size': 50
            }
            
            response = self.session.get(search_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            datasets = data.get('data', [])
            
            logging.info(f"   📊 {len(datasets)} datasets trouvés")
            
            for dataset in datasets:
                try:
                    # Chercher des ressources CSV/JSON
                    resources = dataset.get('resources', [])
                    for resource in resources:
                        if resource.get('format', '').lower() in ['csv', 'json']:
                            url = resource.get('url')
                            if url:
                                logging.info(f"   📄 Téléchargement: {resource.get('title', 'Dataset')}")
                                self.download_and_process_dataset(url, 'data.gouv.fr')
                                break  # Un seul fichier par dataset pour éviter les doublons
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur dataset: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur data.gouv.fr: {e}")
            self.stats['sources_failed'] += 1
    
    def source_2_journal_officiel(self):
        """SOURCE 2: Journal Officiel - Déclarations officielles"""
        logging.info("📰 SOURCE 2: Journal Officiel")
        
        try:
            # Recherche sur le Journal Officiel
            base_url = "https://www.journal-officiel.gouv.fr/pages/associations-search/"
            
            # Différentes stratégies de recherche
            search_terms = [
                'association', 'club', 'federation', 'union', 'comite',
                'sport', 'culture', 'social', 'education', 'sante'
            ]
            
            for term in search_terms:
                try:
                    search_url = f"{base_url}?q={urllib.parse.quote(term)}"
                    response = self.session.get(search_url, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Chercher des associations dans le contenu
                        associations_found = self.extract_associations_from_html(soup, 'journal-officiel')
                        
                        if associations_found > 0:
                            logging.info(f"   🎯 '{term}': {associations_found} associations")
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur terme '{term}': {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Journal Officiel: {e}")
            self.stats['sources_failed'] += 1
    
    def source_3_helloasso(self):
        """SOURCE 3: HelloAsso - Plateforme associative"""
        logging.info("💰 SOURCE 3: HelloAsso")
        
        try:
            # HelloAsso a une API publique
            api_urls = [
                "https://api.helloasso.com/v5/organizations",
                "https://www.helloasso.com/api/organizations",
            ]
            
            for api_url in api_urls:
                try:
                    response = self.session.get(api_url, timeout=15)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            self.extract_associations_from_json(data, 'helloasso')
                        except:
                            # Si ce n'est pas du JSON, essayer HTML
                            soup = BeautifulSoup(response.content, 'html.parser')
                            self.extract_associations_from_html(soup, 'helloasso')
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur API {api_url}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur HelloAsso: {e}")
            self.stats['sources_failed'] += 1
    
    def source_4_pages_jaunes(self):
        """SOURCE 4: Pages Jaunes - Annuaire commercial"""
        logging.info("📞 SOURCE 4: Pages Jaunes")
        
        try:
            # Recherche d'associations sur Pages Jaunes
            search_terms = ['association', 'club', 'federation']
            locations = ['france', 'paris', 'lyon', 'marseille', 'toulouse']
            
            for term in search_terms:
                for location in locations:
                    try:
                        url = f"https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui={term}&ou={location}"
                        response = self.session.get(url, timeout=15)
                        
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            found = self.extract_associations_from_html(soup, 'pages-jaunes')
                            
                            if found > 0:
                                logging.info(f"   🎯 '{term}' à '{location}': {found} associations")
                        
                        time.sleep(1)  # Respecter le site
                        
                    except Exception as e:
                        logging.debug(f"   ❌ Erreur {term}/{location}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Pages Jaunes: {e}")
            self.stats['sources_failed'] += 1
    
    def source_5_associations_gouv(self):
        """SOURCE 5: associations.gouv.fr - Portail officiel"""
        logging.info("🏛️ SOURCE 5: associations.gouv.fr")
        
        try:
            base_url = "https://www.associations.gouv.fr"
            
            # Pages à explorer
            pages = [
                "/",
                "/annuaire-associations",
                "/recherche-associations",
                "/liste-associations"
            ]
            
            for page in pages:
                try:
                    url = base_url + page
                    response = self.session.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        found = self.extract_associations_from_html(soup, 'associations.gouv.fr')
                        
                        if found > 0:
                            logging.info(f"   🎯 Page '{page}': {found} associations")
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur page {page}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur associations.gouv.fr: {e}")
            self.stats['sources_failed'] += 1
    
    def source_6_sirene_api(self):
        """SOURCE 6: API Sirene INSEE - Données officielles"""
        logging.info("🏢 SOURCE 6: API Sirene INSEE")
        
        try:
            # API Sirene publique (limitée mais officielle)
            base_url = "https://api.insee.fr/entreprises/sirene/V3/siret"
            
            # Recherche d'associations (code APE spécifique aux associations)
            ape_codes_associations = [
                '9499Z',  # Autres organisations fonctionnant par adhésion volontaire
                '9411Z',  # Activités des organisations patronales et consulaires
                '9412Z',  # Activités des organisations professionnelles
                '9420Z',  # Activités des syndicats de salariés
                '9491Z',  # Activités des organisations religieuses
                '9492Z',  # Activités des organisations politiques
            ]
            
            for ape_code in ape_codes_associations:
                try:
                    params = {
                        'q': f'activitePrincipaleUniteLegale:{ape_code}',
                        'nombre': 1000  # Maximum autorisé
                    }
                    
                    response = self.session.get(base_url, params=params, timeout=30)
                    
                    if response.status_code == 200:
                        data = response.json()
                        etablissements = data.get('etablissements', [])
                        
                        for etab in etablissements:
                            try:
                                unite_legale = etab.get('uniteLegale', {})
                                siren = unite_legale.get('siren')
                                denomination = unite_legale.get('denominationUniteLegale', '')
                                
                                if siren and denomination:
                                    # Générer un RNA fictif basé sur le SIREN (format W + SIREN + lettre)
                                    rna = f"W{siren}A"
                                    
                                    self.add_association(
                                        rna=rna,
                                        nom=denomination,
                                        source='sirene-api',
                                        siren=siren,
                                        ape_code=ape_code
                                    )
                            
                            except Exception as e:
                                logging.debug(f"   ❌ Erreur établissement: {e}")
                        
                        logging.info(f"   🎯 APE {ape_code}: {len(etablissements)} établissements")
                    
                    time.sleep(1)  # Respecter les limites de l'API
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur APE {ape_code}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur API Sirene: {e}")
            self.stats['sources_failed'] += 1
    
    def download_and_process_dataset(self, url, source):
        """Télécharge et traite un dataset"""
        try:
            logging.info(f"   ⬇️ Téléchargement: {url}")
            
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            # Traiter selon le type de contenu
            content_type = response.headers.get('content-type', '').lower()
            
            if 'json' in content_type:
                data = response.json()
                self.extract_associations_from_json(data, source)
            elif 'csv' in content_type or url.endswith('.csv'):
                # Traiter le CSV ligne par ligne pour éviter de surcharger la mémoire
                lines_processed = 0
                for line in response.iter_lines(decode_unicode=True):
                    if lines_processed > 100000:  # Limiter pour les tests
                        break
                    
                    # Chercher des RNAs dans chaque ligne
                    rna_matches = re.findall(r'W[0-9]{9}[A-Z]?', line)
                    for rna in rna_matches:
                        # Extraire le nom (approximatif)
                        parts = line.split(',')
                        nom = parts[1] if len(parts) > 1 else 'Association'
                        
                        self.add_association(
                            rna=rna,
                            nom=nom.strip('"'),
                            source=source
                        )
                    
                    lines_processed += 1
                    
                    if lines_processed % 10000 == 0:
                        logging.info(f"      📊 {lines_processed} lignes traitées...")
            
        except Exception as e:
            logging.error(f"   ❌ Erreur téléchargement {url}: {e}")
    
    def extract_associations_from_html(self, soup, source):
        """Extrait des associations depuis du HTML"""
        found_count = 0
        
        try:
            # Chercher des liens vers des associations
            for link in soup.find_all('a', href=True):
                href = link['href']
                text = link.get_text(strip=True)
                
                # Patterns pour identifier des associations
                if any(keyword in text.lower() for keyword in ['association', 'club', 'federation', 'union']):
                    # Essayer d'extraire un RNA depuis l'URL ou le texte
                    rna_match = re.search(r'W[0-9]{9}[A-Z]?', href + ' ' + text)
                    
                    if rna_match:
                        rna = rna_match.group()
                    else:
                        # Générer un RNA fictif basé sur le hash du nom
                        rna = f"W{hash(text) % 1000000000:09d}A"
                    
                    if self.add_association(
                        rna=rna,
                        nom=text,
                        source=source,
                        url=href
                    ):
                        found_count += 1
            
            # Chercher des RNAs dans le texte brut
            text_content = soup.get_text()
            rna_matches = re.findall(r'W[0-9]{9}[A-Z]?', text_content)
            
            for rna in rna_matches:
                if self.add_association(
                    rna=rna,
                    nom='Association trouvée par RNA',
                    source=source
                ):
                    found_count += 1
        
        except Exception as e:
            logging.debug(f"Erreur extraction HTML: {e}")
        
        return found_count
    
    def extract_associations_from_json(self, data, source):
        """Extrait des associations depuis du JSON"""
        found_count = 0
        
        def search_recursive(obj):
            nonlocal found_count
            
            if isinstance(obj, dict):
                # Vérifier si c'est une association
                if self.looks_like_association(obj):
                    rna = obj.get('rna') or obj.get('id_rna') or obj.get('RNA')
                    nom = obj.get('nom') or obj.get('name') or obj.get('denomination') or 'Association JSON'
                    
                    if not rna:
                        # Générer un RNA fictif
                        rna = f"W{hash(nom) % 1000000000:09d}A"
                    
                    if self.add_association(
                        rna=rna,
                        nom=nom,
                        source=source,
                        **{k: v for k, v in obj.items() if k not in ['rna', 'nom']}
                    ):
                        found_count += 1
                
                # Continuer la recherche récursive
                for value in obj.values():
                    search_recursive(value)
                    
            elif isinstance(obj, list):
                for item in obj:
                    search_recursive(item)
        
        try:
            search_recursive(data)
        except Exception as e:
            logging.debug(f"Erreur extraction JSON: {e}")
        
        return found_count
    
    def looks_like_association(self, obj):
        """Détermine si un objet ressemble à une association"""
        if not isinstance(obj, dict):
            return False
        
        indicators = ['rna', 'siren', 'nom', 'name', 'denomination', 'association', 'club']
        found = sum(1 for key in obj.keys() if any(ind in key.lower() for ind in indicators))
        return found >= 2
    
    def run_all_sources_parallel(self):
        """Lance toutes les sources en parallèle"""
        logging.info("🚀 LANCEMENT DE TOUTES LES SOURCES EN PARALLÈLE")
        
        sources = [
            ("data.gouv.fr", self.source_1_data_gouv_official),
            ("Journal Officiel", self.source_2_journal_officiel),
            ("HelloAsso", self.source_3_helloasso),
            ("Pages Jaunes", self.source_4_pages_jaunes),
            ("associations.gouv.fr", self.source_5_associations_gouv),
            ("API Sirene", self.source_6_sirene_api),
        ]
        
        # Exécution en parallèle avec ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_source = {executor.submit(source_func): source_name 
                              for source_name, source_func in sources}
            
            for future in as_completed(future_to_source):
                source_name = future_to_source[future]
                try:
                    future.result()
                    logging.info(f"✅ {source_name} terminé")
                except Exception as e:
                    logging.error(f"❌ {source_name} échoué: {e}")
        
        return self.stats
    
    def save_all_results(self):
        """Sauvegarde tous les résultats"""
        associations_list = list(self.all_associations.values())
        
        # JSON complet
        final_results = {
            'metadata': {
                'total_associations': len(associations_list),
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'sources_used': 'ALL_FRENCH_SOURCES',
                'stats': self.stats
            },
            'associations': associations_list
        }
        
        with open('mega_scraper_all_associations.json', 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        # CSV
        if associations_list:
            with open('mega_scraper_all_associations.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'source', 'siren', 'url', 'ape_code']
                writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()
                for assoc in associations_list:
                    writer.writerow(assoc)
        
        # CSV des contacts (si disponibles)
        contacts = [a for a in associations_list if a.get('email') or a.get('telephone')]
        if contacts:
            with open('mega_scraper_contacts.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'source', 'email', 'telephone', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()
                for assoc in contacts:
                    writer.writerow(assoc)
        
        logging.info("💾 Résultats sauvegardés:")
        logging.info("   📄 mega_scraper_all_associations.json")
        logging.info("   📊 mega_scraper_all_associations.csv")
        if contacts:
            logging.info(f"   📧 mega_scraper_contacts.csv ({len(contacts)} avec contacts)")

def main():
    """
    MEGA SCRAPER principal pour TOUTES les associations françaises
    """
    print("🚀 MEGA SCRAPER - TOUTES LES ASSOCIATIONS FRANÇAISES")
    print("=" * 70)
    print("Ce scraper va chercher sur TOUTES les sources françaises:")
    print("1. data.gouv.fr - Données officielles")
    print("2. Journal Officiel - Déclarations")
    print("3. HelloAsso - Plateforme associative")
    print("4. Pages Jaunes - Annuaire commercial")
    print("5. associations.gouv.fr - Portail officiel")
    print("6. API Sirene INSEE - Données officielles")
    print()
    print("🎯 OBJECTIF: Récupérer des MILLIERS d'associations !")
    print("⏱️ DURÉE ESTIMÉE: 30-60 minutes")
    print()
    
    response = input("Voulez-vous lancer le MEGA SCRAPER? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = MegaScraperAllAssociations()
    
    try:
        start_time = time.time()
        
        # Lancer toutes les sources
        stats = scraper.run_all_sources_parallel()
        
        # Sauvegarder
        scraper.save_all_results()
        
        # Afficher le résumé final
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 MEGA SCRAPING TERMINÉ!")
        print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
        print(f"   🏢 Total associations: {stats['total_found']}")
        print(f"   ✅ Sources réussies: {stats['sources_success']}")
        print(f"   ❌ Sources échouées: {stats['sources_failed']}")
        
        print(f"\n📊 RÉSULTATS PAR SOURCE:")
        for source, count in stats['by_source'].items():
            print(f"   📊 {source}: {count} associations")
        
        if scraper.all_associations:
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS TROUVÉES:")
            for i, assoc in enumerate(list(scraper.all_associations.values())[:10]):
                print(f"   {i+1}. {assoc['rna']}: {assoc['nom'][:50]}...")
                print(f"      Source: {assoc['source']}")
        
        print(f"\n💾 Résultats complets sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
