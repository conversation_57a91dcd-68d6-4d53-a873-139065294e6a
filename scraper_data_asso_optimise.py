#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRAPER DATA-ASSO.FR OPTIMISÉ
Utilise la méthode des codes postaux découverte lors de l'analyse
"""

import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_optimise.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ScraperDataAssoOptimise:
    def __init__(self):
        """Scraper optimisé pour data-asso.fr"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.associations = {}
        self.associations_avec_contacts = {}
        self.lock = threading.Lock()
        
        # Patterns pour contacts
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_patterns = [
            re.compile(r'0[1-9](?:[0-9]{8})'),
            re.compile(r'\+33[1-9](?:[0-9]{8})'),
            re.compile(r'(?:0[1-9])(?:[-.\s]?[0-9]{2}){4}'),
        ]
        
        self.stats = {
            'total_associations': 0,
            'avec_contacts': 0,
            'codes_postaux_testes': 0,
            'codes_postaux_reussis': 0
        }
    
    def generer_codes_postaux_francais(self):
        """Génère tous les codes postaux français"""
        codes_postaux = []
        
        # France métropolitaine (01-95)
        for dept in range(1, 96):
            if dept == 20:  # Corse divisée en 2A et 2B
                continue
            
            # Générer les codes postaux pour ce département
            if dept < 10:
                prefix = f"0{dept}"
            else:
                prefix = str(dept)
            
            # Codes postaux principaux pour chaque département
            if dept in [75]:  # Paris - tous les arrondissements
                for arr in range(1, 21):
                    codes_postaux.append(f"750{arr:02d}")
            elif dept in [69]:  # Lyon
                for arr in range(1, 10):
                    codes_postaux.append(f"6900{arr}")
            elif dept in [13]:  # Marseille
                for arr in range(1, 17):
                    codes_postaux.append(f"130{arr:02d}")
            else:
                # Codes postaux standards
                codes_postaux.extend([
                    f"{prefix}000",
                    f"{prefix}001",
                    f"{prefix}100",
                    f"{prefix}200"
                ])
        
        # DOM-TOM
        dom_tom = ['97100', '97110', '97200', '97300', '97400', '97500']
        codes_postaux.extend(dom_tom)
        
        # Corse
        codes_postaux.extend(['20000', '20100', '20200', '20300'])
        
        logging.info(f"📍 {len(codes_postaux)} codes postaux générés")
        return codes_postaux
    
    def scraper_par_code_postal(self, code_postal):
        """Scrape les associations pour un code postal donné"""
        try:
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            
            try:
                # Aller sur la page annuaire
                driver.get("https://www.data-asso.fr/annuaire")
                time.sleep(3)
                
                # Chercher le champ de recherche
                search_input = driver.find_element(By.XPATH, "//input[@type='text' or @type='search']")
                search_input.clear()
                search_input.send_keys(code_postal)
                search_input.send_keys(Keys.RETURN)
                time.sleep(3)
                
                # Extraire les associations
                associations_trouvees = self.extraire_associations_page(driver, code_postal)
                
                with self.lock:
                    self.stats['codes_postaux_testes'] += 1
                    if associations_trouvees > 0:
                        self.stats['codes_postaux_reussis'] += 1
                
                logging.info(f"   📍 {code_postal}: {associations_trouvees} associations")
                
                return associations_trouvees
                
            finally:
                driver.quit()
                
        except Exception as e:
            logging.debug(f"   ❌ Erreur {code_postal}: {e}")
            return 0
    
    def extraire_associations_page(self, driver, code_postal):
        """Extrait les associations de la page actuelle"""
        try:
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            associations_trouvees = 0
            
            # Chercher tous les liens vers des associations
            for link in soup.find_all('a', href=True):
                href = link['href']
                
                if '/association/' in href:
                    # Extraire le RNA
                    rna_match = re.search(r'/association/([A-Z0-9]+)', href)
                    if rna_match:
                        rna = rna_match.group(1)
                        nom = link.get_text(strip=True)
                        
                        if rna not in self.associations:
                            association = {
                                'rna': rna,
                                'nom': nom,
                                'code_postal': code_postal,
                                'url': f"https://www.data-asso.fr{href}" if href.startswith('/') else href
                            }
                            
                            with self.lock:
                                self.associations[rna] = association
                                self.stats['total_associations'] += 1
                            
                            associations_trouvees += 1
            
            return associations_trouvees
            
        except Exception as e:
            logging.debug(f"Erreur extraction page: {e}")
            return 0
    
    def chercher_contacts_association(self, association):
        """Cherche les contacts d'une association spécifique"""
        try:
            rna = association['rna']
            url = association['url']
            
            # Visiter la page de l'association
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                # Extraire les contacts
                emails, phones = self.extraire_contacts_texte(response.text)
                
                if emails or phones:
                    contact_info = {
                        'rna': rna,
                        'nom': association['nom'],
                        'code_postal': association['code_postal'],
                        'email': emails[0] if emails else '',
                        'telephone': phones[0] if phones else '',
                        'emails_all': emails,
                        'telephones_all': phones,
                        'url': url
                    }
                    
                    with self.lock:
                        self.associations_avec_contacts[rna] = contact_info
                        self.stats['avec_contacts'] += 1
                    
                    return contact_info
            
            return None
            
        except Exception as e:
            logging.debug(f"Erreur contacts {association['rna']}: {e}")
            return None
    
    def extraire_contacts_texte(self, text):
        """Extrait emails et téléphones d'un texte"""
        emails = self.email_pattern.findall(text)
        phones = []
        
        for pattern in self.phone_patterns:
            phones.extend(pattern.findall(text))
        
        # Nettoyer et dédupliquer
        emails = list(set(emails))
        phones = list(set([re.sub(r'[-.\s]', '', phone) for phone in phones if len(re.sub(r'[-.\s]', '', phone)) >= 10]))
        
        return emails, phones
    
    def scraper_toutes_associations(self, max_codes_postaux=100):
        """Scrape toutes les associations par codes postaux"""
        logging.info("🚀 SCRAPING OPTIMISÉ PAR CODES POSTAUX")
        
        codes_postaux = self.generer_codes_postaux_francais()
        
        # Limiter pour les tests
        codes_postaux = codes_postaux[:max_codes_postaux]
        
        logging.info(f"📍 Test de {len(codes_postaux)} codes postaux")
        
        # Scraping en parallèle (limité pour éviter de surcharger le site)
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_cp = {executor.submit(self.scraper_par_code_postal, cp): cp for cp in codes_postaux}
            
            for future in as_completed(future_to_cp):
                cp = future_to_cp[future]
                try:
                    result = future.result()
                    
                    # Afficher le progrès
                    if self.stats['codes_postaux_testes'] % 10 == 0:
                        logging.info(f"   📊 Progrès: {self.stats['codes_postaux_testes']}/{len(codes_postaux)} codes postaux")
                        logging.info(f"      📊 {self.stats['total_associations']} associations trouvées")
                        logging.info(f"      ✅ {self.stats['codes_postaux_reussis']} codes postaux avec résultats")
                
                except Exception as e:
                    logging.error(f"Erreur future {cp}: {e}")
                
                time.sleep(0.5)  # Pause respectueuse
        
        logging.info(f"✅ Scraping terminé: {self.stats['total_associations']} associations")
    
    def chercher_tous_contacts(self, max_associations=500):
        """Cherche les contacts pour toutes les associations"""
        logging.info(f"📧 RECHERCHE CONTACTS POUR {max_associations} ASSOCIATIONS")
        
        associations_list = list(self.associations.values())[:max_associations]
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_assoc = {executor.submit(self.chercher_contacts_association, assoc): assoc for assoc in associations_list}
            
            for i, future in enumerate(as_completed(future_to_assoc)):
                try:
                    result = future.result()
                    
                    if result:
                        logging.info(f"   🎯 Contact trouvé: {result['nom'][:40]}...")
                        if result['email']:
                            logging.info(f"      📧 {result['email']}")
                        if result['telephone']:
                            logging.info(f"      📞 {result['telephone']}")
                    
                    if (i + 1) % 50 == 0:
                        logging.info(f"   📊 {i+1}/{len(associations_list)} associations vérifiées")
                        logging.info(f"      📧 {self.stats['avec_contacts']} contacts trouvés")
                
                except Exception as e:
                    logging.debug(f"Erreur future contacts: {e}")
        
        logging.info(f"✅ Recherche contacts terminée: {self.stats['avec_contacts']} contacts")
    
    def sauvegarder_resultats(self):
        """Sauvegarde tous les résultats"""
        # JSON complet
        resultats = {
            'metadata': {
                'total_associations': self.stats['total_associations'],
                'avec_contacts': self.stats['avec_contacts'],
                'codes_postaux_testes': self.stats['codes_postaux_testes'],
                'codes_postaux_reussis': self.stats['codes_postaux_reussis'],
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'methode': 'codes_postaux_optimise'
            },
            'associations': list(self.associations.values()),
            'contacts': list(self.associations_avec_contacts.values())
        }
        
        with open('data_asso_optimise_resultats.json', 'w', encoding='utf-8') as f:
            json.dump(resultats, f, ensure_ascii=False, indent=2)
        
        # CSV toutes associations
        if self.associations:
            with open('data_asso_optimise_associations.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'code_postal', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for assoc in self.associations.values():
                    writer.writerow(assoc)
        
        # CSV contacts seulement
        if self.associations_avec_contacts:
            with open('data_asso_optimise_contacts.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['rna', 'nom', 'code_postal', 'email', 'telephone', 'emails_all', 'telephones_all', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for contact in self.associations_avec_contacts.values():
                    # Convertir les listes en chaînes pour CSV
                    contact_copy = contact.copy()
                    contact_copy['emails_all'] = '; '.join(contact.get('emails_all', []))
                    contact_copy['telephones_all'] = '; '.join(contact.get('telephones_all', []))
                    writer.writerow(contact_copy)
        
        logging.info("💾 Résultats sauvegardés:")
        logging.info("   📄 data_asso_optimise_resultats.json")
        logging.info("   📊 data_asso_optimise_associations.csv")
        if self.associations_avec_contacts:
            logging.info(f"   📧 data_asso_optimise_contacts.csv ({len(self.associations_avec_contacts)} contacts)")
    
    def run_scraping_optimise(self):
        """Lance le scraping optimisé complet"""
        logging.info("🚀 SCRAPING DATA-ASSO.FR OPTIMISÉ")
        logging.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # ÉTAPE 1: Scraper par codes postaux
            self.scraper_toutes_associations(max_codes_postaux=200)
            
            # ÉTAPE 2: Chercher les contacts
            if self.associations:
                self.chercher_tous_contacts(max_associations=1000)
            
            # ÉTAPE 3: Sauvegarder
            self.sauvegarder_resultats()
            
            # Résumé final
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n🎉 SCRAPING OPTIMISÉ TERMINÉ!")
            print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
            print(f"   📍 Codes postaux testés: {self.stats['codes_postaux_testes']}")
            print(f"   ✅ Codes postaux avec résultats: {self.stats['codes_postaux_reussis']}")
            print(f"   📊 Total associations: {self.stats['total_associations']}")
            print(f"   📧 Avec contacts: {self.stats['avec_contacts']}")
            
            if self.associations_avec_contacts:
                print(f"\n📧 EXEMPLES DE CONTACTS TROUVÉS:")
                for i, contact in enumerate(list(self.associations_avec_contacts.values())[:5]):
                    print(f"   {i+1}. {contact['nom'][:40]}... (CP: {contact['code_postal']})")
                    if contact['email']:
                        print(f"      📧 {contact['email']}")
                    if contact['telephone']:
                        print(f"      📞 {contact['telephone']}")
            
            print(f"\n💾 Fichiers créés et prêts à utiliser!")
            
        except Exception as e:
            logging.error(f"❌ Erreur scraping optimisé: {e}")

def main():
    """Fonction principale"""
    print("🚀 SCRAPER DATA-ASSO.FR OPTIMISÉ")
    print("=" * 60)
    print("Utilise la méthode des codes postaux découverte lors de l'analyse")
    print("pour récupérer un maximum d'associations avec leurs contacts.")
    print()
    print("🎯 Méthode: Recherche par codes postaux français")
    print("📍 Couverture: France métropolitaine + DOM-TOM")
    print("📧 Objectif: Trouver des associations avec contacts")
    print()
    
    response = input("Voulez-vous lancer le scraping optimisé? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = ScraperDataAssoOptimise()
    scraper.run_scraping_optimise()

if __name__ == "__main__":
    main()
