#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VÉRIFICATEUR DE DOUBLONS ASSOCIATIONS
Vérifie s'il y a des doublons dans les associations récupérées
"""

import pandas as pd
import json
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)

def verifier_doublons_excel():
    """Vérifie les doublons dans le fichier Excel"""
    print("🔍 VÉRIFICATION DES DOUBLONS DANS LE FICHIER EXCEL")
    print("=" * 60)
    
    try:
        # Charger le fichier Excel
        df = pd.read_excel('Associations_Francaises_Complet.xlsx', sheet_name='Toutes_Associations')
        
        print(f"📊 Total associations dans Excel: {len(df)}")
        
        # Vérifier les doublons par RNA
        doublons_rna = df[df.duplicated(subset=['RNA'], keep=False)]
        
        if len(doublons_rna) > 0:
            print(f"❌ {len(doublons_rna)} doublons trouvés par RNA:")
            
            # Grouper par RNA pour voir les doublons
            for rna, group in doublons_rna.groupby('RNA'):
                print(f"\n🔍 RNA {rna} ({len(group)} occurrences):")
                for idx, row in group.iterrows():
                    print(f"   - {row['Nom_Association'][:50]}... (Source: {row['Source']})")
        else:
            print("✅ Aucun doublon trouvé par RNA")
        
        # Vérifier les doublons par nom
        doublons_nom = df[df.duplicated(subset=['Nom_Association'], keep=False)]
        
        if len(doublons_nom) > 0:
            print(f"\n📝 {len(doublons_nom)} associations avec noms similaires:")
            
            # Afficher quelques exemples
            for nom, group in doublons_nom.groupby('Nom_Association').head(10).items():
                if len(group) > 1:
                    print(f"\n🔍 Nom: {nom[:50]}... ({len(group)} occurrences)")
                    for idx, row in group.iterrows():
                        print(f"   - RNA: {row['RNA']} (Source: {row['Source']})")
        else:
            print("✅ Aucun doublon trouvé par nom")
        
        # Statistiques par source
        print(f"\n📊 RÉPARTITION PAR SOURCE:")
        sources_stats = df['Source'].value_counts()
        for source, count in sources_stats.items():
            print(f"   📊 {source}: {count} associations")
        
        # Vérifier les doublons entre sources
        print(f"\n🔍 VÉRIFICATION CROISÉE ENTRE SOURCES:")
        
        # Associations data.gouv.fr
        df_datagouv = df[df['Source'] == 'data.gouv.fr']
        
        # Associations data-asso.fr
        df_dataasso = df[df['Source'] == 'data-asso.fr']
        
        print(f"   📊 data.gouv.fr: {len(df_datagouv)} associations")
        print(f"   📊 data-asso.fr: {len(df_dataasso)} associations")
        
        # Chercher des RNAs communs
        rnas_datagouv = set(df_datagouv['RNA'].dropna())
        rnas_dataasso = set(df_dataasso['RNA'].dropna())
        
        rnas_communs = rnas_datagouv.intersection(rnas_dataasso)
        
        if rnas_communs:
            print(f"⚠️ {len(rnas_communs)} RNAs communs entre les sources:")
            for rna in list(rnas_communs)[:10]:  # Afficher les 10 premiers
                print(f"   - {rna}")
        else:
            print("✅ Aucun RNA commun entre les sources")
        
        return {
            'total': len(df),
            'doublons_rna': len(doublons_rna),
            'doublons_nom': len(doublons_nom),
            'rnas_communs': len(rnas_communs),
            'sources': dict(sources_stats)
        }
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def verifier_doublons_json():
    """Vérifie les doublons dans les fichiers JSON"""
    print(f"\n🔍 VÉRIFICATION DES FICHIERS JSON")
    print("=" * 40)
    
    fichiers_json = [
        'mega_scraper_all_associations.json',
        'all_methods_data_asso_results.json'
    ]
    
    for fichier in fichiers_json:
        try:
            print(f"\n📄 Fichier: {fichier}")
            
            with open(fichier, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            associations = data.get('associations', [])
            if not associations:
                associations = data.get('results', {}).get('all_associations', [])
            
            print(f"   📊 Total associations: {len(associations)}")
            
            # Vérifier les doublons par RNA
            rnas = [assoc.get('rna') for assoc in associations if assoc.get('rna')]
            rnas_uniques = set(rnas)
            
            if len(rnas) != len(rnas_uniques):
                doublons = len(rnas) - len(rnas_uniques)
                print(f"   ❌ {doublons} doublons trouvés")
            else:
                print(f"   ✅ Aucun doublon")
            
        except FileNotFoundError:
            print(f"   ⚠️ Fichier {fichier} non trouvé")
        except Exception as e:
            print(f"   ❌ Erreur {fichier}: {e}")

def nettoyer_doublons_excel():
    """Nettoie les doublons du fichier Excel"""
    print(f"\n🧹 NETTOYAGE DES DOUBLONS")
    print("=" * 40)
    
    try:
        # Charger le fichier Excel
        df = pd.read_excel('Associations_Francaises_Complet.xlsx', sheet_name='Toutes_Associations')
        
        print(f"📊 Avant nettoyage: {len(df)} associations")
        
        # Supprimer les doublons par RNA (garder le premier)
        df_clean = df.drop_duplicates(subset=['RNA'], keep='first')
        
        print(f"📊 Après nettoyage: {len(df_clean)} associations")
        print(f"🗑️ {len(df) - len(df_clean)} doublons supprimés")
        
        # Sauvegarder le fichier nettoyé
        with pd.ExcelWriter('Associations_Francaises_Sans_Doublons.xlsx', engine='openpyxl') as writer:
            # Feuille principale
            df_clean.to_excel(writer, sheet_name='Toutes_Associations', index=False)
            
            # Feuille contacts seulement
            df_contacts = df_clean[df_clean['Avec_Contact'] == 'OUI']
            if not df_contacts.empty:
                df_contacts.to_excel(writer, sheet_name='Avec_Contacts', index=False)
            
            # Feuille statistiques
            stats_data = [
                ['Statistique', 'Valeur'],
                ['Total associations', len(df_clean)],
                ['Doublons supprimés', len(df) - len(df_clean)],
                [''],
                ['Sources', 'Nombre'],
            ]
            
            sources_stats = df_clean['Source'].value_counts()
            for source, count in sources_stats.items():
                stats_data.append([source, count])
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False, header=False)
        
        print(f"✅ Fichier nettoyé sauvegardé: Associations_Francaises_Sans_Doublons.xlsx")
        
        return len(df) - len(df_clean)
        
    except Exception as e:
        print(f"❌ Erreur nettoyage: {e}")
        return 0

def analyser_qualite_data_asso():
    """Analyse la qualité des données de data-asso.fr"""
    print(f"\n🔍 ANALYSE QUALITÉ DATA-ASSO.FR")
    print("=" * 40)
    
    try:
        # Charger le fichier Excel
        df = pd.read_excel('Associations_Francaises_Complet.xlsx', sheet_name='Toutes_Associations')
        
        # Filtrer les associations data-asso.fr
        df_dataasso = df[df['Source'] == 'data-asso.fr']
        
        print(f"📊 Associations data-asso.fr: {len(df_dataasso)}")
        
        # Analyser les RNAs
        rnas_valides = df_dataasso['RNA'].str.match(r'^W[0-9]{9}[A-Z]?$', na=False).sum()
        rnas_invalides = len(df_dataasso) - rnas_valides
        
        print(f"✅ RNAs valides: {rnas_valides}")
        print(f"❌ RNAs invalides: {rnas_invalides}")
        
        # Analyser les noms
        noms_vides = df_dataasso['Nom_Association'].isna().sum()
        noms_courts = (df_dataasso['Nom_Association'].str.len() < 5).sum()
        
        print(f"📝 Noms vides: {noms_vides}")
        print(f"📝 Noms très courts (<5 char): {noms_courts}")
        
        # Afficher quelques exemples
        print(f"\n📋 EXEMPLES D'ASSOCIATIONS DATA-ASSO.FR:")
        for idx, row in df_dataasso.head(10).iterrows():
            print(f"   {row['RNA']}: {row['Nom_Association'][:50]}...")
        
        return {
            'total': len(df_dataasso),
            'rnas_valides': rnas_valides,
            'rnas_invalides': rnas_invalides,
            'noms_vides': noms_vides,
            'noms_courts': noms_courts
        }
        
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        return None

def main():
    """Fonction principale"""
    print("🔍 VÉRIFICATEUR DE DOUBLONS ASSOCIATIONS")
    print("=" * 60)
    
    # Vérifier les doublons dans Excel
    stats_excel = verifier_doublons_excel()
    
    # Vérifier les doublons dans JSON
    verifier_doublons_json()
    
    # Analyser la qualité data-asso.fr
    stats_dataasso = analyser_qualite_data_asso()
    
    # Proposer le nettoyage si des doublons sont trouvés
    if stats_excel and stats_excel['doublons_rna'] > 0:
        print(f"\n🧹 NETTOYAGE RECOMMANDÉ")
        response = input("Voulez-vous nettoyer les doublons? (o/n): ").lower().strip()
        if response == 'o':
            doublons_supprimes = nettoyer_doublons_excel()
            print(f"✅ {doublons_supprimes} doublons supprimés")
    
    # Résumé final
    print(f"\n📊 RÉSUMÉ FINAL:")
    if stats_excel:
        print(f"   📊 Total associations: {stats_excel['total']}")
        print(f"   🔍 Doublons RNA: {stats_excel['doublons_rna']}")
        print(f"   📝 Doublons nom: {stats_excel['doublons_nom']}")
        print(f"   🔗 RNAs communs entre sources: {stats_excel['rnas_communs']}")
    
    if stats_dataasso:
        print(f"\n📊 QUALITÉ DATA-ASSO.FR:")
        print(f"   📊 Total: {stats_dataasso['total']}")
        print(f"   ✅ RNAs valides: {stats_dataasso['rnas_valides']}")
        print(f"   ❌ RNAs invalides: {stats_dataasso['rnas_invalides']}")

if __name__ == "__main__":
    main()
