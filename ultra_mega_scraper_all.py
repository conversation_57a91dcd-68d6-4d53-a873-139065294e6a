import time
import json
import csv
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import urllib.parse

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultra_mega_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class UltraMegaScraperAll:
    def __init__(self):
        """
        ULTRA MEGA SCRAPER pour récupérer ENCORE PLUS d'associations
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.all_associations = {}
        self.stats = {
            'total_found': 0,
            'sources_success': 0,
            'sources_failed': 0,
            'by_source': {}
        }
        
        self.lock = threading.Lock()
        
        # Charger les associations existantes
        self.load_existing_associations()
        
    def load_existing_associations(self):
        """Charge les associations déjà récupérées"""
        try:
            with open('mega_scraper_all_associations.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                existing = data.get('associations', [])
                
                for assoc in existing:
                    rna = assoc.get('rna')
                    if rna:
                        self.all_associations[rna] = assoc
                
                logging.info(f"📊 {len(existing)} associations existantes chargées")
                self.stats['total_found'] = len(existing)
                
        except FileNotFoundError:
            logging.info("📊 Aucune association existante, démarrage à zéro")
    
    def add_association(self, rna, nom, source, **kwargs):
        """Ajoute une association de manière thread-safe"""
        with self.lock:
            if rna not in self.all_associations:
                self.all_associations[rna] = {
                    'rna': rna,
                    'nom': nom,
                    'source': source,
                    **kwargs
                }
                self.stats['total_found'] += 1
                self.stats['by_source'][source] = self.stats['by_source'].get(source, 0) + 1
                return True
        return False
    
    def source_1_yelp_associations(self):
        """SOURCE 1: Yelp - Associations et clubs"""
        logging.info("⭐ SOURCE 1: Yelp - Associations")
        
        try:
            search_terms = ['association', 'club', 'federation', 'union', 'comite']
            cities = ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Bordeaux', 'Lille', 'Rennes']
            
            for term in search_terms:
                for city in cities:
                    try:
                        url = f"https://www.yelp.fr/search?find_desc={urllib.parse.quote(term)}&find_loc={urllib.parse.quote(city)}"
                        response = self.session.get(url, timeout=15)
                        
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            
                            # Chercher les éléments d'entreprises/associations
                            business_elements = soup.find_all(class_=lambda x: x and any(
                                keyword in str(x).lower() for keyword in ['business', 'result', 'listing']
                            ))
                            
                            for element in business_elements:
                                try:
                                    name_elem = element.find('a') or element.find(class_=lambda x: x and 'name' in str(x).lower())
                                    if name_elem:
                                        nom = name_elem.get_text(strip=True)
                                        if any(keyword in nom.lower() for keyword in ['association', 'club', 'federation']):
                                            rna = f"YELP{hash(nom + city) % 1000000000:09d}"
                                            
                                            self.add_association(
                                                rna=rna,
                                                nom=nom,
                                                source='yelp',
                                                ville=city,
                                                url=url
                                            )
                                
                                except Exception as e:
                                    logging.debug(f"      ❌ Erreur élément: {e}")
                            
                            logging.info(f"   🎯 '{term}' à '{city}': {len(business_elements)} éléments traités")
                        
                        time.sleep(2)  # Respecter le site
                        
                    except Exception as e:
                        logging.debug(f"   ❌ Erreur {term}/{city}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Yelp: {e}")
            self.stats['sources_failed'] += 1
    
    def source_2_google_maps_associations(self):
        """SOURCE 2: Google Maps via recherche"""
        logging.info("🗺️ SOURCE 2: Google Maps - Associations")
        
        try:
            # Utiliser Selenium pour Google Maps
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            
            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            
            try:
                search_terms = ['association france', 'club france', 'federation france']
                
                for term in search_terms:
                    try:
                        url = f"https://www.google.com/maps/search/{urllib.parse.quote(term)}"
                        driver.get(url)
                        time.sleep(5)
                        
                        # Scroll pour charger plus de résultats
                        for _ in range(5):
                            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                            time.sleep(2)
                        
                        # Extraire les résultats
                        soup = BeautifulSoup(driver.page_source, 'html.parser')
                        
                        # Chercher les éléments de lieux
                        place_elements = soup.find_all(attrs={'data-value': True})
                        
                        for element in place_elements:
                            try:
                                name_elem = element.find(class_=lambda x: x and any(
                                    keyword in str(x).lower() for keyword in ['title', 'name', 'heading']
                                ))
                                
                                if name_elem:
                                    nom = name_elem.get_text(strip=True)
                                    if nom and len(nom) > 3:
                                        rna = f"GMAPS{hash(nom) % 1000000000:09d}"
                                        
                                        self.add_association(
                                            rna=rna,
                                            nom=nom,
                                            source='google-maps',
                                            search_term=term
                                        )
                            
                            except Exception as e:
                                logging.debug(f"      ❌ Erreur élément Google: {e}")
                        
                        logging.info(f"   🎯 '{term}': {len(place_elements)} lieux traités")
                        
                    except Exception as e:
                        logging.debug(f"   ❌ Erreur terme {term}: {e}")
            
            finally:
                driver.quit()
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Google Maps: {e}")
            self.stats['sources_failed'] += 1
    
    def source_3_facebook_associations(self):
        """SOURCE 3: Facebook - Pages d'associations"""
        logging.info("📘 SOURCE 3: Facebook - Pages associations")
        
        try:
            # Facebook Graph API (version publique limitée)
            search_terms = ['association', 'club', 'federation']
            
            for term in search_terms:
                try:
                    # Recherche publique Facebook (sans API key)
                    url = f"https://www.facebook.com/search/pages/?q={urllib.parse.quote(term + ' france')}"
                    response = self.session.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        # Facebook utilise beaucoup de JavaScript, donc extraction limitée
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Chercher des liens vers des pages
                        page_links = soup.find_all('a', href=True)
                        
                        for link in page_links:
                            href = link['href']
                            text = link.get_text(strip=True)
                            
                            if '/pages/' in href or text and any(keyword in text.lower() for keyword in ['association', 'club']):
                                if text and len(text) > 3 and len(text) < 100:
                                    rna = f"FB{hash(text) % 1000000000:09d}"
                                    
                                    self.add_association(
                                        rna=rna,
                                        nom=text,
                                        source='facebook',
                                        url=href if href.startswith('http') else f"https://facebook.com{href}"
                                    )
                        
                        logging.info(f"   🎯 '{term}': {len(page_links)} liens traités")
                    
                    time.sleep(3)  # Respecter Facebook
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur terme {term}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Facebook: {e}")
            self.stats['sources_failed'] += 1
    
    def source_4_linkedin_associations(self):
        """SOURCE 4: LinkedIn - Organisations"""
        logging.info("💼 SOURCE 4: LinkedIn - Organisations")
        
        try:
            search_terms = ['association', 'federation', 'organisation']
            
            for term in search_terms:
                try:
                    url = f"https://www.linkedin.com/search/results/companies/?keywords={urllib.parse.quote(term + ' france')}"
                    response = self.session.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Chercher les éléments d'entreprises/organisations
                        company_elements = soup.find_all(class_=lambda x: x and any(
                            keyword in str(x).lower() for keyword in ['company', 'organization', 'result']
                        ))
                        
                        for element in company_elements:
                            try:
                                name_elem = element.find('a') or element.find(class_=lambda x: x and 'name' in str(x).lower())
                                if name_elem:
                                    nom = name_elem.get_text(strip=True)
                                    if nom and len(nom) > 3:
                                        rna = f"LI{hash(nom) % 1000000000:09d}"
                                        
                                        self.add_association(
                                            rna=rna,
                                            nom=nom,
                                            source='linkedin',
                                            search_term=term
                                        )
                            
                            except Exception as e:
                                logging.debug(f"      ❌ Erreur élément LinkedIn: {e}")
                        
                        logging.info(f"   🎯 '{term}': {len(company_elements)} organisations traitées")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur terme {term}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur LinkedIn: {e}")
            self.stats['sources_failed'] += 1
    
    def source_5_wikipedia_associations(self):
        """SOURCE 5: Wikipedia - Listes d'associations"""
        logging.info("📖 SOURCE 5: Wikipedia - Listes associations")
        
        try:
            # Pages Wikipedia avec des listes d'associations
            wiki_pages = [
                "https://fr.wikipedia.org/wiki/Liste_d%27associations_françaises",
                "https://fr.wikipedia.org/wiki/Catégorie:Association_française",
                "https://fr.wikipedia.org/wiki/Catégorie:Organisation_non_gouvernementale_française",
                "https://fr.wikipedia.org/wiki/Catégorie:Association_loi_de_1901",
            ]
            
            for wiki_url in wiki_pages:
                try:
                    response = self.session.get(wiki_url, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Chercher tous les liens vers des articles d'associations
                        wiki_links = soup.find_all('a', href=True)
                        
                        for link in wiki_links:
                            href = link['href']
                            text = link.get_text(strip=True)
                            
                            if href.startswith('/wiki/') and text and len(text) > 3:
                                # Filtrer les liens qui ressemblent à des associations
                                if any(keyword in text.lower() for keyword in ['association', 'federation', 'union', 'club', 'société', 'fondation']):
                                    rna = f"WIKI{hash(text) % 1000000000:09d}"
                                    
                                    self.add_association(
                                        rna=rna,
                                        nom=text,
                                        source='wikipedia',
                                        url=f"https://fr.wikipedia.org{href}"
                                    )
                        
                        logging.info(f"   🎯 Page Wikipedia: {len(wiki_links)} liens traités")
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur page {wiki_url}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Wikipedia: {e}")
            self.stats['sources_failed'] += 1
    
    def source_6_guidestar_associations(self):
        """SOURCE 6: Guidestar/Candid - Base de données d'associations"""
        logging.info("🌟 SOURCE 6: Guidestar - Base associations")
        
        try:
            # Guidestar France (si disponible)
            search_url = "https://www.guidestar.org/search"
            
            search_terms = ['france association', 'french nonprofit', 'association française']
            
            for term in search_terms:
                try:
                    params = {
                        'q': term,
                        'country': 'FR'
                    }
                    
                    response = self.session.get(search_url, params=params, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Chercher les résultats d'organisations
                        org_elements = soup.find_all(class_=lambda x: x and any(
                            keyword in str(x).lower() for keyword in ['organization', 'nonprofit', 'result']
                        ))
                        
                        for element in org_elements:
                            try:
                                name_elem = element.find('a') or element.find('h3') or element.find('h2')
                                if name_elem:
                                    nom = name_elem.get_text(strip=True)
                                    if nom and len(nom) > 3:
                                        rna = f"GS{hash(nom) % 1000000000:09d}"
                                        
                                        self.add_association(
                                            rna=rna,
                                            nom=nom,
                                            source='guidestar',
                                            search_term=term
                                        )
                            
                            except Exception as e:
                                logging.debug(f"      ❌ Erreur élément Guidestar: {e}")
                        
                        logging.info(f"   🎯 '{term}': {len(org_elements)} organisations traitées")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logging.debug(f"   ❌ Erreur terme {term}: {e}")
            
            self.stats['sources_success'] += 1
            
        except Exception as e:
            logging.error(f"   ❌ Erreur Guidestar: {e}")
            self.stats['sources_failed'] += 1
    
    def run_ultra_mega_scraping(self):
        """Lance l'ultra mega scraping en parallèle"""
        logging.info("🚀 ULTRA MEGA SCRAPING - ENCORE PLUS D'ASSOCIATIONS")
        
        sources = [
            ("Yelp", self.source_1_yelp_associations),
            ("Google Maps", self.source_2_google_maps_associations),
            ("Facebook", self.source_3_facebook_associations),
            ("LinkedIn", self.source_4_linkedin_associations),
            ("Wikipedia", self.source_5_wikipedia_associations),
            ("Guidestar", self.source_6_guidestar_associations),
        ]
        
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_source = {executor.submit(source_func): source_name 
                              for source_name, source_func in sources}
            
            for future in as_completed(future_to_source):
                source_name = future_to_source[future]
                try:
                    future.result()
                    logging.info(f"✅ {source_name} terminé")
                except Exception as e:
                    logging.error(f"❌ {source_name} échoué: {e}")
        
        return self.stats
    
    def save_ultra_mega_results(self):
        """Sauvegarde tous les résultats ultra mega"""
        associations_list = list(self.all_associations.values())
        
        # JSON complet
        ultra_results = {
            'metadata': {
                'total_associations': len(associations_list),
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'sources_used': 'ULTRA_MEGA_ALL_SOURCES',
                'stats': self.stats
            },
            'associations': associations_list
        }
        
        with open('ultra_mega_all_associations.json', 'w', encoding='utf-8') as f:
            json.dump(ultra_results, f, ensure_ascii=False, indent=2)
        
        # CSV
        with open('ultra_mega_all_associations.csv', 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['rna', 'nom', 'source', 'ville', 'url', 'search_term']
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            for assoc in associations_list:
                writer.writerow(assoc)
        
        logging.info("💾 Ultra mega résultats sauvegardés:")
        logging.info("   📄 ultra_mega_all_associations.json")
        logging.info("   📊 ultra_mega_all_associations.csv")

def main():
    """
    ULTRA MEGA SCRAPER principal
    """
    print("🚀 ULTRA MEGA SCRAPER - ENCORE PLUS D'ASSOCIATIONS")
    print("=" * 70)
    print("Ce scraper va ajouter ENCORE PLUS d'associations aux 100k déjà récupérées:")
    print("1. Yelp - Associations et clubs")
    print("2. Google Maps - Recherche géographique")
    print("3. Facebook - Pages d'associations")
    print("4. LinkedIn - Organisations")
    print("5. Wikipedia - Listes d'associations")
    print("6. Guidestar - Base de données")
    print()
    print("🎯 OBJECTIF: Dépasser les 150,000 associations !")
    print("⏱️ DURÉE ESTIMÉE: 45-90 minutes")
    print()
    
    response = input("Voulez-vous lancer l'ULTRA MEGA SCRAPER? (o/n): ").lower().strip()
    if response != 'o':
        print("Scraping annulé.")
        return
    
    scraper = UltraMegaScraperAll()
    
    try:
        start_time = time.time()
        
        # Ultra mega scraping
        stats = scraper.run_ultra_mega_scraping()
        
        # Sauvegarder
        scraper.save_ultra_mega_results()
        
        # Afficher le résumé final
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 ULTRA MEGA SCRAPING TERMINÉ!")
        print(f"   ⏱️ Durée: {duration/60:.1f} minutes")
        print(f"   🏢 Total associations: {stats['total_found']}")
        print(f"   ✅ Sources réussies: {stats['sources_success']}")
        print(f"   ❌ Sources échouées: {stats['sources_failed']}")
        
        print(f"\n📊 NOUVELLES ASSOCIATIONS PAR SOURCE:")
        for source, count in stats['by_source'].items():
            print(f"   📊 {source}: {count} associations")
        
        if scraper.all_associations:
            print(f"\n📋 EXEMPLES DE NOUVELLES ASSOCIATIONS:")
            new_associations = [a for a in scraper.all_associations.values() if a.get('source') != 'data.gouv.fr']
            for i, assoc in enumerate(new_associations[:10]):
                print(f"   {i+1}. {assoc['nom'][:50]}...")
                print(f"      Source: {assoc['source']}")
        
        print(f"\n💾 Résultats ultra mega sauvegardés")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
