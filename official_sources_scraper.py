import requests
import json
import csv
import time
import logging
from datetime import datetime
import zipfile
import os

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('official_sources.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OfficialAssociationsScraper:
    def __init__(self):
        """
        Scraper utilisant les sources officielles françaises
        """
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # URLs des sources officielles
        self.sources = {
            'data_gouv_rna': 'https://www.data.gouv.fr/api/1/datasets/repertoire-national-des-associations/',
            'data_gouv_search': 'https://www.data.gouv.fr/api/1/datasets/search/',
            'sirene_api': 'https://api.insee.fr/entreprises/sirene/V3/',
            'journal_officiel': 'https://www.journal-officiel.gouv.fr/pages/associations-search/'
        }
        
    def get_rna_official_data(self):
        """
        Récupère les données officielles du RNA depuis data.gouv.fr
        """
        logging.info("🏛️ RÉCUPÉRATION DES DONNÉES OFFICIELLES RNA")
        
        try:
            # Rechercher les datasets d'associations
            search_url = "https://www.data.gouv.fr/api/1/datasets/search/"
            params = {
                'q': 'associations RNA répertoire national',
                'page_size': 20
            }
            
            response = self.session.get(search_url, params=params)
            response.raise_for_status()
            
            search_results = response.json()
            
            logging.info(f"📊 {search_results.get('total', 0)} datasets trouvés")
            
            # Analyser les datasets
            datasets = []
            for dataset in search_results.get('data', []):
                dataset_info = {
                    'id': dataset.get('id'),
                    'title': dataset.get('title'),
                    'description': dataset.get('description', '')[:200],
                    'url': dataset.get('page'),
                    'resources_count': len(dataset.get('resources', [])),
                    'resources': []
                }
                
                # Analyser les ressources (fichiers)
                for resource in dataset.get('resources', []):
                    if any(keyword in resource.get('title', '').lower() for keyword in ['rna', 'association', 'répertoire']):
                        resource_info = {
                            'title': resource.get('title'),
                            'format': resource.get('format'),
                            'url': resource.get('url'),
                            'filesize': resource.get('filesize'),
                            'description': resource.get('description', '')[:100]
                        }
                        dataset_info['resources'].append(resource_info)
                
                if dataset_info['resources']:
                    datasets.append(dataset_info)
                    logging.info(f"   📄 {dataset_info['title']}")
                    logging.info(f"      📊 {len(dataset_info['resources'])} ressources")
            
            return datasets
            
        except Exception as e:
            logging.error(f"❌ Erreur récupération RNA: {e}")
            return []
    
    def download_official_dataset(self, resource_url, filename):
        """
        Télécharge un dataset officiel
        """
        logging.info(f"⬇️ Téléchargement: {filename}")
        
        try:
            response = self.session.get(resource_url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filename, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            if downloaded % (1024 * 1024) == 0:  # Afficher tous les MB
                                logging.info(f"   📊 {progress:.1f}% ({downloaded // (1024*1024)}MB/{total_size // (1024*1024)}MB)")
            
            logging.info(f"   ✅ Téléchargé: {filename} ({downloaded // (1024*1024)}MB)")
            return filename
            
        except Exception as e:
            logging.error(f"   ❌ Erreur téléchargement: {e}")
            return None
    
    def extract_and_process_dataset(self, filename):
        """
        Extrait et traite un dataset téléchargé
        """
        logging.info(f"📊 Traitement: {filename}")
        
        try:
            associations = []
            
            # Si c'est un ZIP, l'extraire
            if filename.endswith('.zip'):
                with zipfile.ZipFile(filename, 'r') as zip_ref:
                    zip_ref.extractall('extracted_data')
                    extracted_files = zip_ref.namelist()
                    logging.info(f"   📦 {len(extracted_files)} fichiers extraits")
                    
                    # Traiter les fichiers CSV
                    for extracted_file in extracted_files:
                        if extracted_file.endswith('.csv'):
                            csv_path = os.path.join('extracted_data', extracted_file)
                            associations.extend(self.process_csv_file(csv_path))
            
            # Si c'est directement un CSV
            elif filename.endswith('.csv'):
                associations = self.process_csv_file(filename)
            
            logging.info(f"   📊 {len(associations)} associations extraites")
            return associations
            
        except Exception as e:
            logging.error(f"   ❌ Erreur traitement: {e}")
            return []
    
    def process_csv_file(self, csv_path):
        """
        Traite un fichier CSV d'associations
        """
        logging.info(f"📄 Lecture CSV: {csv_path}")
        
        associations = []
        
        try:
            # Essayer différents encodages
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    with open(csv_path, 'r', encoding=encoding) as f:
                        # Détecter le délimiteur
                        sample = f.read(1024)
                        f.seek(0)
                        
                        delimiter = ';' if ';' in sample else ','
                        
                        reader = csv.DictReader(f, delimiter=delimiter)
                        
                        for i, row in enumerate(reader):
                            if i >= 100000:  # Limiter pour les tests
                                break
                                
                            # Extraire les informations d'association
                            association = self.extract_association_from_row(row)
                            if association:
                                associations.append(association)
                            
                            if i % 10000 == 0 and i > 0:
                                logging.info(f"      📊 {i} lignes traitées...")
                    
                    logging.info(f"   ✅ CSV lu avec encodage {encoding}")
                    break
                    
                except UnicodeDecodeError:
                    continue
            
            logging.info(f"   📊 {len(associations)} associations valides extraites")
            return associations
            
        except Exception as e:
            logging.error(f"   ❌ Erreur lecture CSV: {e}")
            return []
    
    def extract_association_from_row(self, row):
        """
        Extrait les informations d'association depuis une ligne CSV
        """
        try:
            # Mapping des colonnes possibles
            column_mappings = {
                'rna': ['rna', 'id_rna', 'numero_rna', 'waldec'],
                'nom': ['nom', 'titre', 'denomination', 'libelle'],
                'siren': ['siren', 'id_siren', 'numero_siren'],
                'siret': ['siret', 'id_siret', 'numero_siret'],
                'adresse': ['adresse', 'adresse_siege', 'adresse_gestion'],
                'ville': ['ville', 'commune', 'localite'],
                'code_postal': ['code_postal', 'cp', 'postal_code'],
                'departement': ['departement', 'dept', 'dep'],
                'telephone': ['telephone', 'tel', 'phone'],
                'email': ['email', 'mail', 'courriel'],
                'site_web': ['site_web', 'website', 'url', 'site'],
                'objet': ['objet', 'objet_social', 'activite', 'description']
            }
            
            association = {}
            
            # Extraire chaque champ
            for field, possible_columns in column_mappings.items():
                value = None
                for col in possible_columns:
                    for key in row.keys():
                        if col.lower() in key.lower():
                            value = row[key]
                            break
                    if value:
                        break
                
                association[field] = value.strip() if value else ''
            
            # Vérifier qu'on a au moins un RNA ou nom
            if association.get('rna') or association.get('nom'):
                return association
            
            return None
            
        except Exception as e:
            logging.debug(f"Erreur extraction ligne: {e}")
            return None
    
    def search_sirene_api(self, siren_list):
        """
        Enrichit les données avec l'API Sirene
        """
        logging.info("🏢 ENRICHISSEMENT AVEC API SIRENE")
        
        # Note: L'API Sirene nécessite une clé d'API
        # Voir: https://api.insee.fr/catalogue/
        
        logging.info("💡 Pour utiliser l'API Sirene, vous devez:")
        logging.info("   1. Créer un compte sur https://api.insee.fr/")
        logging.info("   2. Obtenir une clé d'API")
        logging.info("   3. Ajouter la clé dans ce script")
        
        return []
    
    def save_all_associations(self, associations, filename_base='associations_officielles'):
        """
        Sauvegarde toutes les associations
        """
        if not associations:
            logging.warning("⚠️ Aucune association à sauvegarder")
            return
        
        # JSON complet
        json_file = f"{filename_base}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_associations': len(associations),
                    'extraction_date': datetime.now().isoformat(),
                    'source': 'Sources officielles françaises'
                },
                'associations': associations
            }, f, ensure_ascii=False, indent=2)
        
        # CSV pour Excel
        csv_file = f"{filename_base}.csv"
        if associations:
            fieldnames = list(associations[0].keys())
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for assoc in associations:
                    writer.writerow(assoc)
        
        # Statistiques
        with_rna = sum(1 for a in associations if a.get('rna'))
        with_siren = sum(1 for a in associations if a.get('siren'))
        with_email = sum(1 for a in associations if a.get('email'))
        with_phone = sum(1 for a in associations if a.get('telephone'))
        
        logging.info(f"💾 Sauvegardé:")
        logging.info(f"   📄 {json_file} - Données complètes JSON")
        logging.info(f"   📊 {csv_file} - Format Excel/CSV")
        logging.info(f"   📊 Statistiques:")
        logging.info(f"      🏢 Total associations: {len(associations)}")
        logging.info(f"      🆔 Avec RNA: {with_rna}")
        logging.info(f"      🏢 Avec SIREN: {with_siren}")
        logging.info(f"      📧 Avec email: {with_email}")
        logging.info(f"      📞 Avec téléphone: {with_phone}")
    
    def extract_all_official_associations(self):
        """
        Extraction complète depuis les sources officielles
        """
        logging.info("🏛️ EXTRACTION COMPLÈTE - SOURCES OFFICIELLES")
        
        all_associations = []
        
        try:
            # 1. Récupérer les datasets officiels
            datasets = self.get_rna_official_data()
            
            if not datasets:
                logging.error("❌ Aucun dataset officiel trouvé")
                return []
            
            # 2. Télécharger et traiter les datasets les plus prometteurs
            for dataset in datasets[:3]:  # Traiter les 3 premiers
                logging.info(f"\n📊 Traitement dataset: {dataset['title']}")
                
                for resource in dataset['resources'][:2]:  # 2 ressources par dataset
                    if resource.get('url'):
                        # Déterminer le nom de fichier
                        filename = f"dataset_{len(all_associations)}_{resource['format']}.{resource['format'].lower()}"
                        
                        # Télécharger
                        downloaded_file = self.download_official_dataset(resource['url'], filename)
                        
                        if downloaded_file:
                            # Traiter
                            associations = self.extract_and_process_dataset(downloaded_file)
                            all_associations.extend(associations)
                            
                            logging.info(f"   📊 Total cumulé: {len(all_associations)} associations")
                            
                            # Nettoyer le fichier téléchargé
                            try:
                                os.remove(downloaded_file)
                            except:
                                pass
            
            # 3. Supprimer les doublons par RNA
            unique_associations = {}
            for assoc in all_associations:
                rna = assoc.get('rna')
                if rna and rna not in unique_associations:
                    unique_associations[rna] = assoc
                elif not rna:
                    # Pour les associations sans RNA, utiliser le nom
                    nom = assoc.get('nom', '')
                    if nom and nom not in unique_associations:
                        unique_associations[nom] = assoc
            
            final_associations = list(unique_associations.values())
            
            logging.info(f"\n🎉 EXTRACTION TERMINÉE!")
            logging.info(f"   📊 Associations brutes: {len(all_associations)}")
            logging.info(f"   📊 Associations uniques: {len(final_associations)}")
            
            return final_associations
            
        except Exception as e:
            logging.error(f"❌ Erreur extraction officielle: {e}")
            return all_associations

def main():
    """
    Extraction principale depuis les sources officielles
    """
    print("🏛️ EXTRACTION COMPLÈTE - SOURCES OFFICIELLES FRANÇAISES")
    print("=" * 70)
    print("Ce scraper utilise les sources officielles du gouvernement français:")
    print("- data.gouv.fr (Répertoire National des Associations)")
    print("- Datasets officiels RNA")
    print("- API Sirene (optionnel)")
    print()
    print("⚠️ ATTENTION: Les fichiers officiels peuvent être très volumineux!")
    print("   (Plusieurs centaines de MB à plusieurs GB)")
    print()
    
    response = input("Voulez-vous continuer? (o/n): ").lower().strip()
    if response != 'o':
        print("Extraction annulée.")
        return
    
    scraper = OfficialAssociationsScraper()
    
    try:
        # Extraction complète
        associations = scraper.extract_all_official_associations()
        
        if associations:
            # Sauvegarder
            scraper.save_all_associations(associations)
            
            # Afficher quelques exemples
            print(f"\n📋 EXEMPLES D'ASSOCIATIONS EXTRAITES:")
            for i, assoc in enumerate(associations[:10]):
                print(f"\n{i+1}. {assoc.get('nom', 'N/A')}")
                print(f"   🆔 RNA: {assoc.get('rna', 'N/A')}")
                print(f"   🏢 SIREN: {assoc.get('siren', 'N/A')}")
                print(f"   📍 Ville: {assoc.get('ville', 'N/A')}")
                print(f"   📧 Email: {assoc.get('email', 'N/A')}")
            
            print(f"\n🎉 SUCCÈS! {len(associations)} associations extraites depuis les sources officielles!")
            
        else:
            print("\n❌ Aucune association extraite")
            
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
